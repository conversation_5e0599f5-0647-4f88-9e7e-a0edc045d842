{"$schema": "./$schema.json", "general": {"ascending": "Възходящо", "descending": "Низходящо", "add": "Добави", "start": "Начало", "end": "<PERSON><PERSON><PERSON><PERSON>", "open": "Отвори", "close": "Затвори", "apply": "Прило<PERSON>и", "range": "Об<PERSON><PERSON><PERSON>т", "search": "Търсене", "of": "от", "results": "резултати", "pages": "страници", "next": "Следваща", "prev": "Преди<PERSON>на", "is": "е", "timeline": "Времева линия", "success": "Успех", "warning": "Предупреждение", "tip": "Съвет", "error": "Грешка", "select": "Избери", "selected": "Избрано", "enabled": "Акти<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled": "Деактивирано", "expired": "Изтекло", "active": "Активно", "revoked": "Отменено", "new": "Ново", "modified": "Променено", "added": "Добавено", "removed": "Премахнато", "admin": "Администратор", "store": "Мага<PERSON>ин", "details": "Детайли", "items_one": "{{count}} артикул", "items_other": "{{count}} артикули", "countSelected": "{{count}} избрани", "countOfTotalSelected": "{{count}} от {{total}} избрани", "plusCount": "+ {{count}}", "plusCountMore": "+ {{count}} още", "areYouSure": "Сигурни ли сте?", "noRecordsFound": "Няма намерени записи", "typeToConfirm": "Моля, въведете {val}, за да потвърдите:", "noResultsTitle": "Няма резултати", "noResultsMessage": "Опитайте да промените филтрите или заявката за търсене", "noSearchResults": "Няма резултати от търсенето", "noSearchResultsFor": "Няма резултати от търсенето за <0>'{{query}}'</0>", "noRecordsTitle": "Няма записи", "noRecordsMessage": "Няма записи за показване", "unsavedChangesTitle": "Сигурни ли сте, че искате да напуснете тази форма?", "unsavedChangesDescription": "Имате незапазени промени, които ще бъдат загубени, ако напуснете тази форма.", "includesTaxTooltip": "Цените в тази колона включват данък.", "excludesTaxTooltip": "Цените в тази колона не включват данък.", "noMoreData": "Няма повече данни", "actions": "Действия"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}} кл<PERSON>ч", "numberOfKeys_other": "{{count}} ключа", "drawer": {"header_one": "JSON <0>· {{count}} ключ</0>", "header_other": "JSON <0>· {{count}} ключа</0>", "description": "Вижте JSON данните за този обект."}}, "metadata": {"header": "Метаданни", "numberOfKeys_one": "{{count}} кл<PERSON>ч", "numberOfKeys_other": "{{count}} ключа", "edit": {"header": "Редактиране на метаданни", "description": "Редактирайте метаданните за този обект.", "successToast": "Метаданните бяха успешно актуализирани.", "actions": {"insertRowAbove": "Вмъкни ред отгоре", "insertRowBelow": "Вмъкни ред отдолу", "deleteRow": "Изтрий ред"}, "labels": {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "Стойност"}, "complexRow": {"label": "Някои редове са деактивирани", "description": "Този обект съдържа непримитивни метаданни, като масиви или обекти, които не могат да бъдат редактирани тук. За да редактирате деактивираните редове, използвайте директно API.", "tooltip": "Този ред е деактивиран, защото съдържа непримитивни данни."}}}, "validation": {"mustBeInt": "Стойността трябва да е цяло число.", "mustBePositive": "Стойността трябва да е положително число."}, "actions": {"save": "Запази", "saveAsDraft": "Запази като чернова", "copy": "Коп<PERSON><PERSON><PERSON><PERSON>", "copied": "Копирано", "duplicate": "<PERSON>у<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "publish": "Публикувай", "create": "Създай", "delete": "Изтрий", "remove": "Премахни", "revoke": "Отмени", "cancel": "Отказ", "forceConfirm": "Принудително потвърждение", "continueEdit": "Продължи редакцията", "enable": "Ак<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disable": "Деа<PERSON><PERSON>ив<PERSON><PERSON><PERSON><PERSON>", "undo": "Отмени", "complete": "Завър<PERSON>и", "viewDetails": "Виж детайли", "back": "Назад", "close": "Затвори", "showMore": "Покажи повече", "continue": "Продължи", "continueWithEmail": "Продължи с имейл", "idCopiedToClipboard": "ID е копирано в клипборда", "addReason": "Добави причина", "addNote": "Добави бележка", "reset": "Нулир<PERSON><PERSON>", "confirm": "Потвърди", "edit": "Редак<PERSON><PERSON><PERSON><PERSON><PERSON>", "addItems": "Добави елементи", "download": "Изтегли", "clear": "Изчисти", "clearAll": "Изчисти всичко", "apply": "Прило<PERSON>и", "add": "Добави", "select": "Избери", "browse": "Прегледай", "logout": "Изход", "hide": "Скрий", "export": "Експортир<PERSON>й", "import": "Импорти<PERSON><PERSON>й", "cannotUndo": "Това действие не може да бъде отменено"}, "operators": {"in": "В"}, "app": {"search": {"label": "Търсене", "title": "Търсене", "description": "Търсете в целия магазин, включително поръчки, продукти, клиенти и други.", "allAreas": "Всички области", "navigation": "Навигация", "openResult": "Отвори резултат", "showMore": "Покажи повече", "placeholder": "Премини или намери нещо...", "noResultsTitle": "Няма намерени резултати", "noResultsMessage": "Не успяхме да намерим нищо, което съвпада с вашето търсене.", "emptySearchTitle": "Въведете за търсене", "emptySearchMessage": "Въведете ключова дума или фраза за търсене.", "loadMore": "Зареди още {{count}}", "groups": {"all": "Всички области", "customer": "Клиенти", "customerGroup": "Групи клиенти", "product": "Продукти", "productVariant": "Варианти на продукти", "inventory": "Склад", "reservation": "Резервации", "category": "Категории", "collection": "Колекции", "order": "Поръчки", "promotion": "Промоции", "campaign": "Кампании", "priceList": "Ценови листи", "user": "Потребители", "region": "Региони", "taxRegion": "Данъчни региони", "returnReason": "Причини за връщане", "salesChannel": "Канали за продажба", "productType": "Типове продукти", "productTag": "Етикети на продукти", "location": "Локации", "shippingProfile": "Профили за доставка", "publishableApiKey": "Публични API ключове", "secretApiKey": "Тайни API ключове", "command": "Команди", "navigation": "Навигация"}}, "keyboardShortcuts": {"pageShortcut": "Премини към", "settingShortcut": "Настройки", "commandShortcut": "Команди", "then": "след това", "navigation": {"goToOrders": "Поръчки", "goToProducts": "Продукти", "goToCollections": "Колекции", "goToCategories": "Категории", "goToCustomers": "Клиенти", "goToCustomerGroups": "Групи клиенти", "goToInventory": "Склад", "goToReservations": "Резервации", "goToPriceLists": "Ценови листи", "goToPromotions": "Промоции", "goToCampaigns": "Кампании"}, "settings": {"goToSettings": "Настройки", "goToStore": "Мага<PERSON>ин", "goToUsers": "Потребители", "goToRegions": "Региони", "goToTaxRegions": "Данъчни региони", "goToSalesChannels": "Канали за продажба", "goToProductTypes": "Типове продукти", "goToLocations": "Локации", "goToPublishableApiKeys": "Публични API ключове", "goToSecretApiKeys": "Тайни API ключове", "goToWorkflows": "Работни процеси", "goToProfile": "Профил", "goToReturnReasons": "Причини за връщане"}}, "menus": {"user": {"documentation": "Документация", "changelog": "История на промените", "shortcuts": "Бързи команди", "profileSettings": "Настройки на профила", "theme": {"label": "Тема", "dark": "Тъмна", "light": "Светла", "system": "Системна"}}, "store": {"label": "Мага<PERSON>ин", "storeSettings": "Настройки на магазина"}, "actions": {"logout": "Изход"}}, "nav": {"accessibility": {"title": "Навигация", "description": "Навигационно меню за таблото."}, "common": {"extensions": "Разширения"}, "main": {"store": "Мага<PERSON>ин", "storeSettings": "Настройки на магазина"}, "settings": {"header": "Настройки", "general": "Об<PERSON>и", "developer": "Разработчик", "myAccount": "Моят акаунт"}}}, "dataGrid": {"columns": {"view": "Преглед", "resetToDefault": "Възстанови по подразбиране", "disabled": "Промяната на видимите колони е деактивирана."}, "shortcuts": {"label": "Кратки команди", "commands": {"undo": "Отмени", "redo": "Повтори", "copy": "Коп<PERSON><PERSON><PERSON><PERSON>", "paste": "Постави", "edit": "Редак<PERSON><PERSON><PERSON><PERSON><PERSON>", "delete": "Изтрий", "clear": "Изчисти", "moveUp": "Премести нагоре", "moveDown": "Премести надолу", "moveLeft": "Премести наляво", "moveRight": "Премести надясно", "moveTop": "Премести най-горе", "moveBottom": "Премести най-долу", "selectDown": "Избери надолу", "selectUp": "Избери нагоре", "selectColumnDown": "Избери колона надолу", "selectColumnUp": "Избери колона нагоре", "focusToolbar": "Фокус върху лентата с инструменти", "focusCancel": "Фокус върху отмяна"}}, "errors": {"fixError": "Поправи грешката", "count_one": "{{count}} грешка", "count_other": "{{count}} грешки"}}, "filters": {"sortLabel": "Сортиране", "filterLabel": "<PERSON>и<PERSON><PERSON><PERSON>р", "searchLabel": "Търсене", "date": {"today": "<PERSON><PERSON><PERSON><PERSON>", "lastSevenDays": "Последните 7 дни", "lastThirtyDays": "Последните 30 дни", "lastNinetyDays": "Последните 90 дни", "lastTwelveMonths": "Последните 12 месеца", "custom": "Персонализ<PERSON><PERSON><PERSON>н", "from": "От", "to": "До", "starting": "Започва от", "ending": "Завършва на"}, "compare": {"lessThan": "По-малко от", "greaterThan": "Повече от", "exact": "Точно", "range": "Об<PERSON><PERSON><PERSON>т", "lessThanLabel": "по-малко от {{value}}", "greaterThanLabel": "повече от {{value}}", "andLabel": "и"}, "sorting": {"alphabeticallyAsc": "А до Я", "alphabeticallyDesc": "Я до А", "dateAsc": "Най-нови първо", "dateDesc": "Най-стари първо"}, "radio": {"yes": "Да", "no": "Не", "true": "Истина", "false": "Лъжа"}, "addFilter": "Добави филтър"}, "errorBoundary": {"badRequestTitle": "400 - Нева<PERSON><PERSON><PERSON>на заявка", "badRequestMessage": "Заявката не може да бъде обработена поради неправилен синтаксис.", "notFoundTitle": "404 - Страницата не съществува", "notFoundMessage": "Проверете URL адреса и опитайте отново или използвайте лентата за търсене.", "internalServerErrorTitle": "500 - Вътрешна грешка на сървъра", "internalServerErrorMessage": "Възникна неочаквана грешка на сървъра. Опитайте по-късно.", "defaultTitle": "Възникна грешка", "defaultMessage": "Възникна неочаквана грешка при зареждането на страницата.", "noMatchMessage": "Търсената страница не съществува.", "backToDashboard": "Назад към таблото"}, "addresses": {"title": "Адреси", "shippingAddress": {"header": "Адрес за доставка", "editHeader": "Редактиране на адрес за доставка", "editLabel": "Адрес за доставка", "label": "Адрес за доставка"}, "billingAddress": {"header": "Адрес за фактуриране", "editHeader": "Редактиране на адрес за фактуриране", "editLabel": "Адрес за фактуриране", "label": "Адрес за фактуриране", "sameAsShipping": "Същият като адреса за доставка"}, "contactHeading": "Кон<PERSON><PERSON><PERSON>т", "locationHeading": "Местоположение"}, "email": {"editHeader": "Редактиране на имейл", "editLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "transferOwnership": {"header": "Прехвърляне на собственост", "label": "Прехвърли собственост", "details": {"order": "Детайли за поръчката", "draft": "Детайли за черновата"}, "currentOwner": {"label": "Текущ собственик", "hint": "Текущият собственик на поръчката."}, "newOwner": {"label": "Нов собственик", "hint": "Новият собственик, на когото ще се прехвърли поръчката."}, "validation": {"mustBeDifferent": "Новият собственик трябва да бъде различен от текущия собственик.", "required": "Нов собственик е задължителен."}}, "sales_channels": {"availableIn": "Налично в <0>{{x}}</0> от <1>{{y}}</1> канали за продажба"}, "products": {"domain": "Продукти", "list": {"noRecordsMessage": "Създайте първия си продукт, за да започнете да продавате."}, "edit": {"header": "Редактиране на продукт", "description": "Редактирайте детайлите на продукта.", "successToast": "Продуктът {{title}} беше успешно актуализиран."}, "create": {"title": "Създаване на продукт", "description": "Създайте нов продукт.", "header": "Об<PERSON>и", "tabs": {"details": "Детайли", "organize": "Организиране", "variants": "Варианти", "inventory": "Инвентарни комплекти"}, "errors": {"variants": "Мо<PERSON><PERSON>, изберете поне един вариант.", "options": "Моля, създайте поне една опция.", "uniqueSku": "SKU трябва да е уникален."}, "inventory": {"heading": "Инвентарни комплекти", "label": "Добавете инвентарни елементи към комплекта на варианта.", "itemPlaceholder": "Изберете инвентарен елемент", "quantityPlaceholder": "Колко от тези са необходими за комплекта?"}, "variants": {"header": "Варианти", "subHeadingTitle": "Да, това е продукт с варианти", "subHeadingDescription": "Когато не е избрано, ще създадем вариант по подразбиране за вас", "optionTitle": {"placeholder": "Размер"}, "optionValues": {"placeholder": "М<PERSON><PERSON><PERSON><PERSON>, Среден, Голям"}, "productVariants": {"label": "Варианти на продукта", "hint": "Тази подредба ще определи реда на вариантите във вашия магазин.", "alert": "Добавете опции, за да създадете варианти.", "tip": "Непроверените варианти няма да бъдат създадени. Винаги можете да създавате и редактирате варианти по-късно."}, "productOptions": {"label": "Опции на продукта", "hint": "Определете опциите за продукта, напр. цвят, размер и др."}}, "successToast": "Продуктът {{title}} беше успешно създаден."}, "export": {"header": "Експортиране на списък с продукти", "description": "Експортирайте списъка с продукти в CSV файл.", "success": {"title": "Обработваме вашия експортиране", "description": "Експортирането на данни може да отнеме няколко минути. Ще ви уведомим, когато приключим."}, "filters": {"title": "Филтри", "description": "Приложете филтри в таблицата, за да коригирате изгледа"}, "columns": {"title": "Колони", "description": "Персонализирайте експортираните данни според специфичните нужди"}}, "import": {"header": "Импортиране на списък с продукти", "uploadLabel": "Импортиране на продукти", "uploadHint": "Плъзнете и пуснете CSV файл или кликнете, за да качите", "description": "Импортирайте продукти чрез предоставяне на CSV файл в предварително дефиниран формат", "template": {"title": "Не сте сигурни как да подредите списъка си?", "description": "Изтеглете шаблона по-долу, за да сте сигурни, че следвате правилния формат."}, "upload": {"title": "Качете CSV файл", "description": "Чрез импортиране можете да добавяте или актуализирате продукти. За да актуализирате съществуващи продукти, трябва да използвате съществуващия идентификатор и ръкохватка. За да актуализирате съществуващи варианти, трябва да използвате съществуващия идентификатор. Ще бъдете помолени за потвърждение преди импортирането.", "preprocessing": "Предварителна обработка...", "productsToCreate": "Продуктите ще бъдат създадени", "productsToUpdate": "Продуктите ще бъдат актуализирани"}, "success": {"title": "Обработваме вашия импорт", "description": "Импортирането на данни може да отнеме известно време. Ще ви уведомим, когато приключим."}}, "deleteWarning": "Ще изтриете продукта {{title}}. Това действие не може да бъде отменено.", "variants": {"header": "Варианти", "empty": {"heading": "Няма варианти", "description": "Няма налични варианти за показване."}, "filtered": {"heading": "Няма резултати", "description": "Няма варианти, които отговарят на текущите филтри."}}, "attributes": "Атрибути", "editAttributes": "Редактиране на атрибути", "editOptions": "Редактиране на опции", "editPrices": "Редактиране на цени", "media": {"label": "Медия", "editHint": "Добавете медия към продукта, за да го покажете във вашия магазин.", "makeThumbnail": "Създай миниатюра", "uploadImagesLabel": "Качете изображения", "uploadImagesHint": "Плъзнете и пуснете изображения тук или кликнете, за да качите.", "invalidFileType": "'{{name}}' не е поддържан тип файл. Поддържани файлови типове са: {{types}}.", "failedToUpload": "Неуспешно качване на медията. Моля, опитайте отново.", "deleteWarning_one": "Ще изтриете {{count}} изображение. Това действие не може да бъде отменено.", "deleteWarning_other": "Ще изтриете {{count}} изображения. Това действие не може да бъде отменено.", "deleteWarningWithThumbnail_one": "Ще изтриете {{count}} изображение заедно с миниатюрата. Това действие не може да бъде отменено.", "deleteWarningWithThumbnail_other": "Ще изтриете {{count}} изображения заедно с миниатюрата. Това действие не може да бъде отменено.", "thumbnailTooltip": "Миниатюра", "galleryLabel": "Галерия", "downloadImageLabel": "Изтеглете текущото изображение", "deleteImageLabel": "Изтрийте текущото изображение", "emptyState": {"header": "Все още няма медия", "description": "Добавете медия към продукта, за да го покажете във вашия магазин.", "action": "Добави медия"}, "successToast": "Медията беше успешно актуализирана."}, "discountableHint": "Когато не е отметнато, отстъпките няма да се прилагат за този продукт.", "noSalesChannels": "Не е наличен в нито един канал за продажба", "variantCount_one": "{{count}} вариант", "variantCount_other": "{{count}} варианта", "deleteVariantWarning": "Ще изтриете варианта {{title}}. Това действие не може да бъде отменено.", "productStatus": {"draft": "Чернова", "published": "Публикуван", "proposed": "Предложен", "rejected": "От<PERSON><PERSON><PERSON>рлен"}, "fields": {"title": {"label": "Заглавие", "hint": "Дайте на продукта кратко и ясно заглавие.<0/>Препоръчителната дължина е 50-60 знака за търсачки.", "placeholder": "Зимно яке"}, "subtitle": {"label": "Подзаглавие", "placeholder": "Топло и уютно"}, "handle": {"label": "Идентификатор", "tooltip": "Идентификаторът се използва за препратка към продукта във вашия магазин. Ако не е зададен, ще се генерира от заглавието на продукта.", "placeholder": "зимно-яке"}, "description": {"label": "Описание", "hint": "Дайте кратко и ясно описание на продукта.<0/>Препоръчителната дължина е 120-160 знака за търсачки.", "placeholder": "Топло и уютно яке"}, "discountable": {"label": "С отстъпка", "hint": "Когато не е отметнато, отстъпките няма да се прилагат за този продукт"}, "shipping_profile": {"label": "Профил за доставка", "hint": "Свържете продукта с профил за доставка"}, "type": {"label": "Тип"}, "collection": {"label": "Колекция"}, "categories": {"label": "Категории"}, "tags": {"label": "Етикети"}, "sales_channels": {"label": "Канали за продажба", "hint": "Продуктът ще бъде наличен само в канала за продажба по подразбиране, ако не бъде избран друг."}, "countryOrigin": {"label": "Държава на произход"}, "material": {"label": "Материал"}, "width": {"label": "Ши<PERSON><PERSON><PERSON>"}, "length": {"label": "Дъл<PERSON><PERSON>на"}, "height": {"label": "Висо<PERSON>ина"}, "weight": {"label": "Тегло"}, "options": {"label": "Опции на продукта", "hint": "Опциите се използват за определяне на цвят, размер и др.", "add": "Добави опция", "optionTitle": "Заглавие на опцията", "optionTitlePlaceholder": "Цвят", "variations": "Вариации (разделени със запетая)", "variantionsPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "variants": {"label": "Варианти на продукта", "hint": "Непроверените варианти няма да бъдат създадени. Тази подредба ще определи реда на вариантите във вашия магазин."}, "mid_code": {"label": "MID код"}, "hs_code": {"label": "HS код"}}, "variant": {"edit": {"header": "Редактиране на вариант", "success": "Вариантът на продукта беше успешно редактиран"}, "create": {"header": "Детайли за варианта"}, "deleteWarning": "Сигурни ли сте, че искате да изтриете този вариант?", "pricesPagination": "1 - {{current}} от {{total}} цени", "tableItemAvailable": "{{availableCount}} налични", "tableItem_one": "{{availableCount}} наличен на {{locationCount}} място", "tableItem_other": "{{availableCount}} налични на {{locationCount}} места", "inventory": {"notManaged": "Не се управлява", "manageItems": "Управление на инвентарни елементи", "notManagedDesc": "Инвентарът не се управлява за този вариант. Включете „Управление на инвентар“ за проследяване.", "manageKit": "Управление на инвентарен комплект", "navigateToItem": "Към инвентарния елемент", "actions": {"inventoryItems": "Към инвентарния елемент", "inventoryKit": "Показване на инвентарните елементи"}, "inventoryKit": "Инвента<PERSON><PERSON>н комплект", "inventoryKitHint": "Състои ли се този вариант от няколко инвентарни елемента?", "validation": {"itemId": "Моля, изберете инвентарен елемент.", "quantity": "Полето за количество е задължително. Въведете положително число."}, "header": "Наличност и инвентар", "editItemDetails": "Редактиране на детайли за елемента", "manageInventoryLabel": "Управление на инвентар", "manageInventoryHint": "Когато е активирано, количеството на инвентара ще се актуализира при поръчки и връщания.", "allowBackordersLabel": "Позволи поръчки при липса", "allowBackordersHint": "Когато е активирано, клиентите могат да поръчват дори при липса на наличност.", "toast": {"levelsBatch": "Инвентарните нива са актуализирани.", "update": "Инвентарният елемент беше успешно актуализиран.", "updateLevel": "Нивото на инвентара беше успешно актуализирано.", "itemsManageSuccess": "Инвентарните елементи бяха успешно актуализирани."}}}, "options": {"header": "Опции", "edit": {"header": "Редактиране на опция", "successToast": "Опцията {{title}} беше успешно актуализирана."}, "create": {"header": "Създаване на опция", "successToast": "Опцията {{title}} беше успешно създадена."}, "deleteWarning": "Ще изтриете опцията на продукта: {{title}}. Това действие не може да бъде отменено."}, "organization": {"header": "Организиране", "edit": {"header": "Редактиране на организация", "toasts": {"success": "Успешно актуализирана организация за {{title}}."}}}, "stock": {"heading": "Управление на нива на наличност и местоположения", "description": "Актуализирайте нивата на наличния инвентар за всички варианти на продукта.", "loading": "Моля, изчакайте, това може да отнеме малко време...", "tooltips": {"alreadyManaged": "Този инвентарен елемент вече е редактиран под {{title}}.", "alreadyManagedWithSku": "Този инвентарен елемент вече е редактиран под {{title}} ({{sku}})."}}, "shippingProfile": {"header": "Конфигурация на доставка", "edit": {"header": "Редактиране на конфигурацията за доставка", "toasts": {"success": "Успешно актуализиран профил за доставка за {{title}}."}}, "create": {"errors": {"required": "Профил за доставка е задължителен"}}}, "toasts": {"delete": {"success": {"header": "Продуктът е изтрит", "description": "{{title}} беше успешно изтрит."}, "error": {"header": "Неуспешно изтриване на продукта"}}}}, "collections": {"domain": "Колекции", "subtitle": "Организирайте продуктите в колекции.", "createCollection": "Създаване на колекция", "createCollectionHint": "Създайте нова колекция, за да организирате вашите продукти.", "createSuccess": "Колекцията беше успешно създадена.", "editCollection": "Редактиране на колекция", "handleTooltip": "Идентификаторът се използва за препратка към колекцията във вашия магазин. Ако не е зададен, ще бъде генериран от заглавието на колекцията.", "deleteWarning": "Ще изтриете колекцията {{title}}. Това действие не може да бъде отменено.", "removeSingleProductWarning": "Ще премахнете продукта {{title}} от колекцията. Това действие не може да бъде отменено.", "removeProductsWarning_one": "Ще премахнете {{count}} продукт от колекцията. Това действие не може да бъде отменено.", "removeProductsWarning_other": "Ще премахнете {{count}} продукта от колекцията. Това действие не може да бъде отменено.", "products": {"list": {"noRecordsMessage": "Няма продукти в колекцията."}, "add": {"successToast_one": "Продуктът беше успешно добавен към колекцията.", "successToast_other": "Продуктите бяха успешно добавени към колекцията."}, "remove": {"successToast_one": "Продуктът беше успешно премахнат от колекцията.", "successToast_other": "Продуктите бяха успешно премахнати от колекцията."}}}, "categories": {"domain": "Категории", "subtitle": "Организирайте продуктите в категории и управлявайте тяхното подреждане и йерархия.", "create": {"header": "Създаване на категория", "hint": "Създайте нова категория, за да организирате вашите продукти.", "tabs": {"details": "Детайли", "organize": "Организиране на подреждането"}, "successToast": "Категорията {{name}} беше успешно създадена."}, "edit": {"header": "Редактиране на категория", "description": "Редактирайте категорията, за да актуализирате нейните детайли.", "successToast": "Категорията беше успешно актуализирана."}, "delete": {"confirmation": "Ще изтриете категорията {{name}}. Това действие не може да бъде отменено.", "successToast": "Категорията {{name}} беше успешно изтрита."}, "products": {"add": {"disabledTooltip": "Продуктът вече е в тази категория.", "successToast_one": "До<PERSON><PERSON><PERSON><PERSON>н е {{count}} продукт към категорията.", "successToast_other": "Добавени са {{count}} продукта към категорията."}, "remove": {"confirmation_one": "Ще премахнете {{count}} продукт от категорията. Това действие не може да бъде отменено.", "confirmation_other": "Ще премахнете {{count}} продукта от категорията. Това действие не може да бъде отменено.", "successToast_one": "Према<PERSON><PERSON><PERSON> е {{count}} продукт от категорията.", "successToast_other": "Премахнати са {{count}} продукта от категорията."}, "list": {"noRecordsMessage": "Няма продукти в категорията."}}, "organize": {"header": "Организиране", "action": "Редактиране на подреждането"}, "fields": {"visibility": {"label": "Видимост", "internal": "Вътрешна", "public": "Публична"}, "status": {"label": "Статус", "active": "Активна", "inactive": "Неактивна"}, "path": {"label": "Път", "tooltip": "Показване на пълния път на категорията."}, "children": {"label": "Подкатегории"}, "new": {"label": "Нова"}}}, "inventory": {"domain": "Инвентар", "subtitle": "Управлявайте вашите инвентарни елементи", "reserved": "Резервирано", "available": "Налично", "locationLevels": "Локации", "associatedVariants": "Свързани варианти", "manageLocations": "Управление на локации", "deleteWarning": "Ще изтриете инвентарен елемент. Това действие не може да бъде отменено.", "editItemDetails": "Редактиране на детайлите на елемента", "create": {"title": "Създаване на инвентарен елемент", "details": "Детайли", "availability": "Наличност", "locations": "Локации", "attributes": "Атрибути", "requiresShipping": "Изисква доставка", "requiresShippingHint": "Изисква ли инвентарният елемент доставка?", "successToast": "Инвентарният елемент беше успешно създаден."}, "reservation": {"header": "Резервация на {{itemName}}", "editItemDetails": "Редактиране на резервацията", "lineItemId": "ID на елемента", "orderID": "ID на поръчката", "description": "Описание", "location": "Локация", "inStockAtLocation": "На склад на тази локация", "availableAtLocation": "Налично на тази локация", "reservedAtLocation": "Резервирано на тази локация", "reservedAmount": "Резервирано количество", "create": "Създаване на резервация", "itemToReserve": "Елемент за резервация", "quantityPlaceholder": "Колко искате да резервирате?", "descriptionPlaceholder": "Какъв е типът на резервацията?", "successToast": "Резервацията беше успешно създадена.", "updateSuccessToast": "Резервацията беше успешно актуализирана.", "deleteSuccessToast": "Резервацията беше успешно изтрита.", "errors": {"noAvaliableQuantity": "На избраната локация няма налично количество.", "quantityOutOfRange": "Минималното количество е 1, а максималното е {{max}}"}}, "adjustInventory": {"errors": {"stockedQuantity": "Наличността не може да бъде намалена под резервираното количество от {{quantity}}."}}, "toast": {"updateLocations": "Локациите бяха успешно актуализирани.", "updateLevel": "Нивото на инвентара беше успешно актуализирано.", "updateItem": "Инвентарният елемент беше успешно актуализиран."}, "stock": {"title": "Актуализиране на нивата на инвентара", "description": "Актуализирайте нивата на наличност за избраните инвентарни елементи.", "action": "Редактиране на нива на инвентара", "placeholder": "Не е активирано", "disablePrompt_one": "Ще деактивирате {{count}} ниво на локация. Това действие не може да бъде отменено.", "disablePrompt_other": "Ще деактивирате {{count}} нива на локации. Това действие не може да бъде отменено.", "disabledToggleTooltip": "Не може да се деактивира: изчистете входящото и/или резервираното количество преди деактивиране.", "successToast": "Нивата на инвентара бяха успешно актуализирани."}}, "giftCards": {"domain": "Подаръчни карти", "editGiftCard": "Редактиране на подаръчна карта", "createGiftCard": "Създаване на подаръчна карта", "createGiftCardHint": "Ръчно създайте подаръчна карта, която може да се използва като метод на плащане във вашия магазин.", "selectRegionFirst": "Първо изберете регион", "deleteGiftCardWarning": "Ще изтриете подаръчната карта {{code}}. Това действие не може да бъде отменено.", "balanceHigherThanValue": "Балансът не може да бъде по-голям от първоначалната стойност.", "balanceLowerThanZero": "Балансът не може да бъде отрицателен.", "expiryDateHint": "Различните държави имат различни закони относно срока на валидност на подаръчните карти. Проверете местните разпоредби, преди да зададете дата на изтичане.", "regionHint": "Промяната на региона на подаръчната карта ще промени и нейната валута, което може да повлияе на нейната стойност.", "enabledHint": "Укажете дали подаръчната карта е активирана или деактивирана.", "balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentBalance": "Текущ баланс", "initialBalance": "Първона<PERSON><PERSON><PERSON>ен баланс", "personalMessage": "Лично съобщение", "recipient": "Получател"}, "customers": {"domain": "Клиенти", "list": {"noRecordsMessage": "Вашите клиенти ще се покажат тук."}, "create": {"header": "Създаване на клиент", "hint": "Създайте нов клиент и управлявайте неговите детайли.", "successToast": "Клиентът {{email}} беше успешно създаден."}, "groups": {"label": "Групи клиенти", "remove": "Сигурни ли сте, че искате да премахнете клиента от групата \"{{name}}\"?", "removeMany": "Сигурни ли сте, че искате да премахнете клиента от следните групи: {{groups}}?", "alreadyAddedTooltip": "Клиентът вече е в тази група.", "list": {"noRecordsMessage": "Този клиент не принадлежи към никоя група."}, "add": {"success": "Клиентът е добавен към: {{groups}}.", "list": {"noRecordsMessage": "Моля, първо създайте група клиенти."}}, "removed": {"success": "Клиентът е премахнат от: {{groups}}.", "list": {"noRecordsMessage": "Моля, първо създайте група клиенти."}}}, "edit": {"header": "Редактиране на клиент", "emailDisabledTooltip": "Имейл адресът не може да бъде променен за регистрирани клиенти.", "successToast": "Клиентът {{email}} беше успешно актуализиран."}, "delete": {"title": "Изтриване на клиент", "description": "Ще изтриете клиента {{email}}. Това действие не може да бъде отменено.", "successToast": "Клиентът {{email}} беше успешно изтрит."}, "fields": {"guest": "Гост", "registered": "Регистриран", "groups": "Гру<PERSON>и"}, "registered": "Регистриран", "guest": "Гост", "hasAccount": "<PERSON><PERSON>а акаунт"}, "customerGroups": {"domain": "Групи клиенти", "subtitle": "Организирайте клиентите в групи. Групите могат да имат различни промоции и цени.", "list": {"empty": {"heading": "Няма групи клиенти", "description": "Няма налични групи клиенти за показване."}, "filtered": {"heading": "Няма резултати", "description": "Няма групи клиенти, които да отговарят на текущите филтри."}}, "create": {"header": "Създаване на група клиенти", "hint": "Създайте нова група клиенти, за да ги сегментирате.", "successToast": "Групата клиенти {{name}} беше успешно създадена."}, "edit": {"header": "Редактиране на група клиенти", "successToast": "Групата клиенти {{name}} беше успешно актуализирана."}, "delete": {"title": "Изтриване на група клиенти", "description": "Ще изтриете групата клиенти {{name}}. Това действие не може да бъде отменено.", "successToast": "Групата клиенти {{name}} беше успешно изтрита."}, "customers": {"alreadyAddedTooltip": "Клиентът вече е добавен към групата.", "add": {"successToast_one": "Клиентът беше успешно добавен към групата.", "successToast_other": "Клиентите бяха успешно добавени към групата.", "list": {"noRecordsMessage": "Първо създайте клиент."}}, "remove": {"title_one": "Премахване на клиент", "title_other": "Премахване на клиенти", "description_one": "Ще премахнете {{count}} клиент от групата. Това действие не може да бъде отменено.", "description_other": "Ще премахнете {{count}} клиенти от групата. Това действие не може да бъде отменено."}, "list": {"noRecordsMessage": "Тази група няма клиенти."}}}, "orders": {"domain": "Поръчки", "claim": "Рекламация", "exchange": "Замяна", "return": "Връщане", "cancelWarning": "Ще анулирате поръчката {{id}}. Това действие не може да бъде отменено.", "orderCanceled": "Поръчката беше успешно анулирана", "onDateFromSalesChannel": "{{date}} от {{salesChannel}}", "list": {"noRecordsMessage": "Вашите поръчки ще се покажат тук."}, "status": {"not_paid": "Неплатена", "pending": "В изчакване", "completed": "Завършена", "draft": "Чернова", "archived": "Архивирана", "canceled": "Анули<PERSON><PERSON>на", "requires_action": "Изисква действие"}, "summary": {"requestReturn": "Заявка за връщане", "allocateItems": "Разпределете артикули", "editOrder": "Редактиране на поръчка", "editOrderContinue": "Продължете редакцията на поръчката", "inventoryKit": "Състои се от {{count}}x инвентарни елемента", "itemTotal": "Обща стойност на артикули", "shippingTotal": "Обща стойност на доставка", "discountTotal": "Обща стойност на отстъпки", "taxTotalIncl": "Обща стойност на данъци (включени)", "itemSubtotal": "Междинна сума за артикули", "shippingSubtotal": "Междинна сума за доставка", "discountSubtotal": "Междинна сума за отстъпки", "taxTotal": "Обща стойност на данъци"}, "transfer": {"title": "Прехвърляне на собственост", "requestSuccess": "Заявката за прехвърляне на поръчката беше изпратена до: {{email}}.", "currentOwner": "Текущ собственик", "newOwner": "Нов собственик", "currentOwnerDescription": "Клиентът, свързан с тази поръчка в момента.", "newOwnerDescription": "Клиентът, на когото ще се прехвърли тази поръчка."}, "payment": {"title": "Плащания", "isReadyToBeCaptured": "Плащането <0/> е готово за осребряване.", "totalPaidByCustomer": "Общо платено от клиента", "capture": "Осребряване на плащане", "capture_short": "Осребри", "refund": "Възстановяване", "markAsPaid": "Мар<PERSON><PERSON><PERSON><PERSON><PERSON> като платено", "statusLabel": "Статус на плащането", "statusTitle": "Статус на плащане", "status": {"notPaid": "Неплатено", "authorized": "Одобрено", "partiallyAuthorized": "Частично одобрено", "awaiting": "В изчакване", "captured": "Осребрено", "partiallyRefunded": "Частично възстановено", "partiallyCaptured": "Частично осребрено", "refunded": "Възстановено", "canceled": "Ану<PERSON><PERSON><PERSON><PERSON><PERSON>о", "requiresAction": "Изисква действие"}, "capturePayment": "Плащането от {{amount}} ще бъде осребрено.", "capturePaymentSuccess": "Плащането от {{amount}} беше успешно осребрено", "markAsPaidPayment": "Плащането от {{amount}} ще бъде маркирано като платено.", "markAsPaidPaymentSuccess": "Плащането от {{amount}} беше успешно маркирано като платено", "createRefund": "Създаване на възстановяване", "refundPaymentSuccess": "Успешно възстановяване на сума {{amount}}", "createRefundWrongQuantity": "Количество трябва да бъде число между 1 и {{number}}", "refundAmount": "Възстанови {{ amount }}", "paymentLink": "Копирай линк за плащане за {{ amount }}", "selectPaymentToRefund": "Изберете плащане за възстановяване"}, "edits": {"title": "Редактиране на поръчка", "confirm": "Потвърди редакцията", "confirmText": "Ще потвърдите редакция на поръчка. Това действие не може да бъде отменено.", "cancel": "Отказ на редакцията", "currentItems": "Текущи артикули", "currentItemsDescription": "Коригирайте количеството или премахнете артикули.", "addItemsDescription": "Можете да добавите нови артикули към поръчката.", "addItems": "Добави артикули", "amountPaid": "Плат<PERSON>на сума", "newTotal": "Нова обща сума", "differenceDue": "Разлика за доплащане", "create": "Редактиране на поръчка", "currentTotal": "Текуща обща сума", "noteHint": "Добавете вътрешна бележка за редакцията", "cancelSuccessToast": "Редакцията на поръчката е отменена", "createSuccessToast": "Заявка за редакция на поръчка е създадена", "activeChangeError": "Вече има активна промяна на поръчката (връщане, рекламация, замяна и др.). Моля, завършете или отменете текущата промяна, преди да редактирате поръчката.", "panel": {"title": "Заявена редакция на поръчка", "titlePending": "Очакваща редакция на поръчка"}, "toast": {"canceledSuccessfully": "Редакцията на поръчката е отменена", "confirmedSuccessfully": "Редакцията на поръчката е потвърдена"}, "validation": {"quantityLowerThanFulfillment": "Не може да зададете количество, по-малко или равно на вече изпълненото количество"}}, "edit": {"email": {"title": "Редактиране на имейл", "requestSuccess": "Имейлът на поръчката е актуализиран до {{email}}."}, "shippingAddress": {"title": "Редактиране на адрес за доставка", "requestSuccess": "Адресът за доставка е актуализиран."}, "billingAddress": {"title": "Редактиране на адрес за фактуриране", "requestSuccess": "Адресът за фактуриране е актуализиран."}}, "returns": {"create": "Създаване на връщане", "confirm": "Потвърди връщането", "confirmText": "Ще потвърдите връщането. Това действие не може да бъде отменено.", "inbound": "Входящо", "outbound": "Изходящо", "sendNotification": "Изпрати известие", "sendNotificationHint": "Уведоми клиента за връщането.", "returnTotal": "Обща стойност за връщане", "inboundTotal": "Общо входящо", "refundAmount": "Сума за възстановяване", "outstandingAmount": "Дължима сума", "reason": "Причина", "reasonHint": "Изберете защо клиентът иска да върне артикулите.", "note": "Бележка", "noInventoryLevel": "Няма ниво на инвентара", "noInventoryLevelDesc": "Избраната локация няма инвентар за избраните артикули. Връщането може да бъде заявено, но няма да бъде прието, докато не бъде създадено ниво на инвентара за избраната локация.", "noteHint": "Можете да добавите допълнителна бележка, ако желаете.", "location": "Локация", "locationHint": "Изберете към коя локация искате да върнете артикулите.", "inboundShipping": "Доставка за връщане", "inboundShippingHint": "Изберете метод за връщане на артикулите.", "returnableQuantityLabel": "Количество за връщане", "refundableAmountLabel": "Сума за възстановяване", "returnRequestedInfo": "Заявено връщане на {{requestedItemsCount}} артикула", "returnReceivedInfo": "{{requestedItemsCount}} артикула върнати", "itemReceived": "Артикулите са получени", "returnRequested": "Връщането е заявено", "damagedItemReceived": "Получени повредени артикули", "damagedItemsReturned": "{{quantity}} повредени артикула върнати", "activeChangeError": "Има активна промяна на поръчката. Моля, завършете или отхвърлете текущата промяна преди да продължите.", "cancel": {"title": "Отказ на връщане", "description": "Сигурни ли сте, че искате да отмените заявката за връщане?"}, "placeholders": {"noReturnShippingOptions": {"title": "Няма намерени опции за връщане", "hint": "Не са създадени опции за връщане за тази локация. Можете да създадете такава в <LinkComponent>Локации и доставка</LinkComponent>."}, "outboundShippingOptions": {"title": "Няма намерени опции за изходяща доставка", "hint": "Не са създадени опции за изходяща доставка за тази локация. Можете да създадете такава в <LinkComponent>Локации и доставка</LinkComponent>."}}, "receive": {"action": "Получаване на артикули", "receiveItems": "{{ returnType }} {{ id }}", "restockAll": "Върни всички артикули на склад", "itemsLabel": "Получени артикули", "title": "Получаване на артикули за #{{returnId}}", "sendNotificationHint": "Уведоми клиента за полученото връщане.", "inventoryWarning": "Моля, обърнете внимание, че автоматично ще актуализираме нивата на инвентара въз основа на въведената информация.", "writeOffInputLabel": "Колко от артикулите са повредени?", "toast": {"success": "Връщането беше успешно получено.", "errorLargeValue": "Количеството е по-голямо от заявеното.", "errorNegativeValue": "Количеството не може да е отрицателно.", "errorLargeDamagedValue": "Общото количество повредени и неповредени артикули надвишава заявеното. Моля, коригирайте количеството."}}, "toast": {"canceledSuccessfully": "Връщането беше успешно отменено", "confirmedSuccessfully": "Връщането беше успешно потвърдено"}, "panel": {"title": "Връщане започнато", "description": "Има активна заявка за връщане, която трябва да бъде завършена"}}, "claims": {"create": "Създай рекламация", "confirm": "Потвърди рекламацията", "confirmText": "Ще потвърдите рекламация. Това действие не може да бъде отменено.", "manage": "Управлявай рекламацията", "outbound": "Изходящо", "outboundItemAdded": "{{itemsCount}}x добавени чрез рекламация", "outboundTotal": "Общо изходящо", "outboundShipping": "Изходяща доставка", "outboundShippingHint": "Изберете метода на доставка.", "refundAmount": "Очаквана разлика", "activeChangeError": "Има активна промяна на поръчката. Моля, завършете или отхвърлете предишната промяна.", "actions": {"cancelClaim": {"successToast": "Рекламацията беше успешно отменена."}}, "cancel": {"title": "Отмени рекламацията", "description": "Сигурни ли сте, че искате да отмените рекламацията?"}, "tooltips": {"onlyReturnShippingOptions": "Този списък ще съдържа само опции за връщане."}, "toast": {"canceledSuccessfully": "Рекламацията е успешно отменена", "confirmedSuccessfully": "Рекламацията е успешно потвърдена"}, "panel": {"title": "Рекламацията е започната", "description": "Има активна заявка за рекламация, която трябва да бъде завършена"}}, "exchanges": {"create": "Създай замяна", "manage": "Управлявай замяната", "confirm": "Потвърди замяната", "confirmText": "Ще потвърдите замяна. Това действие не може да бъде отменено.", "outbound": "Изходящо", "outboundItemAdded": "{{itemsCount}}x добавени чрез замяна", "outboundTotal": "Общо изходящо", "outboundShipping": "Изходяща доставка", "outboundShippingHint": "Изберете метода на доставка.", "refundAmount": "Очаквана разлика", "activeChangeError": "Има активна промяна на поръчката. Моля, завършете или отхвърлете предишната промяна.", "actions": {"cancelExchange": {"successToast": "Замяната беше успешно отменена."}}, "cancel": {"title": "Отмени замяната", "description": "Сигурни ли сте, че искате да отмените замяната?"}, "tooltips": {"onlyReturnShippingOptions": "Този списък ще съдържа само опции за връщане."}, "toast": {"canceledSuccessfully": "Замяната е успешно отменена", "confirmedSuccessfully": "Замяната е успешно потвърдена"}, "panel": {"title": "Замяната е започната", "description": "Има активна заявка за замяна, която трябва да бъде завършена"}}, "reservations": {"allocatedLabel": "Разпределени", "notAllocatedLabel": "Неразпределени"}, "allocateItems": {"action": "Разпредели артикули", "title": "Разпределение на артикулите в поръчката", "locationDescription": "Изберете от коя локация искате да разпределите.", "itemsToAllocate": "Артикули за разпределение", "itemsToAllocateDesc": "Изберете броя артикули за разпределение", "search": "Търси артикули", "consistsOf": "Състои се от {{num}}x инвентарни артикула", "requires": "Изисква {{num}} за вариант", "toast": {"created": "Артикулите бяха успешно разпределени"}, "error": {"quantityNotAllocated": "Има неразпределени артикули."}}, "shipment": {"title": "Отбележи пратката като изпратена", "trackingNumber": "Номер за проследяване", "addTracking": "Добави номер за проследяване", "sendNotification": "Изпрати уведомление", "sendNotificationHint": "Уведоми клиента за тази пратка.", "toastCreated": "Пратката беше успешно създадена."}, "fulfillment": {"cancelWarning": "Ще отмените изпълнението. Това действие не може да бъде отменено.", "markAsDeliveredWarning": "Ще отбележите изпълнението като доставено. Това действие не може да бъде отменено.", "differentOptionSelected": "Избраната опция за доставка се различава от избраната от клиента.", "disabledItemTooltip": "Избраната опция за доставка не позволява изпълнението на този артикул", "unfulfilledItems": "Неизпълнени артикули", "statusLabel": "Статус на изпълнението", "statusTitle": "Статус на изпълнението", "fulfillItems": "Изпълни артикули", "awaitingFulfillmentBadge": "Очаква изпълнение", "requiresShipping": "Изисква доставка", "number": "Изпълнение #{{number}}", "itemsToFulfill": "Артикули за изпълнение", "create": "Създай изпълнение", "available": "Налично", "inStock": "В наличност", "markAsShipped": "Отбележи като изпратено", "markAsPickedUp": "Отбележи като взето", "markAsDelivered": "Отбележи като доставено", "itemsToFulfillDesc": "Изберете артикули и количества за изпълнение", "locationDescription": "Изберете от коя локация да изпълните артикулите.", "sendNotificationHint": "Уведоми клиентите за създаденото изпълнение.", "methodDescription": "Изберете различен метод на доставка от този, избран от клиента", "error": {"wrongQuantity": "Само един артикул е наличен за изпълнение", "wrongQuantity_other": "Количеството трябва да бъде между 1 и {{number}}", "noItems": "Няма артикули за изпълнение.", "noShippingOption": "Изисква се опция за доставка", "noLocation": "Изисква се локация"}, "status": {"notFulfilled": "Неизпълнено", "partiallyFulfilled": "Частично изпълнено", "fulfilled": "Изпълнено", "partiallyShipped": "Частично изпратено", "shipped": "Изпратено", "delivered": "Доставено", "partiallyDelivered": "Частично доставено", "partiallyReturned": "Частично върнато", "returned": "Върнато", "canceled": "Отменено", "requiresAction": "Изисква действие"}, "toast": {"created": "Изпълнението беше успешно създадено", "canceled": "Изпълнението беше успешно отменено", "fulfillmentShipped": "Не може да се отмени вече изпратено изпълнение", "fulfillmentDelivered": "Изпълнението е успешно отбелязано като доставено", "fulfillmentPickedUp": "Изпълнението е успешно отбелязано като взето"}, "trackingLabel": "Проследяване", "shippingFromLabel": "Изпращане от", "itemsLabel": "Артикули"}, "refund": {"title": "Създай възстановяване", "sendNotificationHint": "Уведоми клиентите за създаденото възстановяване.", "systemPayment": "Системно плащане", "systemPaymentDesc": "Едно или повече от вашите плащания е системно. Бъдете внимателни, че Medusa не обработва улавяне и възстановяване за такива плащания.", "error": {"amountToLarge": "Не може да се възстанови повече от първоначалната сума на поръчката.", "amountNegative": "Сумата за възстановяване трябва да бъде положителна.", "reasonRequired": "Моля, изберете причина за възстановяване."}}, "customer": {"contactLabel": "Кон<PERSON><PERSON><PERSON>т", "editEmail": "Редакти<PERSON><PERSON>й имейл", "transferOwnership": "Прехвърли собственост", "editBillingAddress": "Редактирай адрес за фактуриране", "editShippingAddress": "Редактирай адрес за доставка"}, "activity": {"header": "Дейност", "showMoreActivities_one": "Покажи още {{count}} дейност", "showMoreActivities_other": "Покажи още {{count}} дейности", "comment": {"label": "Коментар", "placeholder": "Оставете коментар", "addButtonText": "Добави коментар", "deleteButtonText": "Изтрий коментара"}, "from": "От", "to": "До", "events": {"common": {"toReturn": "За връщане", "toSend": "За изпращане"}, "placed": {"title": "Поръчката е направена", "fromSalesChannel": "от {{salesChannel}}"}, "canceled": {"title": "Поръчката е отменена"}, "payment": {"awaiting": "Изчаква плащане", "captured": "Плащането е прието", "canceled": "Плащането е отменено", "refunded": "Плащането е възстановено"}, "fulfillment": {"created": "Артикулите са изпълнени", "canceled": "Изпълнението е отменено", "shipped": "Артикулите са изпратени", "delivered": "Артикулите са доставени", "items_one": "{{count}} артикул", "items_other": "{{count}} артикула"}, "return": {"created": "Заявено връщане #{{returnId}}", "canceled": "Връщане #{{returnId}} е отменено", "received": "Връщане #{{returnId}} е получено", "items_one": "{{count}} артикул върнат", "items_other": "{{count}} артикула върнати"}, "note": {"comment": "Коментар", "byLine": "от {{author}}"}, "claim": {"created": "Заявка #{{claimId}} е създадена", "canceled": "Заявка #{{claimId}} е отменена", "itemsInbound": "{{count}} артикул за връщане", "itemsOutbound": "{{count}} артикул за изпращане"}, "exchange": {"created": "Замяна #{{exchangeId}} е заявена", "canceled": "Замяна #{{exchangeId}} е отменена", "itemsInbound": "{{count}} артикул за връщане", "itemsOutbound": "{{count}} артикул за изпращане"}, "edit": {"requested": "Редакция на поръчка #{{editId}} е заявена", "confirmed": "Редакция на поръчка #{{editId}} е потвърдена"}, "transfer": {"requested": "Прехвърляне на поръчка #{{transferId}} е заявено", "confirmed": "Прехвърляне на поръчка #{{transferId}} е потвърдено", "declined": "Прехвърляне на поръчка #{{transferId}} е отказано"}, "update_order": {"shipping_address": "Адресът за доставка е актуализиран", "billing_address": "Адресът за фактуриране е актуализиран", "email": "Имейлът е актуализиран"}}}, "fields": {"displayId": "ID за показване", "refundableAmount": "Сума за възстановяване", "returnableQuantity": "Количество за връщане"}}, "draftOrders": {"domain": "Чернови на поръчки", "deleteWarning": "Ще изтриете черновата на поръчка {{id}}. Това действие не може да бъде отменено.", "paymentLinkLabel": "Линк за плащане", "cartIdLabel": "ID на количката", "markAsPaid": {"label": "Мар<PERSON><PERSON><PERSON><PERSON><PERSON> като платено", "warningTitle": "Мар<PERSON><PERSON><PERSON><PERSON><PERSON> като платено", "warningDescription": "Ще маркирате черновата на поръчка като платена. Това действие не може да бъде отменено и събирането на плащане няма да е възможно по-късно."}, "status": {"open": "Отворена", "completed": "Завършена"}, "create": {"createDraftOrder": "Създай чернова на поръчка", "createDraftOrderHint": "Създайте нова чернова на поръчка, за да управлявате детайлите преди финализирането ѝ.", "chooseRegionHint": "Изберете регион", "existingItemsLabel": "Съществуващи артикули", "existingItemsHint": "Добавете съществуващи продукти към черновата на поръчка.", "customItemsLabel": "Персонализирани артикули", "customItemsHint": "Добавете персонализирани артикули към черновата на поръчка.", "addExistingItemsAction": "Добави съществуващи артикули", "addCustomItemAction": "Добави персонализиран артикул", "noCustomItemsAddedLabel": "Все още няма добавени персонализирани артикули", "noExistingItemsAddedLabel": "Все още няма добавени съществуващи артикули", "chooseRegionTooltip": "Първо изберете регион", "useExistingCustomerLabel": "Използвай съществуващ клиент", "addShippingMethodsAction": "Добави методи за доставка", "unitPriceOverrideLabel": "Промяна на единичната цена", "shippingOptionLabel": "Опция за доставка", "shippingOptionHint": "Изберете опция за доставка за черновата на поръчка.", "shippingPriceOverrideLabel": "Промяна на цената за доставка", "shippingPriceOverrideHint": "Променете цената за доставка за черновата на поръчка.", "sendNotificationLabel": "Изпрати уведомление", "sendNotificationHint": "Изпратете уведомление до клиента, когато черновата на поръчка е създадена."}, "validation": {"requiredEmailOrCustomer": "Имейл или клиент е задължителен.", "requiredItems": "Изисква се поне един артикул.", "invalidEmail": "Имейлът трябва да е валиден адрес."}}, "stockLocations": {"domain": "Локации и Доставка", "list": {"description": "Управлявайте складовите локации и опциите за доставка на вашия магазин."}, "create": {"header": "Създайте Складова Локация", "hint": "Складовата локация е физическо място, където се съхраняват и изпращат продукти.", "successToast": "Локацията {{name}} беше създадена успешно."}, "edit": {"header": "Редактирайте Складова Локация", "viewInventory": "Преглед на инвентара", "successToast": "Локацията {{name}} беше успешно актуализирана."}, "delete": {"confirmation": "Ще изтриете складовата локация {{name}}. Това действие не може да бъде отменено."}, "fulfillmentProviders": {"header": "Доставчици за Изпълнение", "shippingOptionsTooltip": "Този падащ списък ще съдържа само доставчици, активирани за тази локация. Добавете ги към локацията, ако списъкът е деактивиран.", "label": "Свързани доставчици за изпълнение", "connectedTo": "Свързано с {{count}} от {{total}} доставчици за изпълнение", "noProviders": "Тази складова локация не е свързана с нито един доставчик за изпълнение.", "action": "Свържи доставчици", "successToast": "Доставчиците за изпълнение бяха успешно актуализирани."}, "fulfillmentSets": {"pickup": {"header": "Вземане на място"}, "shipping": {"header": "Доставка"}, "disable": {"confirmation": "Сигурни ли сте, че искате да деактивирате {{name}}? Това ще изтрие всички свързани зони за услуги и опции за доставка и не може да бъде отменено.", "pickup": "Вземането на място беше успешно деактивирано.", "shipping": "Доставката беше успешно деактивирана."}, "enable": {"pickup": "Вземането на място беше успешно активирано.", "shipping": "Доставката беше успешно активирана."}}, "sidebar": {"header": "Конфигурация на Доставката", "shippingProfiles": {"label": "Профили за Доставка", "description": "Групирайте продуктите според изискванията за доставка"}}, "salesChannels": {"header": "Канали за Продажби", "hint": "Управлявайте каналите за продажби, свързани с тази локация.", "label": "Свързани канали за продажби", "connectedTo": "Свързано с {{count}} от {{total}} канали за продажби", "noChannels": "Локацията не е свързана с нито един канал за продажби.", "action": "Свържи канали за продажби", "successToast": "Каналите за продажби бяха успешно актуализирани."}, "pickupOptions": {"edit": {"header": "Промени опция за взимане"}}, "shippingOptions": {"create": {"shipping": {"header": "Създай Опция за Доставка за {{zone}}", "hint": "Създайте нова опция за доставка, за да определите как продуктите се изпращат от тази локация.", "label": "Опции за доставка", "successToast": "Опцията за доставка {{name}} беше създадена успешно."}, "pickup": {"header": "Създай опция за взимане за {{zone}}", "hint": "Създай нова опция за взимане за да определиш как продуктите ще бъдат взимани от тази локация.", "label": "Опции за взимане", "successToast": "Опция за взимане {{name}} беше успешно създадена."}, "returns": {"header": "Създай Опция за Връщане за {{zone}}", "hint": "Създайте нова опция за връщане, за да дефинирате как продуктите се връщат към тази локация.", "label": "Опции за връщане", "successToast": "Опцията за връщане {{name}} беше създадена успешно."}, "tabs": {"details": "Детайли", "prices": "Цени"}, "action": "Създай опция"}, "delete": {"confirmation": "Ще изтриете опцията за доставка {{name}}. Това действие не може да бъде отменено.", "successToast": "Опцията за доставка {{name}} беше успешно изтрита."}, "edit": {"header": "Редактиране на Опция за Доставка", "action": "Редактирай опция", "successToast": "Опцията за доставка {{name}} беше успешно актуализирана."}, "pricing": {"action": "Редактирай цените"}, "conditionalPrices": {"header": "Условни Цени за {{name}}", "description": "Управлявайте условните цени за тази опция за доставка въз основа на общата стойност на артикули в количката.", "attributes": {"cartItemTotal": "Общо артикули в количката"}, "summaries": {"range": "Ако <0>{{attribute}}</0> е между <1>{{gte}}</1> и <2>{{lte}}</2>", "greaterThan": "Ако <0>{{attribute}}</0> ≥ <1>{{gte}}</1>", "lessThan": "Ако <0>{{attribute}}</0> ≤ <1>{{lte}}</1>"}, "actions": {"addPrice": "Добави цена", "manageConditionalPrices": "Управлявай условните цени"}, "rules": {"amount": "Цена на опцията за доставка", "gte": "Мини<PERSON><PERSON><PERSON>на стойност на количката", "lte": "Максимална стойност на количката"}, "customRules": {"label": "Персонализирани правила", "tooltip": "Тази условна цена има правила, които не могат да бъдат управлявани в таблото за управление.", "eq": "Общата стойност на количката трябва да е равна на", "gt": "Общата стойност на количката трябва да е по-голяма от", "lt": "Общата стойност на количката трябва да е по-малка от"}, "errors": {"amountRequired": "Изисква се цена за опцията за доставка", "minOrMaxRequired": "Трябва да се предостави поне една от минимална или максимална стойност на количката", "minGreaterThanMax": "Минималната стойност на количката трябва да е по-малка или равна на максималната стойност", "duplicateAmount": "Цената за опцията за доставка трябва да е уникална за всяко условие", "overlappingConditions": "Условията трябва да са уникални за всички правила за ценообразуване"}}, "fields": {"count": {"shipping_one": "{{count}} опция за доставка", "shipping_other": "{{count}} опции за доставка", "pickup_one": "{{count}} опция за взимане", "pickup_other": "{{count}} опции за взимане", "returns_one": "{{count}} опция за връщане", "returns_other": "{{count}} опции за връщане"}, "priceType": {"label": "Тип цена", "options": {"fixed": {"label": "Фиксир<PERSON>на", "hint": "Цената на опцията за доставка е фиксирана и не се променя според съдържанието на поръчката."}, "calculated": {"label": "Изчислена", "hint": "Цената на опцията за доставка се изчислява от доставчика по време на поръчката."}}}, "enableInStore": {"label": "Активиране в магазина", "hint": "Дали клиентите могат да използват тази опция при финализиране на поръчката."}, "provider": "Доставчик за изпълнение", "profile": "Профил за доставка", "fulfillmentOption": "Опция за изпълнение"}}, "serviceZones": {"create": {"headerPickup": "Създай Зона за Услуги за Вземане от {{location}}", "headerShipping": "Създай Зона за Услуги за Доставка от {{location}}", "action": "Създай зона за услуги", "successToast": "Зоната за услуги {{name}} беше създадена успешно."}, "edit": {"header": "Редакти<PERSON><PERSON><PERSON> Зона за Услуги", "successToast": "Зоната за услуги {{name}} беше успешно актуализирана."}, "delete": {"confirmation": "Ще изтриете зоната за услуги {{name}}. Това действие не може да бъде отменено.", "successToast": "Зоната за услуги {{name}} беше успешно изтрита."}, "manageAreas": {"header": "Управлявай Зони за {{name}}", "action": "Управлявай зони", "label": "Зони", "hint": "Изберете географските зони, които обхваща зоната за услуги.", "successToast": "Зоните за {{name}} бяха успешно актуализирани."}, "fields": {"noRecords": "Няма зони за услуги, към които да добавите опции за доставка.", "tip": "Зоната за услуги е колекция от географски зони. Използва се за ограничаване на наличните опции за доставка до определени местоположения."}}}, "shippingProfile": {"domain": "Профили за доставка", "subtitle": "Групирайте продукти с подобни изисквания за доставка в профили.", "create": {"header": "Създаване на профил за доставка", "hint": "Създайте нов профил за доставка, за да групирате продукти с подобни изисквания за доставка.", "successToast": "Профилът за доставка {{name}} беше създаден успешно."}, "delete": {"title": "Изтриване на профил за доставка", "description": "Вие ще изтриете профила за доставка {{name}}. Това действие не може да бъде отменено.", "successToast": "Профилът за доставка {{name}} беше изтрит успешно."}, "tooltip": {"type": "Въведете тип профил за доставка, например: <PERSON><PERSON><PERSON><PERSON><PERSON>, Голям размер, Само товари и др."}}, "taxRegions": {"domain": "Данъчни региони", "list": {"hint": "Управлявайте какво начислявате на клиентите си, когато пазаруват от различни държави и региони."}, "delete": {"confirmation": "Ще изтриете данъчен регион. Това действие не може да бъде отменено.", "successToast": "Данъчният регион беше успешно изтрит."}, "create": {"header": "Създаване на данъчен регион", "hint": "Създайте нов данъчен регион, за да определите данъчните ставки за конкретна държава.", "errors": {"rateIsRequired": "Данъчната ставка е задължителна при създаване на стандартна данъчна ставка.", "nameIsRequired": "Името е задължително при създаване на стандартна данъчна ставка."}, "successToast": "Данъчният регион беше успешно създаден."}, "province": {"header": "Провинции", "create": {"header": "Създаване на данъчен регион за провинция", "hint": "Създайте нов данъчен регион, за да определите данъчни ставки за конкретна провинция."}}, "state": {"header": "Щати", "create": {"header": "Създаване на данъчен регион за щат", "hint": "Създайте нов данъчен регион, за да определите данъчни ставки за конкретен щат."}}, "stateOrTerritory": {"header": "Щати или територии", "create": {"header": "Създаване на данъчен регион за щат/територия", "hint": "Създайте нов данъчен регион, за да определите данъчни ставки за конкретен щат или територия."}}, "county": {"header": "Окръзи", "create": {"header": "Създаване на данъчен регион за окръг", "hint": "Създайте нов данъчен регион, за да определите данъчни ставки за конкретен окръг."}}, "region": {"header": "Региони", "create": {"header": "Създаване на данъчен регион за регион", "hint": "Създайте нов данъчен регион, за да определите данъчни ставки за конкретен регион."}}, "department": {"header": "Департаменти", "create": {"header": "Създаване на данъчен регион за департамент", "hint": "Създайте нов данъчен регион, за да определите данъчни ставки за конкретен департамент."}}, "territory": {"header": "Територии", "create": {"header": "Създаване на данъчен регион за територия", "hint": "Създайте нов данъчен регион, за да определите данъчни ставки за конкретна територия."}}, "prefecture": {"header": "Префектури", "create": {"header": "Създаване на данъчен регион за префектура", "hint": "Създайте нов данъчен регион, за да определите данъчни ставки за конкретна префектура."}}, "district": {"header": "Области", "create": {"header": "Създаване на данъчен регион за област", "hint": "Създайте нов данъчен регион, за да определите данъчни ставки за конкретна област."}}, "governorate": {"header": "Гу<PERSON><PERSON>рнаторства", "create": {"header": "Създаване на данъчен регион за губернаторство", "hint": "Създайте нов данъчен регион, за да определите данъчни ставки за конкретно губернаторство."}}, "canton": {"header": "Кантони", "create": {"header": "Създаване на данъчен регион за кантон", "hint": "Създайте нов данъчен регион, за да определите данъчни ставки за конкретен кантон."}}, "emirate": {"header": "Емира<PERSON>и", "create": {"header": "Създаване на данъчен регион за емират", "hint": "Създайте нов данъчен регион, за да определите данъчни ставки за конкретен емират."}}, "sublevel": {"header": "Поднива", "create": {"header": "Създаване на данъчен регион за подниво", "hint": "Създайте нов данъчен регион, за да определите данъчни ставки за конкретно подниво."}}, "taxOverrides": {"header": "Замени", "create": {"header": "Създаване на замяна", "hint": "Създайте данъчна ставка, която замества стандартните данъчни ставки при определени условия."}, "edit": {"header": "Редактиране на замяна", "hint": "Редактирайте данъчната ставка, която замества стандартните данъчни ставки при определени условия."}}, "taxRates": {"create": {"header": "Създаване на данъчна ставка", "hint": "Създайте нова данъчна ставка за определен регион.", "successToast": "Данъчната ставка беше успешно създадена."}, "edit": {"header": "Редактиране на данъчна ставка", "hint": "Редактирайте данъчната ставка за определен регион.", "successToast": "Данъчната ставка беше успешно актуализирана."}, "delete": {"confirmation": "Ще изтриете данъчната ставка {{name}}. Това действие не може да бъде отменено.", "successToast": "Данъчната ставка беше успешно изтрита."}}, "fields": {"isCombinable": {"label": "Комбинируема", "hint": "Дали тази данъчна ставка може да се комбинира със стандартната ставка от данъчния регион.", "true": "Комбинируема", "false": "Не може да се комбинира"}, "defaultTaxRate": {"label": "Стандартна данъчна ставка", "tooltip": "Стандартната данъчна ставка за този регион. Пример е стандартната ДДС ставка за дадена държава или регион.", "action": "Създаване на стандартна данъчна ставка"}, "taxRate": "Дан<PERSON><PERSON>на ставка", "taxCode": "Данъ<PERSON>ен код", "targets": {"label": "Цели", "hint": "Изберете целите, към които ще се прилага тази данъчна ставка.", "options": {"product": "Продукти", "productCollection": "Колекции от продукти", "productTag": "Етикети на продукти", "productType": "Видове продукти", "customerGroup": "Групи клиенти"}, "operators": {"in": "в", "on": "на", "and": "и"}, "placeholders": {"product": "Търсене на продукти", "productCollection": "Търсене на колекции от продукти", "productTag": "Търсене на етикети на продукти", "productType": "Търсене на видове продукти", "customerGroup": "Търсене на групи клиенти"}, "tags": {"product": "Продукт", "productCollection": "Колекция от продукти", "productTag": "Етикет на продукт", "productType": "Вид продукт", "customerGroup": "Група клиенти"}, "modal": {"header": "Добавяне на цели"}, "values_one": "{{count}} стойност", "values_other": "{{count}} стойности", "numberOfTargets_one": "{{count}} цел", "numberOfTargets_other": "{{count}} цели", "additionalValues_one": "и още {{count}} стойност", "additionalValues_other": "и още {{count}} стойности", "action": "Добавяне на цел"}, "sublevels": {"labels": {"province": "Провинция", "state": "Щат", "region": "Регион", "stateOrTerritory": "Щат/Територия", "department": "Депар<PERSON><PERSON><PERSON><PERSON><PERSON>т", "county": "Окръг", "territory": "Територия", "prefecture": "Префектура", "district": "Област", "governorate": "Гу<PERSON><PERSON>рнаторство", "emirate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "canton": "Кантон", "sublevel": "Код на подниво"}, "placeholders": {"province": "Изберете провинция", "state": "Изберете щат", "region": "Изберете регион", "stateOrTerritory": "Изберете щат/територия", "department": "Изберете департамент", "county": "Изберете окръг", "territory": "Изберете територия", "prefecture": "Изберете префектура", "district": "Изберете област", "governorate": "Изберете губернаторство", "emirate": "Изберете емират", "canton": "Изберете кантон"}, "tooltips": {"sublevel": "Въведете ISO 3166-2 кода за данъчния регион на подниво.", "notPartOfCountry": "{{province}} изглежда не е част от {{country}}. Моля, проверете отново дали това е вярно."}, "alert": {"header": "Поднивата са деактивирани за този данъчен регион", "description": "Поднивата са деактивирани за този регион по подразбиране. Можете да ги активирате, за да създавате поднива като провинции, щати или територии.", "action": "Активиране на поднива"}}, "noDefaultRate": {"label": "Няма стандартна ставка", "tooltip": "Този данъчен регион няма стандартна данъчна ставка. Ако има стандартна ставка, като например ДДС за дадена държава, моля, добавете я тук."}}}, "promotions": {"domain": "Промоции", "sections": {"details": "Детайли за промоцията"}, "tabs": {"template": "Тип", "details": "Детайли", "campaign": "Кампания"}, "fields": {"type": "Тип", "value_type": "Тип стойност", "value": "Стойност", "campaign": "Кампания", "method": "Метод", "allocation": "Разпределение", "addCondition": "Добави условие", "clearAll": "Изчисти всички", "amount": {"tooltip": "Изберете валутен код, за да зададете сумата"}, "conditions": {"rules": {"title": "Кой може да използва този код?", "description": "Кой клиент може да използва промоционалния код? Ако не е зададено, кодът може да се използва от всички клиенти."}, "target-rules": {"title": "За кои артикули ще се прилага промоцията?", "description": "Промоцията ще се прилага за артикулите, които отговарят на следните условия."}, "buy-rules": {"title": "Какво трябва да има в количката, за да се отключи промоцията?", "description": "Ако тези условия са изпълнени, промоцията ще се приложи върху избраните артикули."}}}, "tooltips": {"campaignType": "Валутният код трябва да бъде избран, за да се зададе бюджет за кампанията."}, "errors": {"requiredField": "Задължително поле", "promotionTabError": "Поправете грешките в раздела Промоция преди да продължите"}, "toasts": {"promotionCreateSuccess": "Промоцията ({{code}}) беше създадена успешно."}, "create": {}, "edit": {"title": "Редактиране на детайли за промоцията", "rules": {"title": "Редактиране на условия за използване"}, "target-rules": {"title": "Редактиране на условия за артикули"}, "buy-rules": {"title": "Редактиране на условия за покупка"}}, "campaign": {"header": "Кампания", "edit": {"header": "Редактиране на кампания", "successToast": "Кампанията на промоцията беше успешно актуализирана."}, "actions": {"goToCampaign": "Към кампанията"}}, "campaign_currency": {"tooltip": "Това е валутата на промоцията. Променете я от раздела Детайли."}, "form": {"required": "Задъл<PERSON>ително", "and": "И", "selectAttribute": "Изберете атрибут", "campaign": {"existing": {"title": "Съществуваща кампания", "description": "Добавете промоцията към съществуваща кампания.", "placeholder": {"title": "Няма съществуващи кампании", "desc": "Създайте такава, за да проследявате множество промоции и да задавате бюджетни ограничения."}}, "new": {"title": "Нова кампания", "description": "Създайте нова кампания за тази промоция."}, "none": {"title": "Без кампания", "description": "Продължете без да асоциирате промоцията с кампания"}}, "status": {"label": "Статус", "draft": {"title": "Чернова", "description": "Клиентите все още няма да могат да използват кода"}, "active": {"title": "Активна", "description": "Клиентите ще могат да използват кода"}, "inactive": {"title": "Неактивна", "description": "Клиентите вече няма да могат да използват кода"}}, "method": {"label": "Метод", "code": {"title": "Промоционален код", "description": "Клиентите трябва да въведат този код при плащане"}, "automatic": {"title": "Автоматично", "description": "Клиентите ще видят тази промоция при плащане"}}, "max_quantity": {"title": "Максимално количество", "description": "Максималният брой артикули, за които се прилага промоцията."}, "type": {"standard": {"title": "Стандартна", "description": "Стандартна промоция"}, "buyget": {"title": "Купи и вземи", "description": "Купи X, вземи Y промоция"}}, "allocation": {"each": {"title": "На всеки", "description": "Прилага стойността за всеки артикул"}, "across": {"title": "На всички", "description": "Прилага стойността върху всички артикули"}}, "code": {"title": "<PERSON>од", "description": "Кодът, който клиентите ще въведат при плащане."}, "value": {"title": "Стойност на промоцията"}, "value_type": {"fixed": {"title": "Фиксир<PERSON>на стойност", "description": "Сумата за отстъпка. напр. 100"}, "percentage": {"title": "Процент", "description": "Процент отстъпка от сумата. напр. 8%"}}}, "deleteWarning": "Ще изтриете промоцията {{code}}. Това действие не може да бъде отменено.", "createPromotionTitle": "Създаване на промоция", "type": "Тип промоция", "conditions": {"add": "Добави условие", "list": {"noRecordsMessage": "Добавете условие, за да ограничите артикулите, за които се прилага промоцията."}}}, "campaigns": {"domain": "Кампании", "details": "Детайли за кампанията", "status": {"active": "Активна", "expired": "Изтекла", "scheduled": "Пла<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "delete": {"title": "Сигурни ли сте?", "description": "Ще изтриете кампанията '{{name}}'. Това действие не може да бъде отменено.", "successToast": "Кампанията '{{name}}' беше успешно създадена."}, "edit": {"header": "Редактиране на кампания", "description": "Редактирайте детайлите на кампанията.", "successToast": "Кампанията '{{name}}' беше успешно актуализирана."}, "configuration": {"header": "Конфигурация", "edit": {"header": "Редактиране на конфигурацията на кампанията", "description": "Редактирайте конфигурацията на кампанията.", "successToast": "Конфигурацията на кампанията беше успешно актуализирана."}}, "create": {"title": "Създаване на кампания", "description": "Създайте промоционална кампания.", "hint": "Създайте промоционална кампания.", "header": "Създаване на кампания", "successToast": "Кампанията '{{name}}' беше успешно създадена."}, "fields": {"name": "Име", "identifier": "Идентификатор", "start_date": "Начална дата", "end_date": "Крайна дата", "total_spend": "Изразходван бюджет", "total_used": "Използван бюджет", "budget_limit": "Лимит на бюджета", "campaign_id": {"hint": "Само кампании със същия валутен код като промоцията се показват в този списък."}}, "budget": {"create": {"hint": "Създайте бюджет за кампанията.", "header": "Бюджет на кампанията"}, "details": "Бюджет на кампанията", "fields": {"type": "Тип", "currency": "Валута", "limit": "<PERSON>и<PERSON><PERSON><PERSON>", "used": "Използвано"}, "type": {"spend": {"title": "Разходи", "description": "Задайте лимит на общата сума на отстъпките за всички използвани промоции."}, "usage": {"title": "Използване", "description": "Задайте лимит за броя пъти, когато промоцията може да бъде използвана."}}, "edit": {"header": "Редактиране на бюджета на кампанията"}}, "promotions": {"remove": {"title": "Премахване на промоция от кампанията", "description": "Ще премахнете {{count}} промоция(и) от кампанията. Това действие не може да бъде отменено."}, "alreadyAdded": "Тази промоция вече е добавена към кампанията.", "alreadyAddedDiffCampaign": "Тази промоция вече е добавена към друга кампания ({{name}}).", "currencyMismatch": "Валутата на промоцията и кампанията не съвпадат", "toast": {"success": "Успешно добавихте {{count}} промоция(и) към кампанията"}, "add": {"list": {"noRecordsMessage": "Първо създайте промоция."}}, "list": {"noRecordsMessage": "Няма промоции в кампанията."}}, "deleteCampaignWarning": "Ще изтриете кампанията {{name}}. Това действие не може да бъде отменено.", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "Ценови листи", "subtitle": "Създайте промоционални или персонализирани цени за определени условия.", "delete": {"confirmation": "Ще изтриете ценовата листа {{title}}. Това действие не може да бъде отменено.", "successToast": "Ценовата листа {{title}} беше успешно изтрита."}, "create": {"header": "Създаване на ценова листа", "subheader": "Създайте нова ценова листа за управление на цените на вашите продукти.", "tabs": {"details": "Детайли", "products": "Продукти", "prices": "Цени"}, "successToast": "Ценовата листа {{title}} беше успешно създадена.", "products": {"list": {"noRecordsMessage": "Първо създайте продукт."}}}, "edit": {"header": "Редактиране на ценова листа", "successToast": "Ценовата листа {{title}} беше успешно актуализирана."}, "configuration": {"header": "Конфигурация", "edit": {"header": "Редактиране на конфигурацията на ценовата листа", "description": "Редактирайте конфигурацията на ценовата листа.", "successToast": "Конфигурацията на ценовата листа беше успешно актуализирана."}}, "products": {"header": "Продукти", "actions": {"addProducts": "Добавяне на продукти", "editPrices": "Редактиране на цени"}, "delete": {"confirmation_one": "Ще изтриете цените за {{count}} продукт от ценовата листа. Това действие не може да бъде отменено.", "confirmation_other": "Ще изтриете цените за {{count}} продукта от ценовата листа. Това действие не може да бъде отменено.", "successToast_one": "Успешно изтрити цени за {{count}} продукт.", "successToast_other": "Успешно изтрити цени за {{count}} продукта."}, "add": {"successToast": "Цените бяха успешно добавени към ценовата листа."}, "edit": {"successToast": "Цените бяха успешно актуализирани."}}, "fields": {"priceOverrides": {"label": "Заместващи цени", "header": "Заместващи цени"}, "status": {"label": "Статус", "options": {"active": "Активна", "draft": "Чернова", "expired": "Изтекла", "scheduled": "Пла<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "type": {"label": "Тип", "hint": "Изберете типа ценова листа, която искате да създадете.", "options": {"sale": {"label": "Промоция", "description": "Промоционалните цени са временни намаления на продуктите."}, "override": {"label": "Заместваща", "description": "Заместващите цени обикновено се използват за създаване на цени, специфични за клиенти."}}}, "startsAt": {"label": "Ценовата листа има начална дата?", "hint": "Планирайте активирането на ценовата листа в бъдеще."}, "endsAt": {"label": "Ценовата листа има крайна дата?", "hint": "Планирайте деактивирането на ценовата листа в бъдеще."}, "customerAvailability": {"header": "Изберете клиентски групи", "label": "Достъпност за клиенти", "hint": "Изберете за кои клиентски групи да се прилага ценовата листа.", "placeholder": "Търсете клиентски групи", "attribute": "Клиентски групи"}}}, "profile": {"domain": "Профил", "manageYourProfileDetails": "Управлявайте детайлите на профила си.", "fields": {"languageLabel": "Език", "usageInsightsLabel": "Анализ на използването"}, "edit": {"header": "Редактиране на профил", "languageHint": "Езикът, който искате да използвате в администраторския панел. Това няма да промени езика на вашия магазин.", "languagePlaceholder": "Изберете език", "usageInsightsHint": "Споделяйте анализи на използването и помогнете да подобрим Medusa. Можете да прочетете повече за това какво събираме и как го използваме в нашата <0>документация</0>."}, "toast": {"edit": "Промените в профила са запазени"}}, "users": {"domain": "Потребители", "editUser": "Редактиране на потребител", "inviteUser": "Покани потребител", "inviteUserHint": "Поканете нов потребител във вашия магазин.", "sendInvite": "Изпрати покана", "pendingInvites": "Чакащи покани", "deleteInviteWarning": "Ще изтриете поканата за {{email}}. Това действие не може да бъде отменено.", "resendInvite": "Изпрати поканата отново", "copyInviteLink": "Копирай линка за покана", "expiredOnDate": "Изтекла на {{date}}", "validFromUntil": "Ва<PERSON><PERSON><PERSON>на от <0>{{from}}</0> - <1>{{until}}</1>", "acceptedOnDate": "Приета на {{date}}", "inviteStatus": {"accepted": "Приета", "pending": "Чакаща", "expired": "Изтекла"}, "roles": {"admin": "Администратор", "developer": "Разработчик", "member": "<PERSON><PERSON><PERSON>н"}, "list": {"empty": {"heading": "Няма намерени потребители", "description": "След като потребител бъде поканен, ще се появи тук."}, "filtered": {"heading": "Няма резултати", "description": "Няма потребители, които съответстват на текущия филтър."}}, "deleteUserWarning": "Ще изтриете потребителя {{name}}. Това действие не може да бъде отменено.", "deleteUserSuccess": "Потребителят {{name}} беше успешно изтрит", "invite": "Покани"}, "store": {"domain": "Мага<PERSON>ин", "manageYourStoresDetails": "Управлявайте детайлите на вашия магазин", "editStore": "Редактиране на магазин", "defaultCurrency": "Основна валута", "defaultRegion": "Основен регион", "defaultSalesChannel": "Основен канал за продажби", "defaultLocation": "Основно местоположение", "swapLinkTemplate": "Шаблон за линк за размяна", "paymentLinkTemplate": "Шаблон за линк за плащане", "inviteLinkTemplate": "Шаблон за линк за покана", "currencies": "Валути", "addCurrencies": "Добавяне на валути", "enableTaxInclusivePricing": "Включване на цени с ДДС", "disableTaxInclusivePricing": "Изключване на цени с ДДС", "removeCurrencyWarning_one": "Ще премахнете {{count}} валута от магазина си. Уверете се, че сте премахнали всички цени с тази валута преди да продължите.", "removeCurrencyWarning_other": "Ще премахнете {{count}} валути от магазина си. Уверете се, че сте премахнали всички цени с тези валути преди да продължите.", "currencyAlreadyAdded": "Тази валута вече е добавена към вашия магазин.", "edit": {"header": "Редактиране на магазин"}, "toast": {"update": "Магазинът беше успешно актуализиран", "currenciesUpdated": "Валутите бяха успешно актуализирани", "currenciesRemoved": "Валутите бяха успешно премахнати от магазина", "updatedTaxInclusivitySuccessfully": "Цените с ДДС бяха успешно актуализирани"}}, "regions": {"domain": "Региони", "subtitle": "Регион е област, в която продавате продукти. Той може да обхваща множество държави и да има различни данъчни ставки, доставчици и валута.", "createRegion": "Създаване на регион", "createRegionHint": "Управлявайте данъчни ставки и доставчици за група от държави.", "addCountries": "Добавяне на държави", "editRegion": "Редактиране на регион", "countriesHint": "Добавете държавите, включени в този регион.", "deleteRegionWarning": "Ще изтриете региона {{name}}. Това действие не може да бъде отменено.", "removeCountriesWarning_one": "Ще премахнете {{count}} държава от региона. Това действие не може да бъде отменено.", "removeCountriesWarning_other": "Ще премахнете {{count}} държави от региона. Това действие не може да бъде отменено.", "removeCountryWarning": "Ще премахнете държавата {{name}} от региона. Това действие не може да бъде отменено.", "automaticTaxesHint": "Когато е включено, данъците ще се изчисляват само при плащане въз основа на адреса за доставка.", "taxInclusiveHint": "Когато е включено, цените в региона ще включват данъци.", "providersHint": "Добавете кои доставчици на плащания са налични в този регион.", "shippingOptions": "Опции за доставка", "deleteShippingOptionWarning": "Ще изтриете опцията за доставка {{name}}. Това действие не може да бъде отменено.", "return": "Връщане", "outbound": "Изпращане", "priceType": "Тип цена", "flatRate": "Фиксирана цена", "calculated": "Изчислена", "list": {"noRecordsMessage": "Създайте регион за областите, в които продавате."}, "toast": {"delete": "Регионът беше успешно изтрит", "edit": "Редакцията на региона беше запазена", "create": "Регионът беше успешно създаден", "countries": "Държавите в региона бяха успешно актуализирани"}, "shippingOption": {"createShippingOption": "Създаване на опция за доставка", "createShippingOptionHint": "Създайте нова опция за доставка за региона.", "editShippingOption": "Редактиране на опция за доставка", "fulfillmentMethod": "Метод на изпълнение", "type": {"outbound": "Изпращане", "outboundHint": "Използвайте това, ако създавате опция за изпращане на продукти до клиента.", "return": "Връщане", "returnHint": "Използвайте това, ако създавате опция за връщане на продукти от клиента към вас."}, "priceType": {"label": "Тип цена", "flatRate": "Фиксирана цена", "calculated": "Изчислена"}, "availability": {"adminOnly": "Само за администратор", "adminOnlyHint": "Когато е включено, опцията за доставка ще бъде налична само в администраторския панел и няма да се показва в магазина."}, "taxInclusiveHint": "Когато е включено, цената на доставката ще включва данъци.", "requirements": {"label": "Изисквания", "hint": "Посочете изискванията за опцията за доставка."}}}, "taxes": {"domain": "Данъчни региони", "domainDescription": "Управлявайте вашия данъчен регион", "countries": {"taxCountriesHint": "Данъчните настройки се прилагат за изброените държави."}, "settings": {"editTaxSettings": "Редактиране на данъчни настройки", "taxProviderLabel": "Доставчик на данъци", "systemTaxProviderLabel": "Системен доставчик на данъци", "calculateTaxesAutomaticallyLabel": "Автоматично изчисляване на данъци", "calculateTaxesAutomaticallyHint": "Когато е включено, данъчните ставки ще се изчисляват автоматично и ще се прилагат към количките. Когато е изключено, данъците трябва да се изчисляват ръчно при плащане. Ръчните данъци се препоръчват при използване на външни доставчици на данъчни услуги.", "applyTaxesOnGiftCardsLabel": "Прилагане на данъци върху подаръчни карти", "applyTaxesOnGiftCardsHint": "Когато е включено, данъци ще се прилагат към подаръчни карти при плащане. В някои държави се изисква прилагането на данъци към подаръчни карти при покупка.", "defaultTaxRateLabel": "Стандартна данъчна ставка", "defaultTaxCodeLabel": "Стандартен данъчен код"}, "defaultRate": {"sectionTitle": "Стандартна данъчна ставка"}, "taxRate": {"sectionTitle": "Данъчни ставки", "createTaxRate": "Създаване на данъчна ставка", "createTaxRateHint": "Създайте нова данъчна ставка за региона.", "deleteRateDescription": "Ще изтриете данъчната ставка {{name}}. Това действие не може да бъде отменено.", "editTaxRate": "Редактиране на данъчна ставка", "editRateAction": "Редактиране на ставка", "editOverridesAction": "Редактиране на изключения", "editOverridesTitle": "Редактиране на изключения за данъчна ставка", "editOverridesHint": "Посочете изключенията за данъчната ставка.", "deleteTaxRateWarning": "Ще изтриете данъчната ставка {{name}}. Това действие не може да бъде отменено.", "productOverridesLabel": "Изключения за продукти", "productOverridesHint": "Посочете изключенията за данъчната ставка за продукти.", "addProductOverridesAction": "Добавяне на изключения за продукти", "productTypeOverridesLabel": "Изключения за типове продукти", "productTypeOverridesHint": "Посочете изключенията за данъчната ставка за типове продукти.", "addProductTypeOverridesAction": "Добавяне на изключения за типове продукти", "shippingOptionOverridesLabel": "Изключения за опции за доставка", "shippingOptionOverridesHint": "Посочете изключенията за данъчната ставка за опции за доставка.", "addShippingOptionOverridesAction": "Добавяне на изключения за опции за доставка", "productOverridesHeader": "Продукти", "productTypeOverridesHeader": "Типове продукти", "shippingOptionOverridesHeader": "Опции за доставка"}}, "locations": {"domain": "Локации", "editLocation": "Редактиране на локация", "addSalesChannels": "Добавяне на търговски канали", "noLocationsFound": "Няма намерени локации", "selectLocations": "Изберете локации, които складират артикула.", "deleteLocationWarning": "Ще изтриете локацията {{name}}. Това действие не може да бъде отменено.", "removeSalesChannelsWarning_one": "Ще премахнете {{count}} търговски канал от локацията.", "removeSalesChannelsWarning_other": "Ще премахнете {{count}} търговски канала от локацията.", "toast": {"create": "Локацията беше създадена успешно", "update": "Локацията беше актуализирана успешно", "removeChannel": "Търговският канал беше премахнат успешно"}}, "reservations": {"domain": "Резервации", "subtitle": "Управлявайте резервираното количество от складови артикули.", "deleteWarning": "Ще изтриете резервацията. Това действие не може да бъде отменено."}, "salesChannels": {"domain": "Търговски канали", "subtitle": "Управлявайте онлайн и офлайн каналите, на които продавате продуктите си.", "list": {"empty": {"heading": "Няма намерени търговски канали", "description": "След като създадете търговски канал, той ще се появи тук."}, "filtered": {"heading": "Няма резултати", "description": "Няма търговски канали, отговарящи на текущите филтри."}}, "createSalesChannel": "Създаване на търговски канал", "createSalesChannelHint": "Създайте нов търговски канал за продажба на продуктите си.", "enabledHint": "Посочете дали търговският канал е активен.", "removeProductsWarning_one": "Ще премахнете {{count}} продукт от {{sales_channel}}.", "removeProductsWarning_other": "Ще премахнете {{count}} продукта от {{sales_channel}}.", "addProducts": "Добавяне на продукти", "editSalesChannel": "Редактиране на търговски канал", "productAlreadyAdded": "Продуктът вече е добавен към търговския канал.", "deleteSalesChannelWarning": "Ще изтриете търговския канал {{name}}. Това действие не може да бъде отменено.", "toast": {"create": "Търговският канал беше създаден успешно", "update": "Търговският канал беше актуализиран успешно", "delete": "Търговският канал беше изтрит успешно"}, "tooltip": {"cannotDeleteDefault": "Не може да се изтрие основният търговски канал"}, "products": {"list": {"noRecordsMessage": "Няма продукти в този търговски канал."}, "add": {"list": {"noRecordsMessage": "Създайте продукт първо."}}}}, "apiKeyManagement": {"domain": {"publishable": "Публични API ключове", "secret": "Тайни API ключове"}, "subtitle": {"publishable": "Управлявайте API ключовете, използвани във витрината, за да ограничите обхвата на заявките до конкретни търговски канали.", "secret": "Управлявайте API ключовете, използвани за удостоверяване на администраторски потребители в администраторски приложения."}, "status": {"active": "Акти<PERSON><PERSON>н", "revoked": "Отменен"}, "type": {"publishable": "Публичен", "secret": "<PERSON>а<PERSON><PERSON>"}, "create": {"createPublishableHeader": "Създаване на публичен API ключ", "createPublishableHint": "Създайте нов публичен API ключ, за да ограничите обхвата на заявките до конкретни търговски канали.", "createSecretHeader": "Създаване на таен API ключ", "createSecretHint": "Създайте нов таен API ключ за достъп до Medusa API като удостоверен администратор.", "secretKeyCreatedHeader": "Създаден таен ключ", "secretKeyCreatedHint": "Вашият нов таен ключ беше генериран. Копирайте го и го съхранете на сигурно място. Това е единственият път, когато ще бъде показан.", "copySecretTokenSuccess": "Тайният ключ беше копиран в клипборда.", "copySecretTokenFailure": "Неуспешно копиране на тайния ключ в клипборда.", "successToast": "API ключът беше създаден успешно."}, "edit": {"header": "Редактиране на API ключ", "description": "Редактирайте заглавието на API ключа.", "successToast": "API ключът {{title}} беше успешно актуализиран."}, "salesChannels": {"title": "Добавяне на търговски канали", "description": "Добавете търговските канали, до които да има достъп API ключът.", "successToast_one": "{{count}} търговски канал беше успешно добавен към API ключа.", "successToast_other": "{{count}} търговски канала бяха успешно добавени към API ключа.", "alreadyAddedTooltip": "Търговският канал вече е добавен към API ключа.", "list": {"noRecordsMessage": "Няма търговски канали в обхвата на публичния API ключ."}}, "delete": {"warning": "Ще изтриете API ключа {{title}}. Това действие не може да бъде отменено.", "successToast": "API ключът {{title}} беше успешно изтрит."}, "revoke": {"warning": "Ще отнемете API ключа {{title}}. Това действие не може да бъде отменено.", "successToast": "API ключът {{title}} беше успешно отменен."}, "addSalesChannels": {"list": {"noRecordsMessage": "Първо създайте търговски канал."}}, "removeSalesChannel": {"warning": "Ще премахнете търговския канал {{name}} от API ключа. Това действие не може да бъде отменено.", "warningBatch_one": "Ще премахнете {{count}} търговски канал от API ключа. Това действие не може да бъде отменено.", "warningBatch_other": "Ще премахнете {{count}} търговски канала от API ключа. Това действие не може да бъде отменено.", "successToast": "Търговският канал беше успешно премахнат от API ключа.", "successToastBatch_one": "{{count}} търговски канал беше успешно премахнат от API ключа.", "successToastBatch_other": "{{count}} търговски канала бяха успешно премахнати от API ключа."}, "actions": {"revoke": "Отнемане на API ключ", "copy": "Копиране на API ключ", "copySuccessToast": "API ключът беше копиран в клипборда."}, "table": {"lastUsedAtHeader": "Последно използван", "createdAtHeader": "Създаден на"}, "fields": {"lastUsedAtLabel": "Последно използван на", "revokedByLabel": "Отме<PERSON>ен от", "revokedAtLabel": "Отменен на", "createdByLabel": "Създаден от"}}, "returnReasons": {"domain": "Причини за връщане", "subtitle": "Управлявайте причините за върнати артикули.", "calloutHint": "Управлявайте причините за категоризиране на връщанията.", "editReason": "Редактиране на причина за връщане", "create": {"header": "Добавяне на причина за връщане", "subtitle": "Посочете най-честите причини за връщане.", "hint": "Създайте нова причина за връщане за категоризиране на връщанията.", "successToast": "Причината за връщане {{label}} беше успешно създадена."}, "edit": {"header": "Редактиране на причина за връщане", "subtitle": "Редактирайте стойността на причината за връщане.", "successToast": "Причината за връщане {{label}} беше успешно актуализирана."}, "delete": {"confirmation": "Ще изтриете причината за връщане {{label}}. Това действие не може да бъде отменено.", "successToast": "Причината за връщане {{label}} беше успешно изтрита."}, "fields": {"value": {"label": "Стойност", "placeholder": "wrong_size", "tooltip": "Стойността трябва да е уникален идентификатор за причината за връщане."}, "label": {"label": "Етикет", "placeholder": "Грешен размер"}, "description": {"label": "Описание", "placeholder": "Клиентът е получил грешен размер"}}}, "login": {"forgotPassword": "Забравена парола? - <0><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></0>", "title": "Добре дошли в Medusa", "hint": "Впишете се, за да получите достъп до профила си"}, "invite": {"title": "Добре дошли в Medusa", "hint": "Създайте своя профил по-долу", "backToLogin": "Обратно към вписване", "createAccount": "Създаване на профил", "alreadyHaveAccount": "Вече имате профил? - <0>Впишете се</0>", "emailTooltip": "Вашият имейл не може да бъде променен. Ако искате да използвате друг имейл, трябва да се изпрати нова покана.", "invalidInvite": "Поканата е невалидна или е изтекла.", "successTitle": "Вашият профил беше създаден", "successHint": "Започнете с Medusa Admin веднага.", "successAction": "Стартир<PERSON><PERSON>те Medusa Admin", "invalidTokenTitle": "Вашият токен за покана е невалиден", "invalidTokenHint": "Опитайте да поискате нова покана.", "passwordMismatch": "Паролите не съвпадат", "toast": {"accepted": "Поканата беше приета успешно"}}, "resetPassword": {"title": "Нулиране на парола", "hint": "Въведете вашия имейл по-долу и ще ви изпратим инструкции за нулиране на паролата.", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sendResetInstructions": "Изпрати инструкции за нулиране", "backToLogin": "<0>Обратно към вписване</0>", "newPasswordHint": "Изберете нова парола по-долу.", "invalidTokenTitle": "Вашият токен за нулиране е невалиден", "invalidTokenHint": "Опитайте да поискате нова връзка за нулиране.", "expiredTokenTitle": "Вашият токен за нулиране е изтекъл", "goToResetPassword": "Към нулиране на паролата", "resetPassword": "Нулиране на парола", "newPassword": "Нова парола", "repeatNewPassword": "Повторете новата парола", "tokenExpiresIn": "Токенът изтича след <0>{{time}}</0> минути", "successfulRequestTitle": "Успешно изпратен имейл", "successfulRequest": "Изпратихме ви имейл с инструкции за нулиране на паролата. Проверете спам папката, ако не го получите след няколко минути.", "successfulResetTitle": "Паролата е успешно нулирана", "successfulReset": "Моля, впишете се от страницата за вписване.", "passwordMismatch": "Паролите не съвпадат", "invalidLinkTitle": "Вашата връзка за нулиране е невалидна", "invalidLinkHint": "Опитайте отново да нулирате паролата."}, "workflowExecutions": {"domain": "Работни потоци", "subtitle": "Вижте и следете изпълнението на работни потоци във вашето приложение Medusa.", "transactionIdLabel": "ID на транзакция", "workflowIdLabel": "ID на работен поток", "progressLabel": "Напредък", "stepsCompletedLabel_one": "{{completed}} от {{count}} стъпка", "stepsCompletedLabel_other": "{{completed}} от {{count}} стъпки", "list": {"noRecordsMessage": "Все още няма изпълнени работни потоци."}, "history": {"sectionTitle": "История", "runningState": "Изпълнява се...", "awaitingState": "Очаква", "failedState": "Неуспешно", "skippedState": "Пропуснато", "skippedFailureState": "Про<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Грешка)", "definitionLabel": "Дефиниция", "outputLabel": "Резултат", "compensateInputLabel": "Компен<PERSON><PERSON><PERSON><PERSON><PERSON> вход", "revertedLabel": "Върнато", "errorLabel": "Грешка"}, "state": {"done": "Готово", "failed": "Неуспешно", "reverted": "Върнато", "invoking": "Извикване", "compensating": "Компенсиране", "notStarted": "Не е започнато"}, "transaction": {"state": {"waitingToCompensate": "Очаква компенсация"}}, "step": {"state": {"skipped": "Пропуснато", "skippedFailure": "Про<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Грешка)", "dormant": "Неактивно", "timeout": "Времето изтече"}}}, "productTypes": {"domain": "Видове продукти", "subtitle": "Организирайте продуктите си по видове.", "create": {"header": "Създаване на вид продукт", "hint": "Създайте нов вид продукт за категоризиране.", "successToast": "Видът продукт {{value}} беше успешно създаден."}, "edit": {"header": "Редактиране на вид продукт", "successToast": "Видът продукт {{value}} беше успешно актуализиран."}, "delete": {"confirmation": "Ще изтриете вида продукт {{value}}. Това действие не може да бъде отменено.", "successToast": "Видът продукт {{value}} беше успешно изтрит."}, "fields": {"value": "Стойност"}}, "productTags": {"domain": "Продуктови етикети", "create": {"header": "Създаване на продуктов етикет", "subtitle": "Създайте нов продуктов етикет, за да категоризирате вашите продукти.", "successToast": "Продуктовият етикет {{value}} беше успешно създаден."}, "edit": {"header": "Редактиране на продуктов етикет", "subtitle": "Редактирайте стойността на продуктовия етикет.", "successToast": "Продуктовият етикет {{value}} беше успешно актуализиран."}, "delete": {"confirmation": "Ще изтриете продуктовия етикет {{value}}. Това действие не може да бъде отменено.", "successToast": "Продуктовият етикет {{value}} беше успешно изтрит."}, "fields": {"value": "Стойност"}}, "notifications": {"domain": "Известия", "emptyState": {"title": "Няма известия", "description": "В момента нямате известия, но когато получите такива, ще се покажат тук."}, "accessibility": {"description": "Известия за дейности в Medusa ще се показват тук."}}, "errors": {"serverError": "Сървърна грешка - Опитайте отново по-късно.", "invalidCredentials": "Невалиден имейл или парола"}, "statuses": {"scheduled": "План<PERSON><PERSON><PERSON>но", "expired": "Изтекло", "active": "Активно", "inactive": "Неактивно", "draft": "Чернова", "enabled": "Акти<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled": "Деактивирано"}, "labels": {"productVariant": "Продуктов вариант", "prices": "Цени", "available": "Налично", "inStock": "В наличност", "added": "Добавено", "removed": "Премахнато", "from": "От", "to": "До", "beaware": "Имайте предвид", "loading": "Зареждане"}, "fields": {"amount": "Сума", "refundAmount": "Сума за възстановяване", "name": "Име", "default": "По подразбиране", "lastName": "Фамилия", "firstName": "Собствено име", "title": "Заглавие", "customTitle": "Персонализирано заглавие", "manageInventory": "Управление на инвентара", "inventoryKit": "Има инвента<PERSON>ен комплект", "inventoryItems": "Инвентарни артикули", "inventoryItem": "Инвента<PERSON>ен артикул", "requiredQuantity": "Изисквано количество", "description": "Описание", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "password": "Парола", "repeatPassword": "Повторете паролата", "confirmPassword": "Потвърдете паролата", "newPassword": "Нова парола", "repeatNewPassword": "Повторете новата парола", "categories": "Категории", "shippingMethod": "Метод на доставка", "configurations": "Конфигурации", "conditions": "Условия", "category": "Категория", "collection": "Колекция", "discountable": "Подлежи на отстъпка", "handle": "Идентификатор", "subtitle": "Подзаглавие", "by": "От", "item": "Арти<PERSON><PERSON><PERSON>", "qty": "бр.", "limit": "Ограничение", "tags": "Етикети", "type": "Тип", "reason": "Причина", "none": "няма", "all": "всички", "search": "Търсене", "percentage": "Процент", "sales_channels": "Канали за продажби", "customer_groups": "Групи клиенти", "product_tags": "Продуктови етикети", "product_types": "Продуктови типове", "product_collections": "Продуктови колекции", "status": "Статус", "code": "<PERSON>од", "value": "Стойност", "disabled": "Деактивирано", "dynamic": "Ди<PERSON><PERSON><PERSON>чно", "normal": "Нормално", "years": "Годи<PERSON>и", "months": "Месеци", "days": "<PERSON><PERSON>и", "hours": "Часове", "minutes": "Минути", "totalRedemptions": "Общ брой използвания", "countries": "Държави", "paymentProviders": "Доставчици на плащания", "refundReason": "Причина за възстановяване", "fulfillmentProviders": "Доставчици на изпълнение", "fulfillmentProvider": "Доставчик на изпълнение", "providers": "Доставчици", "availability": "Наличност", "inventory": "Инвентар", "optional": "По избор", "note": "Бележка", "automaticTaxes": "Автоматични данъци", "taxInclusivePricing": "Цени с включени данъци", "currency": "Валута", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "address2": "Апарта<PERSON><PERSON>нт, етаж и др.", "city": "<PERSON>р<PERSON><PERSON>", "postalCode": "Пощенски код", "country": "Държава", "state": "Щат", "province": "Провинция", "company": "Компания", "phone": "Телефон", "metadata": "Метаданни", "selectCountry": "Изберете държава", "products": "Продукти", "variants": "Варианти", "orders": "Поръчки", "account": "Профил", "total": "Обща сума", "paidTotal": "Общо платено", "totalExclTax": "Общо без ДДС", "subtotal": "Междинна сума", "shipping": "Доставка", "outboundShipping": "Изходяща доставка", "returnShipping": "Връщане на доставка", "tax": "Дан<PERSON>к", "created": "Създадено", "key": "<PERSON><PERSON><PERSON><PERSON>", "customer": "Кли<PERSON><PERSON>т", "date": "Дата", "order": "Поръчка", "fulfillment": "Изпълнение", "provider": "Доставчик", "payment": "Плащане", "items": "Артикули", "salesChannel": "Канал за продажби", "region": "Регион", "discount": "Отстъпка", "role": "Роля", "sent": "Изпратено", "salesChannels": "Канали за продажби", "product": "Продукт", "createdAt": "Създадено на", "updatedAt": "Актуализирано на", "revokedAt": "Отменено на", "true": "Да", "false": "Не", "giftCard": "Пода<PERSON><PERSON><PERSON>на карта", "tag": "Етикет", "dateIssued": "Дата на издаване", "issuedDate": "Дата на издаване", "expiryDate": "Срок на валидност", "price": "Цена", "priceTemplate": "Цена {{regionOrCurrency}}", "height": "Висо<PERSON>ина", "width": "Ши<PERSON><PERSON><PERSON>", "length": "Дъл<PERSON><PERSON>на", "weight": "Тегло", "midCode": "MID код", "hsCode": "HS код", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "Количество на склад", "barcode": "Баркод", "countryOfOrigin": "Държава на произход", "material": "Материал", "thumbnail": "Миниатюра", "sku": "SKU", "managedInventory": "Управляван инвентар", "allowBackorder": "Позволи предварителни поръчки", "inStock": "В наличност", "location": "Локация", "quantity": "Количество", "variant": "Вар<PERSON><PERSON><PERSON>т", "id": "ID", "parent": "Родител", "minSubtotal": "Мини<PERSON><PERSON><PERSON>на междинна сума", "maxSubtotal": "Максимална междинна сума", "shippingProfile": "Профил за доставка", "summary": "Обобщение", "details": "Детайли", "label": "Етикет", "rate": "Процент", "requiresShipping": "Изисква доставка", "unitPrice": "Единична цена", "startDate": "Начална дата", "endDate": "Крайна дата", "draft": "Чернова", "values": "Стойности"}, "quotes": {"domain": "Оферти", "title": "Оферти", "subtitle": "Управлявайте оферти и предложения на клиенти", "noQuotes": "Няма намерени оферти", "noQuotesDescription": "В момента няма оферти. Създайте една от магазина.", "table": {"id": "ID на офертата", "customer": "Кли<PERSON><PERSON>т", "status": "Статус", "company": "Компания", "amount": "Сума", "createdAt": "Създадено", "updatedAt": "Актуа<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>о", "actions": "Действия"}, "status": {"pending_merchant": "Очаква търговец", "pending_customer": "Очаква клиент", "merchant_rejected": "Отхвърлено от търговец", "customer_rejected": "Отхвърлено от клиент", "accepted": "Прието", "unknown": "Неизвестно"}, "actions": {"sendQuote": "Изпрати оферта", "rejectQuote": "Отхвърли оферта", "viewOrder": "Виж поръчка"}, "details": {"header": "Детайли на офертата", "quoteSummary": "Обобщение на офертата", "customer": "Кли<PERSON><PERSON>т", "company": "Компания", "items": "Артикули", "total": "Общо", "subtotal": "Междинна сума", "shipping": "Доставка", "tax": "Дан<PERSON>к", "discounts": "Отстъпки", "originalTotal": "Първоначална сума", "quoteTotal": "Обща сума на офертата", "messages": "Съобщения", "actions": "Действия", "sendMessage": "Изпрати съобщение", "send": "Изпрати", "pickQuoteItem": "Избери артикул от офертата", "selectQuoteItem": "Изберете артикул от офертата за коментар", "selectItem": "Избери артикул", "manage": "Управлявай", "phone": "Телефон", "spendingLimit": "Лимит за разходи", "name": "Име", "manageQuote": "Управлявай оферта", "noItems": "Няма артикули в тази оферта", "noMessages": "Няма съобщения за тази оферта"}, "items": {"title": "Продукт", "quantity": "Количество", "unitPrice": "Единична цена", "total": "Общо"}, "messages": {"admin": "Администратор", "customer": "Кли<PERSON><PERSON>т", "placeholder": "Напишете съобщението си тук..."}, "filters": {"status": "Филтрирай по статус"}, "confirmations": {"sendTitle": "Изпрати оферта", "sendDescription": "Сигурни ли сте, че искате да изпратите тази оферта на клиента?", "rejectTitle": "Отхвърли оферта", "rejectDescription": "Сигурни ли сте, че искате да отхвърлите тази оферта?"}, "acceptance": {"message": "Офертата беше приета"}, "toasts": {"sendSuccess": "Офертата беше успешно изпратена на клиента", "sendError": "Неуспешно изпращане на офертата", "rejectSuccess": "Офертата на клиента беше успешно отхвърлена", "rejectError": "Неуспешно отхвърляне на офертата", "messageSuccess": "Съобщението беше успешно изпратено на клиента", "messageError": "Неуспешно изпращане на съобщението", "updateSuccess": "Офертата беше успешно актуализирана"}, "manage": {"overridePriceHint": "Замени първоначалната цена за този артикул", "updatePrice": "Актуализирай цена"}}, "companies": {"domain": "Компании", "title": "Компании", "subtitle": "Управлявайте бизнес отношения", "noCompanies": "Няма намерени компании", "noCompaniesDescription": "Създайте първата си компания, за да започнете.", "notFound": "Компанията не е намерена", "table": {"name": "Име", "phone": "Телефон", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "employees": "Служители", "customerGroup": "Група клиенти", "actions": "Действия"}, "fields": {"name": "Име на компанията", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "phone": "Телефон", "website": "Уебс<PERSON><PERSON>т", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>р<PERSON><PERSON>", "state": "Щат", "zip": "Пощенски код", "zipCode": "Пощенски код", "country": "Държава", "currency": "Валута", "logoUrl": "URL на логото", "description": "Описание", "employees": "Служители", "customerGroup": "Група клиенти", "approvalSettings": "Настройки за одобрение"}, "placeholders": {"name": "Въведете име на компанията", "email": "Въведете имейл адрес", "phone": "Въведете телефонен номер", "website": "Въведете URL на уебсайта", "address": "Въведете адрес", "city": "Въведете град", "state": "Въведете щат", "zip": "Въведете пощенски код", "logoUrl": "Въведете URL на логото", "description": "Въведете описание на компанията", "selectCountry": "Изберете държава", "selectCurrency": "Изберете валута"}, "validation": {"nameRequired": "Името на компанията е задължително", "emailRequired": "Имейлът е задължителен", "emailInvalid": "Невалиден имейл адрес", "addressRequired": "Адресът е задължителен", "cityRequired": "Градът е задължителен", "stateRequired": "Щатът е задължителен", "zipRequired": "Пощенският код е задължителен"}, "create": {"title": "Създай компания", "description": "Създайте нова компания за управление на бизнес отношения.", "submit": "Създай компания"}, "edit": {"title": "Редактирай компания", "submit": "Актуализ<PERSON><PERSON><PERSON>й компания"}, "details": {"actions": "Действия"}, "approvals": {"requiresAdminApproval": "Изисква одобрение от администратор", "requiresSalesManagerApproval": "Изисква одобрение от мениджър продажби", "noApprovalRequired": "Не се изисква одобрение"}, "deleteWarning": "Това ще изтрие окончателно компанията и всички свързани данни.", "approvalSettings": {"title": "Настройки за одобрение", "requiresAdminApproval": "Изисква одобрение от администратор", "requiresSalesManagerApproval": "Изисква одобрение от мениджър продажби", "requiresAdminApprovalDesc": "Поръчките от тази компания изискват одобрение от администратор преди обработка", "requiresSalesManagerApprovalDesc": "Поръчките от тази компания изискват одобрение от мениджър продажби преди обработка", "updateSuccess": "Настройките за одобрение бяха успешно актуализирани", "updateError": "Неуспешна актуализация на настройките за одобрение"}, "customerGroup": {"title": "Управление на група клиенти", "hint": "Назначете тази компания към група клиенти за прилагане на групови цени и разрешения.", "name": "Име на група клиенти", "groupName": "Група клиенти", "actions": "Действия", "add": "Добавяне", "remove": "Премахване", "description": "Управлявайте групи клиенти за тази компания", "noGroups": "Няма налични групи клиенти", "addSuccess": "Компанията беше успешно добавена към група клиенти", "addError": "Неуспешно добавяне на компанията към група клиенти", "removeSuccess": "Компанията беше успешно премахната от група клиенти", "removeError": "Неуспешно премахване на компанията от група клиенти"}, "actions": {"edit": "Редактирай компания", "editDetails": "Редактирай детайли", "manageCustomerGroup": "Управлявай група клиенти", "approvalSettings": "Настройки за одобрение", "delete": "Изтрий компания", "confirmDelete": "Потвърди изтриване"}, "delete": {"title": "Изтрий компания", "description": "Сигурни ли сте, че искате да изтриете тази компания? Това действие не може да бъде отменено."}, "employees": {"title": "Служители", "noEmployees": "Няма намерени служители за тази компания", "name": "Име", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "phone": "Телефон", "role": "Роля", "spendingLimit": "Лимит за разходи", "admin": "Администратор", "employee": "Служител", "add": "Добави служител", "create": {"title": "Създай служител", "success": "Служителят беше успешно създаден", "error": "Неуспешно създаване на служител"}, "form": {"details": "Подробна информация", "permissions": "Разрешения", "firstName": "Име", "lastName": "Фамилия", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "phone": "Телефон", "spendingLimit": "Лимит за разходи", "adminAccess": "Достъп на администратор", "isAdmin": "Е администратор", "isAdminDesc": "Предостави администраторски привилегии на този служител", "isAdminTooltip": "Администраторите могат да управляват настройките на компанията и други служители", "firstNamePlaceholder": "Въведете име", "lastNamePlaceholder": "Въведете фамилия", "emailPlaceholder": "Въведете имейл адрес", "phonePlaceholder": "Въведете телефонен номер", "spendingLimitPlaceholder": "Въведете лимит за разходи", "save": "Запази", "saving": "Запазване..."}, "delete": {"confirmation": "Сигурни ли сте, че искате да изтриете този служител?", "success": "Служителят беше успешно изтрит"}, "edit": {"title": "Редактир<PERSON>й служител"}, "toasts": {"updateSuccess": "Служителят беше успешно актуализиран", "updateError": "Неуспешна актуализация на служител"}}, "toasts": {"createSuccess": "Компанията беше успешно създадена", "createError": "Неуспешно създаване на компания", "updateSuccess": "Компанията беше успешно актуализирана", "updateError": "Неуспешна актуализация на компания", "deleteSuccess": "Компанията беше успешно изтрита", "deleteError": "Неуспешно изтриване на компания"}}, "approvals": {"domain": "Одобрения", "title": "Одобрения", "subtitle": "Управлявайте работни процеси за одобрение", "noApprovals": "Няма намерени одобрения", "noApprovalsDescription": "В момента няма одобрения за преглед.", "table": {"id": "ID", "type": "Тип", "company": "Компания", "customer": "Кли<PERSON><PERSON>т", "amount": "Сума", "status": "Статус", "createdAt": "Създадено"}, "status": {"pending": "Чакащо", "approved": "Одобрено", "rejected": "Отхвърлено", "expired": "Изтекло", "unknown": "Неизвестно"}, "details": {"header": "Детайли на одобрението", "summary": "Обобщение на одобрението", "company": "Компания", "customer": "Кли<PERSON><PERSON>т", "order": "Поръчка", "amount": "Сума", "updatedAt": "Актуа<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>о", "reason": "Причина", "actions": "Действия"}, "actions": {"approve": "Одобри", "reject": "Отхвърли", "confirmApprove": "Потвърди одобрение", "confirmReject": "Потвърди отхвърляне", "reasonPlaceholder": "Въведете причина (по избор)..."}, "filters": {"status": "Филтрирай по статус"}, "toasts": {"approveSuccess": "Успешно одобрено", "approveError": "Неуспешно одобрение", "rejectSuccess": "Успешно отхвърлено", "rejectError": "Неуспешно отхвърляне"}}, "dateTime": {"years_one": "Год<PERSON><PERSON>", "years_other": "Годи<PERSON>и", "months_one": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "months_other": "Месеци", "weeks_one": "Седмица", "weeks_other": "Седмици", "days_one": "<PERSON><PERSON><PERSON>", "days_other": "<PERSON><PERSON>и", "hours_one": "<PERSON><PERSON><PERSON>", "hours_other": "Часове", "minutes_one": "Минута", "minutes_other": "Минути", "seconds_one": "Секунда", "seconds_other": "Секунди"}}