{"$schema": "./$schema.json", "general": {"ascending": "Αύξουσα", "descending": "Φθίνουσα", "add": "Προσθήκη", "start": "Έναρξη", "end": "Τέλος", "open": "Άνοιγμα", "close": "Κλείσιμο", "apply": "Εφαρμογή", "range": "Εύρ<PERSON>", "search": "Αναζήτηση", "of": "από", "results": "αποτελέσματα", "pages": "σελίδες", "next": "Επόμενο", "prev": "Προηγούμενο", "is": "είναι", "timeline": "Χρον<PERSON><PERSON><PERSON>γι<PERSON>", "success": "Επιτυχία", "warning": "Προειδοποίηση", "tip": "Συμβουλή", "error": "Σφάλμα", "select": "Επιλογή", "selected": "Επιλεγμένο", "enabled": "Ενεργοποιημένο", "disabled": "Απενεργοποιημένο", "expired": "Ληγμένο", "active": "Ενεργό", "revoked": "Ανακληθέν", "new": "Νέο", "modified": "Τροποποιημένο", "added": "Προστέθηκε", "removed": "Αφαιρέθηκε", "admin": "Διαχειριστής", "store": "Κατάστημα", "details": "Λεπτομέρειες", "items_one": "{{count}} αντικείμενο", "items_other": "{{count}} αντικείμενα", "countSelected": "{{count}} επιλεγμένα", "countOfTotalSelected": "{{count}} από {{total}} επιλεγμένα", "plusCount": "+ {{count}}", "plusCountMore": "+ {{count}} περισσότερα", "areYouSure": "Είστε σίγουροι;", "noRecordsFound": "Δεν βρέθηκαν εγγραφές", "typeToConfirm": "Πληκτρολογήστε {val} για επιβεβαίωση:", "noResultsTitle": "Δεν υπάρχουν αποτελέσματα", "noResultsMessage": "Δοκιμάστε να αλλάξετε τα φίλτρα ή την αναζήτηση", "noSearchResults": "Δεν υπάρχουν αποτελέσματα αναζήτησης", "noSearchResultsFor": "Δεν υπάρχουν αποτελέσματα αναζήτησης για '{{query}}'", "noRecordsTitle": "Δεν υπάρχουν εγγραφές", "noRecordsMessage": "Δεν υπάρχουν εγγραφές προς εμφάνιση", "unsavedChangesTitle": "Είστε σίγουροι ότι θέλετε να αποχωρήσετε από αυτήν τη φόρμα;", "unsavedChangesDescription": "Έχετε μη αποθηκευμένες αλλαγές που θα χαθούν αν βγείτε από αυτήν τη φόρμα.", "includesTaxTooltip": "Οι τιμές σε αυτήν τη στήλη περιλαμβάνουν ΦΠΑ.", "excludesTaxTooltip": "Οι τιμές σε αυτήν τη στήλη δεν περιλαμβάνουν ΦΠΑ.", "noMoreData": "Δεν υπάρχουν άλλα δεδομένα", "actions": "Ενέργειες"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}} κλειδί", "numberOfKeys_other": "{{count}} κλειδιά", "drawer": {"header_one": "JSON · {{count}} κλειδί", "header_other": "JSON · {{count}} κλειδιά", "description": "Δείτε τα δεδομένα JSON για αυτό το αντικείμενο."}}, "metadata": {"header": "Μεταδεδομένα", "numberOfKeys_one": "{{count}} κλειδί", "numberOfKeys_other": "{{count}} κλειδιά", "edit": {"header": "Επεξεργασία Μεταδεδομένων", "description": "Επεξεργαστείτε τα μεταδεδομένα για αυτό το αντικείμενο.", "successToast": "Τα μεταδεδομένα ενημερώθηκαν με επιτυχία.", "actions": {"insertRowAbove": "Εισαγωγή γραμμής από πάνω", "insertRowBelow": "Εισαγωγή γραμμής από κάτω", "deleteRow": "Διαγραφή γραμμής"}, "labels": {"key": "Κλειδί", "value": "Τιμή"}, "complexRow": {"label": "Ορισμένες γραμμές είναι απενεργοποιημένες", "description": "Αυτό το αντικείμενο περιέχει μη πρωτογενή μεταδεδομένα, όπως πίνακες ή αντικείμενα, που δεν μπορούν να επεξεργαστούν εδώ. Για να επεξεργαστείτε τις απενεργοποιημένες γραμμές, χρησιμοποιήστε το API απευθείας.", "tooltip": "Αυτή η γραμμή είναι απενεργοποιημένη επειδή περιέχει μη πρωτογενή δεδομένα."}}}, "validation": {"mustBeInt": "Η τιμή πρέπει να είναι ακέραιος αριθμός.", "mustBePositive": "Η τιμή πρέπει να είναι θετικός αριθμός."}, "actions": {"save": "Αποθήκευση", "saveAsDraft": "Αποθήκευση ως πρόχειρο", "copy": "Αντιγραφή", "copied": "Αντιγράφηκε", "duplicate": "Διπλότυπο", "publish": "Δημοσίευση", "create": "Δημιουργία", "delete": "Διαγραφή", "remove": "Αφαίρεση", "revoke": "Ανάκληση", "cancel": "Ακύρωση", "forceConfirm": "Επιβεβαίωση", "continueEdit": "Συνέχεια επεξεργασίας", "enable": "Ενεργοποίηση", "disable": "Απενεργοποίηση", "undo": "Αναίρεση", "complete": "Ολοκλήρωση", "viewDetails": "Προβολή λεπτομερειών", "back": "Πίσω", "close": "Κλείσιμο", "showMore": "Εμφάνιση περισσότερων", "continue": "Συνέχεια", "continueWithEmail": "Συνέχεια με Email", "idCopiedToClipboard": "Το ID αντιγράφηκε στο πρόχειρο", "addReason": "Προσθήκη λόγου", "addNote": "Προσθήκη σημείωσης", "reset": "Επαναφορά", "confirm": "Επιβεβαίωση", "edit": "Επεξεργασία", "addItems": "Προσθήκη αντικειμένων", "download": "Λή<PERSON>η", "clear": "Εκκαθάριση", "clearAll": "Εκκαθάριση όλων", "apply": "Εφαρμογή", "add": "Προσθήκη", "select": "Επιλογή", "browse": "Περιήγηση", "logout": "Αποσύνδεση", "hide": "Απόκρυψη", "export": "Εξαγωγή", "import": "Εισαγωγή", "cannotUndo": "Αυτή η ενέργεια δεν μπορεί να αναιρεθεί"}, "operators": {"in": "Σε"}, "app": {"search": {"label": "Αναζήτηση", "title": "Αναζήτηση", "description": "Αναζητήστε σε ολόκληρο το κατά<PERSON><PERSON>η<PERSON><PERSON> σας, συμπεριλαμβανομένων παραγγελιών, προϊόντων, πελ<PERSON><PERSON><PERSON><PERSON> και άλλων.", "allAreas": "Όλες οι περιοχές", "navigation": "Πλοήγηση", "openResult": "Άνοιγμα αποτελέσματος", "showMore": "Εμφάνιση περισσότερων", "placeholder": "Μετάβαση ή εύρεση οτιδήποτε...", "noResultsTitle": "Δεν βρέθηκαν αποτελέσματα", "noResultsMessage": "Δεν μπορέσαμε να βρούμε τίποτα που να ταιριάζει με την αναζήτησή σας.", "emptySearchTitle": "Πληκτρολογήστε για αναζήτηση", "emptySearchMessage": "Εισαγάγετε μια λέξη-κλειδ<PERSON> ή φράση για να εξερευνήσετε.", "loadMore": "Φόρτωση {{count}} περισσότερων", "groups": {"all": "Όλες οι περιοχές", "customer": "Πελάτες", "customerGroup": "Ομάδες πελατών", "product": "Προϊόντα", "productVariant": "Παραλλαγ<PERSON>ς προϊόντων", "inventory": "Απόθεμα", "reservation": "Κρατήσεις", "category": "Κατηγορίες", "collection": "Συλλογές", "order": "Παραγγελίες", "promotion": "Προωθήσεις", "campaign": "Καμπάνιες", "priceList": "Τιμο<PERSON>ατάλογοι", "user": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON>", "region": "Περιοχ<PERSON>ς", "taxRegion": "Φορολογικ<PERSON>ς περιοχές", "returnReason": "Λόγοι επιστροφής", "salesChannel": "Κανάλια πωλήσεων", "productType": "Τύποι προϊόντων", "productTag": "Ετικέτες προϊόντων", "location": "Τοποθεσίες", "shippingProfile": "Προ<PERSON><PERSON><PERSON> αποστολής", "publishableApiKey": "Κλειδιά API που μπορούν να δημοσιευτούν", "secretApiKey": "Μυστικά κλειδιά API", "command": "Εντολές", "navigation": "Πλοήγηση"}}, "keyboardShortcuts": {"pageShortcut": "Μετάβαση σε", "settingShortcut": "Ρυθμίσεις", "commandShortcut": "Εντολές", "then": "έπειτα", "navigation": {"goToOrders": "Παραγγελίες", "goToProducts": "Προϊόντα", "goToCollections": "Συλλογές", "goToCategories": "Κατηγορίες", "goToCustomers": "Πελάτες", "goToCustomerGroups": "Ομάδες πελατών", "goToInventory": "Απόθεμα", "goToReservations": "Κρατήσεις", "goToPriceLists": "Τιμο<PERSON>ατάλογοι", "goToPromotions": "Προωθήσεις", "goToCampaigns": "Καμπάνιες"}, "settings": {"goToSettings": "Ρυθμίσεις", "goToStore": "Κατάστημα", "goToUsers": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON>", "goToRegions": "Περιοχ<PERSON>ς", "goToTaxRegions": "Φορολογικ<PERSON>ς περιοχές", "goToSalesChannels": "Κανάλια πωλήσεων", "goToProductTypes": "Τύποι προϊόντων", "goToLocations": "Τοποθεσίες", "goToPublishableApiKeys": "Κλειδιά API που μπορούν να δημοσιευτούν", "goToSecretApiKeys": "Μυστικά κλειδιά API", "goToWorkflows": "Ροές εργασίας", "goToProfile": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON>", "goToReturnReasons": "Λόγοι επιστροφής"}}, "menus": {"user": {"documentation": "Τεκμηρίωση", "changelog": "Αρχ<PERSON><PERSON><PERSON> αλ<PERSON>ν", "shortcuts": "Συντομεύσεις", "profileSettings": "Ρυθμίσεις προφίλ", "theme": {"label": "Θέμα", "dark": "Σκούρο", "light": "Ανοιχτόχρωμο", "system": "Σύστημα"}}, "store": {"label": "Κατάστημα", "storeSettings": "Ρυθμίσεις καταστήματος"}, "actions": {"logout": "Αποσύνδεση"}}, "nav": {"accessibility": {"title": "Πλοήγηση", "description": "Μενού πλοήγησης για τον πίνακα ελέγχου."}, "common": {"extensions": "Επεκτάσεις"}, "main": {"store": "Κατάστημα", "storeSettings": "Ρυθμίσεις καταστήματος"}, "settings": {"header": "Ρυθμίσεις", "general": "Γενικά", "developer": "Προγραμματιστής", "myAccount": "Ο λογαριασμός μου"}}}, "dataGrid": {"columns": {"view": "Προβολή", "resetToDefault": "Επανα<PERSON><PERSON><PERSON><PERSON> στις προεπιλογές", "disabled": "Η αλλαγή των ορατών στηλών είναι απενεργοποιημένη."}, "shortcuts": {"label": "Συντομεύσεις", "commands": {"undo": "Αναίρεση", "redo": "Επανάληψη", "copy": "Αντιγραφή", "paste": "Επικόλληση", "edit": "Επεξεργασία", "delete": "Διαγραφή", "clear": "Εκκαθάριση", "moveUp": "Μετακίνηση προς τα πάνω", "moveDown": "Μετακίνηση προς τα κάτω", "moveLeft": "Μετακίνηση προς τα αριστερά", "moveRight": "Μετακίνηση προς τα δεξιά", "moveTop": "Μετακίνηση στην κορυφή", "moveBottom": "Μετακίνηση στο κάτω μέρος", "selectDown": "Επιλογή προς τα κάτω", "selectUp": "Επιλογή προς τα πάνω", "selectColumnDown": "Επιλογή στήλης προς τα κάτω", "selectColumnUp": "Επιλογή στήλης προς τα πάνω", "focusToolbar": "Εστίαση στη γραμμή εργαλείων", "focusCancel": "Εστίαση στην ακύρωση"}}, "errors": {"fixError": "Διόρθωση σφάλματος", "count_one": "{{count}} σφάλμα", "count_other": "{{count}} σφάλματα"}}, "filters": {"sortLabel": "Ταξινόμηση", "filterLabel": "Φιλτράρισμα", "searchLabel": "Αναζήτηση", "date": {"today": "Σήμερα", "lastSevenDays": "Τελευταίες 7 ημέρες", "lastThirtyDays": "Τελευταίες 30 ημέρες", "lastNinetyDays": "Τελευταίες 90 ημέρες", "lastTwelveMonths": "Τελευταίοι 12 μήνες", "custom": "Προσαρμοσμένο", "from": "Από", "to": "Έως", "starting": "Αρχ<PERSON><PERSON>οντας", "ending": "Τελειώνοντας"}, "compare": {"lessThan": "Λιγότερο από", "greaterThan": "Μεγαλύτερο από", "exact": "Α<PERSON><PERSON>ιβ<PERSON>ς", "range": "Εύρ<PERSON>", "lessThanLabel": "λιγότερο από {{value}}", "greaterThanLabel": "μεγαλύτερο από {{value}}", "andLabel": "και"}, "sorting": {"alphabeticallyAsc": "A με Ω", "alphabeticallyDesc": "Z με Α", "dateAsc": "Νεότερο πρώτα", "dateDesc": "Παλιότερο πρώτα"}, "radio": {"yes": "Ναι", "no": "Όχι", "true": "Αληθές", "false": "Ψευδ<PERSON>ς"}, "addFilter": "Προσθήκη φίλτρου"}, "errorBoundary": {"badRequestTitle": "400 - <PERSON><PERSON><PERSON><PERSON> αίτημα", "badRequestMessage": "Το αίτημα δεν έγινε κατανοητό από τον διακομιστή λόγω λανθασμένης σύνταξης.", "notFoundTitle": "404 - Δεν υπάρχει σελίδα σε αυτήν τη διεύθυνση", "notFoundMessage": "Ελέγξτε τη διεύθυνση URL και προσπαθήστε ξανά ή χρησιμοποιήστε τη γραμμή αναζήτησης για να βρείτε αυτό που ψάχνετε.", "internalServerErrorTitle": "500 - Εσωτερικ<PERSON> σφάλμα διακομιστή", "internalServerErrorMessage": "Παρου<PERSON><PERSON><PERSON><PERSON><PERSON>ηκε ένα μη αναμενόμενο σφάλμα στον διακομιστή. Παρακ<PERSON><PERSON><PERSON> προσπαθήστε ξανά αργότερα.", "defaultTitle": "Παρου<PERSON><PERSON>ά<PERSON>τηκε σφάλμα", "defaultMessage": "Παρου<PERSON><PERSON><PERSON><PERSON><PERSON>η<PERSON><PERSON> ένα μη αναμενόμενο σφάλμα κατά την απόδοση αυτής της σελίδας.", "noMatchMessage": "Η σελίδα που αναζητάτε δεν υπάρχει.", "backToDashboard": "Επιστροφή στον πίνακα ελέγχου"}, "addresses": {"title": "Διευθύνσεις", "shippingAddress": {"header": "Διεύθυνση αποστολής", "editHeader": "Επεξεργασία διεύθυνσης αποστολής", "editLabel": "Διεύθυνση αποστολής", "label": "Διεύθυνση αποστολής"}, "billingAddress": {"header": "Διεύθυνση χρέωσης", "editHeader": "Επεξεργασία διεύθυνσης χρέωσης", "editLabel": "Διεύθυνση χρέωσης", "label": "Διεύθυνση χρέωσης", "sameAsShipping": "Ίδια με τη διεύθυνση αποστολής"}, "contactHeading": "Επαφή", "locationHeading": "Τοποθεσία"}, "email": {"editHeader": "Επεξεργασία Email", "editLabel": "Email", "label": "Email"}, "transferOwnership": {"header": "Μεταφ<PERSON><PERSON><PERSON> ιδιοκτησίας", "label": "Μεταφ<PERSON><PERSON><PERSON> ιδιοκτησίας", "details": {"order": "Λεπτομέρειες παραγγελίας", "draft": "Λεπτομέρειες προχείρου"}, "currentOwner": {"label": "Τρέχων κάτοχος", "hint": "Ο τρέχων κάτοχος της παραγγελίας."}, "newOwner": {"label": "<PERSON><PERSON><PERSON> κ<PERSON>το<PERSON>ος", "hint": "Ο νέος κάτοχος στον οποίο θα μεταφερθεί η παραγγελία."}, "validation": {"mustBeDifferent": "Ο νέος κάτοχος πρέπει να είναι διαφορετικός από τον τρέχοντα κάτοχο.", "required": "Απαιτε<PERSON><PERSON><PERSON><PERSON> νέος κάτοχος."}}, "sales_channels": {"availableIn": "Διαθέσιμο σε {{x}} από {{y}} κανάλια πωλήσεων"}, "products": {"domain": "Προϊόντα", "list": {"noRecordsMessage": "Δημιουργήστε το πρώτο σας προϊόν για να ξεκινήσετε τις πωλήσεις."}, "edit": {"header": "Επεξεργασία προϊόντος", "description": "Επεξεργαστείτε τις λεπτομέρειες του προϊόντος.", "successToast": "Το προϊόν {{title}} ενημερώθηκε με επιτυχία."}, "create": {"title": "Δημιουργία προϊόντος", "description": "Δημιουργήστε ένα νέο προϊόν.", "header": "Γενικά", "tabs": {"details": "Λεπτομέρειες", "organize": "Οργάνωση", "variants": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς", "inventory": "<PERSON>ιτ αποθέματος"}, "errors": {"variants": "Επιλέξτε τουλάχιστον μία παραλλαγή.", "options": "Δημιουργήστε τουλάχιστον μία επιλογή.", "uniqueSku": "Το SKU πρέπει να είναι μοναδικό."}, "inventory": {"heading": "<PERSON>ιτ αποθέματος", "label": "Προσθέστε αντικείμενα αποθέματος στο κιτ αποθέματος της παραλλαγής.", "itemPlaceholder": "Επιλέξτε αντικείμενο αποθέματος", "quantityPlaceholder": "Πόσα από αυτά χρειάζονται για το κιτ;"}, "variants": {"header": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς", "subHeadingTitle": "Ναι, αυτό είναι ένα προϊόν με παραλλαγές", "subHeadingDescription": "Όταν δεν είναι επιλεγμένο, θα δημιουργήσουμε μια προεπιλεγμένη παραλλαγή για εσάς", "optionTitle": {"placeholder": "Μέγεθος"}, "optionValues": {"placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Μεσα<PERSON><PERSON>, Μεγάλο"}, "productVariants": {"label": "Παραλλαγ<PERSON>ς προϊόντων", "hint": "Αυτή η κατάταξη θα επηρεάσει τη σειρά των παραλλαγών στο κατάστημά σας.", "alert": "Προσθέστε επιλογές για να δημιουργήσετε παραλλαγές.", "tip": "Οι παραλλαγές που δεν έχουν επιλεγεί δεν θα δημιουργηθούν. Μπορείτε πάντα να δημιουργείτε και να επεξεργάζεστε παραλλαγές αργότερα, αλλά αυτή η λίστα ταιριάζει με τις παραλλαγές στις επιλογές προϊόντων σας."}, "productOptions": {"label": "Επιλογές προϊόντων", "hint": "Ορίστε τις επιλογές για το προϊόν, π.χ. χρώμα, μέγεθος κ.λπ."}}, "successToast": "Το προϊόν {{title}} δημιουργήθηκε με επιτυχία."}, "export": {"header": "Εξαγωγή λίστας προϊόντων", "description": "Εξαγάγετε τη λίστα προϊόντων σε ένα αρχείο CSV.", "success": {"title": "Επεξεργαζόμαστε την εξαγωγή σας", "description": "Η εξαγωγή δεδομένων ενδέχεται να διαρκέσει μερικά λεπτά. Θα σας ειδοποιήσουμε όταν ολοκληρωθεί."}, "filters": {"title": "Φίλτρα", "description": "Εφαρμόστε φίλτρα στην επισκόπηση του πίνακα για να προσαρμόσετε αυτήν την προβολή"}, "columns": {"title": "Στήλες", "description": "Προσαρμόστε τα δεδομένα που εξάγονται για να καλύψουν συγκεκριμένες ανάγκες"}}, "import": {"header": "Εισαγωγή λίστας προϊόντων", "uploadLabel": "Εισαγωγή προϊόντων", "uploadHint": "Σύρετε και αποθέστε ένα αρχείο CSV ή κάντε κλικ για μεταφόρτωση", "description": "Εισαγάγετε προϊόντα παρέχοντας ένα αρχείο CSV σε μια προκαθορισμένη μορφή", "template": {"title": "Δεν είστε σίγουροι για το πώς να οργανώσετε τη λίστα σας;", "description": "Κατεβάστε το παρακάτω πρότυπο για να βεβαιωθείτε ότι ακολουθείτε τη σωστή μορφή."}, "upload": {"title": "Μεταφόρτωση αρχείου CSV", "description": "Μέσω εισαγωγών μπορείτε να προσθέσετε ή να ενημερώσετε προϊόντα. Για να ενημερώσετε υπάρχοντα προϊόντα, πρέπει να χρησιμοποιήσετε το υπάρχον handle και ID, για να ενημερώσετε υπάρχουσες παραλλαγές, πρέπει να χρησιμοποιήσετε το υπάρχον ID. Θα σας ζητηθεί επιβεβαίωση πριν εισαγάγουμε προϊόντα.", "preprocessing": "Προεπεξεργασία...", "productsToCreate": "Θα δημιουργηθούν προϊόντα", "productsToUpdate": "Θα ενημερωθούν προϊόντα"}, "success": {"title": "Επεξεργ<PERSON>ζόμαστε την εισαγωγή σας", "description": "Η εισαγωγή δεδομένων ενδέχεται να διαρκέσει λίγο. Θα σας ειδοποιήσουμε όταν ολοκληρωθεί."}}, "deleteWarning": "Είστε έτοιμοι να διαγράψετε το προϊόν {{title}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "variants": {"header": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς", "empty": {"heading": "Καμία παραλλαγή", "description": "Δεν υπάρχουν παραλλαγές για αυτό το προϊόν."}, "filtered": {"heading": "Κανένα αποτέλεσμα", "description": "Καμία παραλλαγή δεν ταιριάζει με τα φίλτρα."}}, "attributes": "<PERSON><PERSON><PERSON><PERSON><PERSON>τηριστικά", "editAttributes": "Επεξεργασ<PERSON>α χαρακτηριστικών", "editOptions": "Επεξεργασία επιλογών", "editPrices": "Επεξεργασία τιμών", "media": {"label": "Μέσα", "editHint": "Προσθέστε μέσα στο προϊόν για να το παρουσιάσετε στο κατάστημά σας.", "makeThumbnail": "Ορισμός ως μικρογραφία", "uploadImagesLabel": "Μεταφόρτωση εικόνων", "uploadImagesHint": "Σύρετε και αποθέστε εικόνες εδώ ή κάντε κλικ για μεταφόρτωση.", "invalidFileType": "Το '{{name}}' δεν είναι υποστηριζόμενος τύπος αρχείου. Οι υποστηριζόμενοι τύποι αρχείων είναι: {{types}}.", "failedToUpload": "Αποτυχ<PERSON><PERSON> μεταφόρτωσης των μέσων που προστέθηκαν. Παρακαλ<PERSON> προσπαθήστε ξανά.", "deleteWarning_one": "Είστε έτοιμοι να διαγράψετε {{count}} εικόνα. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "deleteWarning_other": "Είστε έτοιμοι να διαγράψετε {{count}} εικόνες. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "deleteWarningWithThumbnail_one": "Είστε έτοιμοι να διαγράψετε {{count}} εικ<PERSON>ν<PERSON>, συμπεριλαμβανομένης της μικρογραφίας. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "deleteWarningWithThumbnail_other": "Είστε έτοιμοι να διαγράψετε {{count}} ει<PERSON><PERSON><PERSON><PERSON><PERSON>, συμπεριλαμβανομένης της μικρογραφίας. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "thumbnailTooltip": "Μικρογραφία", "galleryLabel": "Γκαλερί", "downloadImageLabel": "Λήψη τρέχου<PERSON>ας εικόνας", "deleteImageLabel": "Διαγραφή τρέχουσας εικόνας", "emptyState": {"header": "Δεν υπάρχουν μέσα ακόμα", "description": "Προσθέστε μέσα στο προϊόν για να το παρουσιάσετε στο κατάστημά σας.", "action": "Προσθήκη μέσων"}, "successToast": "Τα μέσα ενημερώθηκαν με επιτυχία."}, "discountableHint": "Όταν δεν είναι επιλεγμένο, οι εκπτώσεις δεν θα εφαρμόζονται σε αυτό το προϊόν.", "noSalesChannels": "Δεν είναι διαθέσιμο σε κανένα κανάλι πωλήσεων", "variantCount_one": "{{count}} παραλλαγή", "variantCount_other": "{{count}} παραλλαγές", "deleteVariantWarning": "Είστε έτοιμοι να διαγράψετε την παραλλαγή {{title}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "productStatus": {"draft": "Πρόχειρο", "published": "Δημοσιευμένο", "proposed": "Προτεινόμενο", "rejected": "Απορριφθέν"}, "fields": {"title": {"label": "Τίτλος", "hint": "Δώστε στο προϊόν σας έναν σύντομο και σαφή τίτλο.<0/>50-60 χαρακτήρες είναι το συνιστώμενο μήκος για τις μηχανές αναζήτησης.", "placeholder": "Χειμερινό μπουφάν"}, "subtitle": {"label": "Υπότιτλος", "placeholder": "Ζεστό και άνετο"}, "handle": {"label": "<PERSON><PERSON>", "tooltip": "Το handle χρησιμοποιείται για ανα<PERSON><PERSON><PERSON><PERSON> στο προϊόν στο κατάστημά σας. Εάν δεν έχει καθοριστεί, το handle θα δημιουργηθεί από τον τίτλο του προϊόντος.", "placeholder": "χειμωνιάτικο-μπουφάν"}, "description": {"label": "Περιγραφή", "hint": "Δώστε στο προϊόν σας μια σύντομη και σαφή περιγραφή.<0/>120-160 χαρακτήρες είναι το συνιστώμενο μήκος για τις μηχανές αναζήτησης.", "placeholder": "Ένα ζεστό και άνετο μπουφάν"}, "discountable": {"label": "Με δυνατότητα έκπτωσης", "hint": "Όταν δεν είναι επιλεγμένο, οι εκπτώσεις δεν θα εφαρμόζονται σε αυτό το προϊόν"}, "shipping_profile": {"label": "Προ<PERSON><PERSON><PERSON> αποστολής", "hint": "Σύνδεση προϊόντος με προφίλ αποστολής"}, "type": {"label": "Τύπος"}, "collection": {"label": "Συλλογή"}, "categories": {"label": "Κατηγορίες"}, "tags": {"label": "Ετικέτες"}, "sales_channels": {"label": "Κανάλια πωλήσεων", "hint": "Αυτό το προϊόν θα είναι διαθέσιμο μόνο στο προεπιλεγμένο κανάλι πωλήσεων εάν δεν τροποποιηθεί."}, "countryOrigin": {"label": "Χώρα προέλευσης"}, "material": {"label": "Υλικό"}, "width": {"label": "<PERSON>λ<PERSON><PERSON><PERSON>"}, "length": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "height": {"label": "Ύψος"}, "weight": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options": {"label": "Επιλογές προϊόντων", "hint": "Οι επιλογές χρησιμοποιούνται για τον ορισμό του χρώματος, του μεγέθους κ.λπ. του προϊόντος", "add": "Προσθήκη επιλογής", "optionTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> επιλογής", "optionTitlePlaceholder": "Χρώμα", "variations": "Παρα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (χωρισμένες με κόμμα)", "variantionsPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Πρά<PERSON><PERSON>νο"}, "variants": {"label": "Παραλλαγ<PERSON>ς προϊόντων", "hint": "Οι παραλλαγές που δεν έχουν επιλεγεί δεν θα δημιουργηθούν, Αυτή η κατάταξη θα επηρεάσει τον τρόπο με τον οποίο κατατάσσονται οι παραλλαγές στο frontend σας."}, "mid_code": {"label": "Κωδικός MID"}, "hs_code": {"label": "Κωδικός HS"}}, "variant": {"edit": {"header": "Επεξεργα<PERSON>ία παραλλαγής", "success": "Η παραλλαγή προϊόντος επεξεργάστηκε με επιτυχία"}, "create": {"header": "Λεπτομέρειες παραλλαγής"}, "deleteWarning": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτήν την παραλλαγή;", "pricesPagination": "1 - {{current}} από {{total}} τιμές", "tableItemAvailable": "{{availableCount}} διαθέσιμα", "tableItem_one": "{{availableCount}} διαθέσιμα σε {{locationCount}} τοποθεσία", "tableItem_other": "{{availableCount}} διαθέσιμα σε {{locationCount}} τοποθεσίες", "inventory": {"notManaged": "Δεν διαχειρίζεται", "manageItems": "Διαχείριση αντικειμένων αποθέματος", "notManagedDesc": "Το απόθεμα δεν διαχειρίζεται για αυτήν την παραλλαγή. Ενεργοποιήστε τη 'Διαχείριση αποθέματος' για να παρακολουθείτε το απόθεμα της παραλλαγής.", "manageKit": "Διαχείριση κιτ αποθέματος", "navigateToItem": "Μετάβαση στο αντικείμενο αποθέματος", "actions": {"inventoryItems": "Μετάβαση στο αντικείμενο αποθέματος", "inventoryKit": "Εμφάνιση αντικειμένων αποθέματος"}, "inventoryKit": "<PERSON>ιτ αποθέματος", "inventoryKitHint": "Αποτελείται αυτή η παραλλαγή από πολλά αντικείμενα αποθέματος;", "validation": {"itemId": "Επιλέξτε αντικείμενο αποθέματος.", "quantity": "Απαιτ<PERSON><PERSON><PERSON><PERSON><PERSON> ποσότητα. Εισαγάγετε έναν θετικό αριθμό."}, "header": "Απόθεμα & Διαχείριση", "editItemDetails": "Επεξερ<PERSON><PERSON><PERSON><PERSON><PERSON> λεπτομερειών αντικειμένου", "manageInventoryLabel": "Διαχείριση αποθέματος", "manageInventoryHint": "Όταν είναι ενεργοποιημένη, θα αλλάξουμε την ποσότητα αποθέματος για εσάς όταν δημιουργούνται παραγγελίες και επιστροφές.", "allowBackordersLabel": "Να επιτρέπονται οι παραγγελίες εκτός αποθέματος", "allowBackordersHint": "Όταν είναι ενεργοποιημένη, οι πελάτες μπορούν να αγοράσουν την παραλλαγή ακόμα και αν δεν υπάρχει διαθέσιμη ποσότητα.", "toast": {"levelsBatch": "Τα επίπεδα αποθέματος ενημερώθηκαν.", "update": "Το αντικείμενο αποθέματος ενημερώθηκε με επιτυχία.", "updateLevel": "Το επίπεδο αποθέματος ενημερώθηκε με επιτυχία.", "itemsManageSuccess": "Τα αντικείμενα αποθέματος ενημερώθηκαν με επιτυχία."}}}, "options": {"header": "Επιλογές", "edit": {"header": "Επεξεργασία επιλογής", "successToast": "Η επιλογή {{title}} ενημερώθηκε με επιτυχία."}, "create": {"header": "Δημιουργία επιλογής", "successToast": "Η επιλογή {{title}} δημιουργήθηκε με επιτυχία."}, "deleteWarning": "Είστε έτοιμοι να διαγράψετε την επιλογή προϊόντος: {{title}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί."}, "organization": {"header": "Οργάνωση", "edit": {"header": "Επεξεργασία οργάνωσης", "toasts": {"success": "Ενημερώθηκε με επιτυχία η οργάνωση του {{title}}."}}}, "stock": {"heading": "Διαχείριση επιπέδων και τοποθεσιών αποθέματος προϊόντων", "description": "Ενημέρωση των επιπέδων αποθεμάτων για όλες τις παραλλαγές του προϊόντος.", "loading": "Περιμένετε, αυτό μπορεί να πάρει λίγο χρόνο...", "tooltips": {"alreadyManaged": "Αυτό το στοιχεί<PERSON> αποθέματος είναι ήδη επεξεργάσιμο στο {{title}}.", "alreadyManagedWithSku": "Αυτό το στοιχεί<PERSON> αποθέματος είναι ήδη επεξεργάσιμο στο {{title}} ({{sku}})."}}, "shippingProfile": {"header": "Διαμόρφωση αποστολής", "edit": {"header": "Διαμόρφωση Αποστολής", "toasts": {"success": "Η ενημέρωση του προφίλ αποστολής για το {{title}} ολοκληρώθηκε με επιτυχία."}}, "create": {"errors": {"required": "Το προφίλ αποστολής είναι υποχρεωτικό"}}}, "toasts": {"delete": {"success": {"header": "Το προϊόν διαγράφηκε", "description": "Το {{title}} διαγράφηκε με επιτυχία."}, "error": {"header": "Αποτυχία διαγραφής προϊόντος"}}}}, "collections": {"domain": "Συλλογές", "subtitle": "Οργανώστε τα προϊόντα σε συλλογές.", "createCollection": "Δημιουργία συλλογής", "createCollectionHint": "Δημιουργήστε μια νέα συλλογή για να οργανώσετε τα προϊόντα σας.", "createSuccess": "Η συλλογή δημιουργήθηκε με επιτυχία.", "editCollection": "Επεξεργασία συλλογής", "handleTooltip": "Το handle χρησιμοποιείται για ανα<PERSON><PERSON><PERSON><PERSON> στη συλλογή στο κατάστημά σας. Εάν δεν έχει καθοριστεί, το handle θα δημιουργηθεί από τον τίτλο της συλλογής.", "deleteWarning": "Είστε έτοιμοι να διαγράψετε τη συλλογή {{title}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "removeSingleProductWarning": "Είστε έτοιμοι να αφαιρέσετε το προϊόν {{title}} από τη συλλογή. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "removeProductsWarning_one": "Είστε έτοιμοι να αφαιρέσετε {{count}} προϊόν από τη συλλογή. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "removeProductsWarning_other": "Είστε έτοιμοι να αφαιρέσετε {{count}} προϊόντα από τη συλλογή. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "products": {"list": {"noRecordsMessage": "Δεν υπάρχουν προϊόντα στη συλλογή."}, "add": {"successToast_one": "Το προϊόν προστέθηκε με επιτυχία στη συλλογή.", "successToast_other": "Τα προϊόντα προστέθηκαν με επιτυχία στη συλλογή."}, "remove": {"successToast_one": "Το προϊόν αφαιρέθηκε με επιτυχία από τη συλλογή.", "successToast_other": "Τα προϊόντα αφαιρέθηκαν με επιτυχία από τη συλλογή."}}}, "categories": {"domain": "Κατηγορίες", "subtitle": "Οργανώστε τα προϊόντα σε κατηγορίες και διαχειριστείτε την κατάταξη και την ιεραρχία αυτών των κατηγοριών.", "create": {"header": "Δημιουργ<PERSON>α κατηγορίας", "hint": "Δημιουργήστε μια νέα κατηγορία για να οργανώσετε τα προϊόντα σας.", "tabs": {"details": "Λεπτομέρειες", "organize": "Οργάνωση κατάταξης"}, "successToast": "Η κατηγορία {{name}} δημιουργήθηκε με επιτυχία."}, "edit": {"header": "Επεξεργασία κατηγορίας", "description": "Επεξεργαστείτε την κατηγορία για να ενημερώσετε τις λεπτομέρειές της.", "successToast": "Η κατηγορία ενημερώθηκε με επιτυχία."}, "delete": {"confirmation": "Είστε έτοιμοι να διαγράψετε την κατηγορία {{name}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "successToast": "Η κατηγορία {{name}} διαγράφηκε με επιτυχία."}, "products": {"add": {"disabledTooltip": "Το προϊόν είναι ήδη σε αυτήν την κατηγορία.", "successToast_one": "Προστέθηκε {{count}} προϊόν στην κατηγορία.", "successToast_other": "Προστέθηκαν {{count}} προϊόντα στην κατηγορία."}, "remove": {"confirmation_one": "Είστε έτοιμοι να αφαιρέσετε {{count}} προϊόν από την κατηγορία. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "confirmation_other": "Είστε έτοιμοι να αφαιρέσετε {{count}} προϊόντα από την κατηγορία. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "successToast_one": "Αφαιρέθηκε {{count}} προϊόν από την κατηγορία.", "successToast_other": "Αφαιρέθηκαν {{count}} προϊόντα από την κατηγορία."}, "list": {"noRecordsMessage": "Δεν υπάρχουν προϊόντα στην κατηγορία."}}, "organize": {"header": "Οργάνωση", "action": "Επεξεργα<PERSON><PERSON>α κατάταξης"}, "fields": {"visibility": {"label": "Ορατότητα", "internal": "Εσωτερική", "public": "Δημόσια"}, "status": {"label": "Κατάσταση", "active": "Ενεργή", "inactive": "Ανενεργή"}, "path": {"label": "Διαδρομή", "tooltip": "Εμφάνιση της πλήρους διαδρομής της κατηγορίας."}, "children": {"label": "Παιδιά"}, "new": {"label": "Νέο"}}}, "inventory": {"domain": "Απόθεμα", "subtitle": "Διαχειριστείτε τα αντικείμενα αποθέματος σας", "reserved": "Κρατημένο", "available": "Διαθέσιμο", "locationLevels": "Τοποθεσίες", "associatedVariants": "Συσχετισμένες παραλλαγές", "manageLocations": "Διαχείριση τοποθεσιών", "deleteWarning": "Είστε έτοιμοι να διαγράψετε ένα αντικείμενο αποθέματος. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "editItemDetails": "Επεξερ<PERSON><PERSON><PERSON><PERSON><PERSON> λεπτομερειών αντικειμένου", "create": {"title": "Δημιουργ<PERSON><PERSON> αντικειμένου αποθέματος", "details": "Λεπτομέρειες", "availability": "Διαθεσιμότητα", "locations": "Τοποθεσίες", "attributes": "<PERSON><PERSON><PERSON><PERSON><PERSON>τηριστικά", "requiresShipping": "Απαιτεί αποστολή", "requiresShippingHint": "Απαιτεί το αντικείμενο αποθέματος αποστολή;", "successToast": "Το αντικείμενο αποθέματος δημιουργήθηκε με επιτυχία."}, "reservation": {"header": "Κράτηση του {{itemName}}", "editItemDetails": "Επεξεργασία κράτησης", "lineItemId": "ID στοιχείου γραμμής", "orderID": "ID παραγγελίας", "description": "Περιγραφή", "location": "Τοποθεσία", "inStockAtLocation": "Σε απόθεμα σε αυτήν την τοποθεσία", "availableAtLocation": "Διαθέσιμο σε αυτήν την τοποθεσία", "reservedAtLocation": "Κρατημ<PERSON>νο σε αυτήν την τοποθεσία", "reservedAmount": "Ποσό κράτησης", "create": "Δημιουργία κράτησης", "itemToReserve": "Αντικείμενο προς κράτηση", "quantityPlaceholder": "Πόσο θέλετε να κρατήσετε;", "descriptionPlaceholder": "Τι είδους κράτηση είναι αυτή;", "successToast": "Η κράτηση δημιουργήθηκε με επιτυχία.", "updateSuccessToast": "Η κράτηση ενημερώθηκε με επιτυχία.", "deleteSuccessToast": "Η κράτηση διαγράφηκε με επιτυχία.", "errors": {"noAvaliableQuantity": "Η τοποθεσία αποθέματος δεν έχει διαθέσιμη ποσότητα.", "quantityOutOfRange": "Η ελάχιστη ποσότητα είναι 1 και η μέγιστη ποσότητα είναι {{max}}"}}, "adjustInventory": {"errors": {"stockedQuantity": "Η αποθηκευμένη ποσότητα δεν μπορεί να ενημερωθεί σε λιγότερο από την κρατημένη ποσότητα των {{quantity}}."}}, "toast": {"updateLocations": "Οι τοποθεσίες ενημερώθηκαν με επιτυχία.", "updateLevel": "Το επίπεδο αποθέματος ενημερώθηκε με επιτυχία.", "updateItem": "Το αντικείμενο αποθέματος ενημερώθηκε με επιτυχία."}, "stock": {"title": "Ενημέρωση επιπέδων αποθέματος", "description": "Ενημέρωση των επιπέδων αποθέματος για τα επιλεγμένα στοιχεία.", "action": "Επεξεργα<PERSON><PERSON>α επιπέδων αποθέματος", "placeholder": "Μη ενεργοποιημένο", "disablePrompt_one": "Πρόκειται να απενεργοποιήσετε {{count}} επίπεδο τοποθεσίας. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "disablePrompt_other": "Πρόκειται να απενεργοποιήσετε {{count}} επίπεδα τοποθεσίας. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "disabledToggleTooltip": "Δεν είναι δυνατή η απενεργοποίηση: εκκαθαρίστε την εισερχόμενη ή/και την δεσμευμένη ποσότητα πριν την απενεργοποίηση.", "successToast": "Τα επίπεδα αποθέματος ενημερώθηκαν με επιτυχία."}}, "giftCards": {"domain": "Δωρ<PERSON><PERSON><PERSON><PERSON>τες", "editGiftCard": "Επεξεργα<PERSON><PERSON>α δωροκάρτας", "createGiftCard": "Δημιουργ<PERSON><PERSON> δωροκάρτας", "createGiftCardHint": "Δημιουργήστε χειροκίνητα μια δωροκάρτα που μπορεί να χρησιμοποιηθεί ως μέθοδος πληρωμής στο κατάστημά σας.", "selectRegionFirst": "Επιλέξτε μια περιοχή πρώτα", "deleteGiftCardWarning": "Είστε έτοιμοι να διαγράψετε τη δωροκάρτα {{code}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "balanceHigherThanValue": "Το υπόλοιπο δεν μπορεί να είναι υψηλότερο από το αρχικό ποσό.", "balanceLowerThanZero": "Το υπόλοιπο δεν μπορεί να είναι αρνητικό.", "expiryDateHint": "Οι χώρες έχουν διαφορετικούς νόμους σχετικά με τις ημερομηνίες λήξης των δωροκαρτών. Βεβαιωθείτε ότι έχετε ελέγξει τους τοπικούς κανονισμούς πριν ορίσετε μια ημερομηνία λήξης.", "regionHint": "Η αλλαγή της περιοχής της δωροκάρτας θα αλλάξει επίσης το νόμισμά της, επηρε<PERSON><PERSON><PERSON>ντας ενδεχομένως την χρηματική της αξία.", "enabledHint": "Καθορίστε εάν η δωροκάρτα είναι ενεργοποιημένη ή απενεργοποιημένη.", "balance": "Υπόλοιπο", "currentBalance": "Τρέχον υπόλοιπο", "initialBalance": "Αρχικό υπόλοιπο", "personalMessage": "Προσωπικό μήνυμα", "recipient": "Παρα<PERSON><PERSON><PERSON><PERSON>ης"}, "customers": {"domain": "Πελάτες", "list": {"noRecordsMessage": "Οι πελάτες σας θα εμφανίζονται εδώ."}, "create": {"header": "Δημιουρ<PERSON><PERSON><PERSON> πελάτη", "hint": "Δημιουρ<PERSON><PERSON><PERSON><PERSON><PERSON> έναν νέο πελάτη και διαχειριστείτε τα στοιχεία του.", "successToast": "Ο πελάτης {{email}} δημιουργήθηκε με επιτυχία."}, "groups": {"label": "Ομάδες πελατών", "remove": "Είστε σίγουροι ότι θέλετε να αφαιρέσετε τον πελάτη από την ομάδα πελατών \"{{name}}\";", "removeMany": "Είστε σίγουροι ότι θέλετε να αφαιρέσετε τον πελάτη από τις ακόλουθες ομάδες πελατών: {{groups}}?", "alreadyAddedTooltip": "Ο πελάτης είναι ήδη σε αυτήν την ομάδα πελατών.", "list": {"noRecordsMessage": "Αυτ<PERSON>ς ο πελάτης δεν ανήκει σε καμία ομάδα."}, "add": {"success": "Ο πελάτης προστέθηκε σε: {{groups}}.", "list": {"noRecordsMessage": "Δημιουργήστε πρώτα μια ομάδα πελατών."}}, "removed": {"success": "Ο πελάτης αφαιρέθηκε από: {{groups}}.", "list": {"noRecordsMessage": "Δημιουργήστε πρώτα μια ομάδα πελατών."}}}, "edit": {"header": "Επεξεργα<PERSON><PERSON>α πελάτη", "emailDisabledTooltip": "Η διεύθυνση email δεν μπορεί να αλλάξει για εγγεγραμμένους πελάτες.", "successToast": "Ο πελάτης {{email}} ενημερώθηκε με επιτυχία."}, "delete": {"title": "Διαγρα<PERSON><PERSON> πελάτη", "description": "Είστε έτοιμοι να διαγράψετε τον πελάτη {{email}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "successToast": "Ο πελάτης {{email}} διαγράφηκε με επιτυχία."}, "fields": {"guest": "Επισκέπτης", "registered": "Εγγεγραμμένος", "groups": "Ομάδες"}, "registered": "Εγγεγραμμένος", "guest": "Επισκέπτης", "hasAccount": "Έχει λογαριασμό"}, "customerGroups": {"domain": "Ομάδες πελατών", "subtitle": "Οργανώστε τους πελάτες σε ομάδες. Οι ομάδες μπορούν να έχουν διαφορετικές προσφορές και τιμές.", "list": {"empty": {"heading": "No customer groups", "description": "There are no customer groups to display."}, "filtered": {"heading": "No results", "description": "No customer groups match the current filter criteria."}}, "create": {"header": "Δημιουργ<PERSON><PERSON> ομάδας πελατών", "hint": "Δημιουργήστε μια νέα ομάδα πελατών για να κατηγοριοποιήσετε τους πελάτες σας.", "successToast": "Η ομάδα πελατών {{name}} δημιουργήθηκε με επιτυχία."}, "edit": {"header": "Επεξεργα<PERSON><PERSON><PERSON> ομάδας πελατών", "successToast": "Η ομάδα πελατών {{name}} ενημερώθηκε με επιτυχία."}, "delete": {"title": "Διαγρα<PERSON><PERSON> ομάδας πελατών", "description": "Είστε έτοιμοι να διαγράψετε την ομάδα πελατών {{name}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "successToast": "Η ομάδα πελατών {{name}} διαγράφηκε με επιτυχία."}, "customers": {"alreadyAddedTooltip": "Ο πελάτης έχει ήδη προστεθεί στην ομάδα.", "add": {"successToast_one": "Ο πελάτης προστέθηκε με επιτυχία στην ομάδα.", "successToast_other": "Οι πελάτες προστέθηκαν με επιτυχία στην ομάδα.", "list": {"noRecordsMessage": "Δημιουργήστε πρώτα έναν πελάτη."}}, "remove": {"title_one": "Αφαίρεση πελάτη", "title_other": "Αφαίρεση πελατών", "description_one": "Είστε έτοιμοι να αφαιρέσετε {{count}} πελάτη από την ομάδα πελατών. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "description_other": "Είστε έτοιμοι να αφαιρέσετε {{count}} πελάτες από την ομάδα πελατών. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί."}, "list": {"noRecordsMessage": "Αυτή η ομάδα δεν έχει πελάτες."}}}, "orders": {"domain": "Παραγγελίες", "claim": "Απαίτηση", "exchange": "Ανταλλαγή", "return": "Επιστροφή", "cancelWarning": "Είστε έτοιμοι να ακυρώσετε την παραγγελία {{id}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "orderCanceled": "Η παραγγελία ακυρώθηκε επιτυχώς.", "onDateFromSalesChannel": "{{date}} από {{salesChannel}}", "list": {"noRecordsMessage": "Οι παραγγελίες σας θα εμφανίζονται εδώ."}, "status": {"not_paid": "Μη πληρωμένο", "pending": "Σε εκκρεμότητα", "completed": "Ολοκληρώθηκε", "draft": "Πρόχειρο", "archived": "Αρχειοθετημένο", "canceled": "Ακυρώθηκε", "requires_action": "Απαιτείται ενέργεια"}, "summary": {"requestReturn": "Αίτημα επιστροφής", "allocateItems": "Εκχώρηση αντικειμένων", "editOrder": "Επεξεργασία παραγγελίας", "editOrderContinue": "Συνέχεια επεξεργασίας παραγγελίας", "inventoryKit": "Αποτελείται από {{count}}x αντικείμενα αποθέματος", "itemTotal": "Σύνολο αντικειμένων", "shippingTotal": "Σύνολο αποστολής", "discountTotal": "Σύν<PERSON><PERSON><PERSON>πτωσης", "taxTotalIncl": "Σύνολο ΦΠΑ (συμπεριλαμβάνεται)", "itemSubtotal": "Μερικό σύνολο αντικειμένων", "shippingSubtotal": "Μερικό σύνολο αποστολής", "discountSubtotal": "Μερικό σύνολο έκπτωσης", "taxTotal": "Σύνολο ΦΠΑ"}, "transfer": {"title": "Μεταφ<PERSON><PERSON><PERSON> ιδιοκτησίας", "requestSuccess": "Το αίτημα μεταφοράς παραγγελίας στάλθηκε στο: {{email}}.", "currentOwner": "Τρέχων κάτοχος", "newOwner": "<PERSON><PERSON><PERSON> κ<PERSON>το<PERSON>ος", "currentOwnerDescription": "Ο πελάτης που σχετίζεται αυτήν τη στιγμή με αυτήν την παραγγελία.", "newOwnerDescription": "Ο πελάτης στον οποίο θα μεταφερθεί αυτή η παραγγελία."}, "payment": {"title": "Πληρωμές", "isReadyToBeCaptured": "Η πληρωμή <0/> ε<PERSON><PERSON><PERSON><PERSON> έτοιμη για καταγραφή.", "totalPaidByCustomer": "Συνολικό ποσό που καταβλήθηκε από τον πελάτη", "capture": "Καταγραφή πληρωμής", "capture_short": "Καταγραφή", "refund": "Επιστροφή χρημάτων", "markAsPaid": "Σημείωση ως πληρωμένη", "statusLabel": "Κατάσταση πληρωμής", "statusTitle": "Κατάσταση πληρωμής", "status": {"notPaid": "Μη πληρωμένη", "authorized": "Εξουσιοδοτημένη", "partiallyAuthorized": "Με<PERSON>ι<PERSON><PERSON><PERSON> εξουσιοδοτημένη", "awaiting": "Σε αναμονή", "captured": "Καταγεγραμμένη", "partiallyRefunded": "Με<PERSON>ι<PERSON><PERSON><PERSON> επιστραφέντα", "partiallyCaptured": "Μερικ<PERSON><PERSON> καταγεγραμμένη", "refunded": "Επιστραφέντα", "canceled": "Ακυρωμένη", "requiresAction": "Απαιτείται ενέργεια"}, "capturePayment": "Η πληρωμή των {{amount}} θα καταγραφεί.", "capturePaymentSuccess": "Η πληρωμή των {{amount}} καταγράφηκε με επιτυχία", "markAsPaidPayment": "Η πληρωμή των {{amount}} θα σημειωθεί ως πληρωμένη.", "markAsPaidPaymentSuccess": "Η πληρωμή των {{amount}} σημειώθηκε με επιτυχία ως πληρωμένη", "createRefund": "Δημιουρ<PERSON><PERSON><PERSON> επιστροφής χρημάτων", "refundPaymentSuccess": "Η επιστροφή χρημάτων του ποσού {{amount}} ολοκληρώθηκε με επιτυχία", "createRefundWrongQuantity": "Η ποσότητα πρέπει να είναι αριθμός μεταξύ 1 και {{number}}", "refundAmount": "Επιστροφή χρημάτων {{ amount }}", "paymentLink": "Αντιγραφ<PERSON> συνδέσμου πληρωμής για {{ amount }}", "selectPaymentToRefund": "Επιλέξτε πληρωμή για επιστροφή χρημάτων"}, "edits": {"title": "Επεξεργασία παραγγελίας", "confirm": "Επιβεβαίωση επεξεργασίας", "confirmText": "Είστε έτοιμοι να επιβεβαιώσετε μια επεξεργασία παραγγελίας. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "cancel": "Ακύρωση επεξεργασίας", "currentItems": "Τρέχοντα αντικείμενα", "currentItemsDescription": "Προσαρμόστε την ποσότητα των αντικειμένων ή αφαιρέστε τα.", "addItemsDescription": "Μπορείτε να προσθέσετε νέα αντικείμενα στην παραγγελία.", "addItems": "Προσθήκη αντικειμένων", "amountPaid": "Ποσό που καταβλήθηκε", "newTotal": "Νέο σύνολο", "differenceDue": "Διαφορ<PERSON> οφειλόμενη", "create": "Επεξεργασία παραγγελίας", "currentTotal": "Τρέχον σύνολο", "noteHint": "Προσθέστε μια εσωτερική σημείωση για την επεξεργασία", "cancelSuccessToast": "Η επεξεργασία παραγγελίας ακυρώθηκε", "createSuccessToast": "Το αίτημα επεξεργασίας παραγγελίας δημιουργήθηκε", "activeChangeError": "Υπάρχει ήδη ενεργή αλλαγή παραγγελίας στην παραγγελία (επιστροφή, απαίτηση, ανταλλαγή κ.λπ.). Ολοκληρώστε ή ακυρώστε την αλλαγή πριν επεξεργαστείτε την παραγγελία.", "panel": {"title": "Ζητήθηκε επεξεργασία παραγγελίας", "titlePending": "Η επεξεργασία παραγγελίας εκκρεμεί"}, "toast": {"canceledSuccessfully": "Η επεξεργασία παραγγελίας ακυρώθηκε", "confirmedSuccessfully": "Η επεξεργασία παραγγελίας επιβεβαιώθηκε"}, "validation": {"quantityLowerThanFulfillment": "Δεν είναι δυνατή η ρύθμιση της ποσότητας σε μικρότερη ή ίση με την εκπληρωμένη ποσότητα"}}, "edit": {"email": {"title": "Επεξεργασία email", "requestSuccess": "Το email παραγγελίας ενημερώθηκε σε {{email}}."}, "shippingAddress": {"title": "Επεξεργασία διεύθυνσης αποστολής", "requestSuccess": "Η διεύθυνση αποστολής παραγγελίας ενημερώθηκε."}, "billingAddress": {"title": "Επεξεργασία διεύθυνσης χρέωσης", "requestSuccess": "Η διεύθυνση χρέωσης παραγγελίας ενημερώθηκε."}}, "returns": {"create": "Δημιουργ<PERSON>α επιστροφής", "confirm": "Επιβεβαίωση επιστροφής", "confirmText": "Είστε έτοιμοι να επιβεβαιώσετε μια επιστροφή. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "inbound": "Εισερχόμενα", "outbound": "Εξερχόμενα", "sendNotification": "Αποστολή ειδοποίησης", "sendNotificationHint": "Ειδοποιήστε τον πελάτη για την επιστροφή.", "returnTotal": "Συνολικό ποσό επιστροφής", "inboundTotal": "Συνολικό ποσό εισερχομένων", "refundAmount": "Ποσ<PERSON> επιστροφής χρημάτων", "outstandingAmount": "Εκκρεμές ποσό", "reason": "Λόγ<PERSON>", "reasonHint": "Επιλέξτε γιατί ο πελάτης θέλει να επιστρέψει αντικείμενα.", "note": "Σημείωση", "noInventoryLevel": "<PERSON><PERSON><PERSON><PERSON><PERSON> επίπεδο αποθέματος", "noInventoryLevelDesc": "Η επιλεγμένη τοποθεσία δεν έχει επίπεδο αποθέματος για τα επιλεγμένα αντικείμενα. Η επιστροφή μπορεί να ζητηθεί, αλλά δεν μπορεί να παραληφθεί έως ότου δημιουργηθεί ένα επίπεδο αποθέματος για την επιλεγμένη τοποθεσία.", "noteHint": "Μπορείτε να πληκτρολογήσετε ελεύθερα εάν θέλετε να διευκρινίσετε κάτι.", "location": "Τοποθεσία", "locationHint": "Επιλέξτε σε ποια τοποθεσία θέλετε να επιστρέψετε τα αντικείμενα.", "inboundShipping": "Αποστολή επιστροφής", "inboundShippingHint": "Επιλέξτε ποια μέθοδο θέλετε να χρησιμοποιήσετε.", "returnableQuantityLabel": "Ποσότητα που μπορεί να επιστραφεί", "refundableAmountLabel": "Ποσό που μπορεί να επιστραφεί", "returnRequestedInfo": "Ζητήθηκε επιστροφή {{requestedItemsCount}}x αντικειμένων", "returnReceivedInfo": "Ελήφθη επιστροφή {{requestedItemsCount}}x αντικειμένων", "itemReceived": "Αντικείμενα που ελήφθησαν", "returnRequested": "Ζητήθηκε επιστροφή", "damagedItemReceived": "Ελήφθησαν κατεστραμμένα αντικείμενα", "damagedItemsReturned": "Επιστράφηκαν {{quantity}}x κατεστραμμένα αντικείμενα", "activeChangeError": "Υπάρχει μια ενεργή αλλαγή παραγγελίας σε εξέλιξη σε αυτήν την παραγγελία. Ολοκληρώστε ή απορρίψτε την αλλαγή πρώτα.", "cancel": {"title": "Ακύρωση επιστροφής", "description": "Είστε σίγουροι ότι θέλετε να ακυρώσετε το αίτημα επιστροφής;"}, "placeholders": {"noReturnShippingOptions": {"title": "Δεν βρέθηκαν επιλογές αποστολής επιστροφής", "hint": "Δεν δημιουργήθηκαν επιλογές αποστολής επιστροφής για την τοποθεσία. Μπορείτε να δημιουργήσετε μία στην <LinkComponent>Τοποθεσία & Αποστολή</LinkComponent>."}, "outboundShippingOptions": {"title": "Δεν βρέθηκαν επιλογές εξερχόμενης αποστολής", "hint": "Δεν δημιουργήθηκαν επιλογές εξερχόμενης αποστολής για την τοποθεσία. Μπορείτε να δημιουργήσετε μία στην <LinkComponent>Τοποθεσία & Αποστολή</LinkComponent>."}}, "receive": {"action": "Παραλα<PERSON><PERSON> αντικειμένων", "receiveItems": "{{ returnType }} {{ id }}", "restockAll": "Επανα<PERSON><PERSON><PERSON><PERSON>λων των αντικειμένων", "itemsLabel": "Αντικείμενα που ελήφθησαν", "title": "Παραλα<PERSON><PERSON> αντικειμένων για #{{returnId}}", "sendNotificationHint": "Ειδοποιήστε τον πελάτη για την παραλαβή της επιστροφής.", "inventoryWarning": "Λάβετε υπόψη ότι θα προσαρμόσουμε αυτόματα τα επίπεδα αποθέματος με βάση τα παραπάνω στοιχεία σας.", "writeOffInputLabel": "Πόσα από τα αντικείμενα είναι κατεστραμμένα;", "toast": {"success": "Η επιστροφή ελήφθη με επιτυχία.", "errorLargeValue": "Η ποσότητα είναι μεγαλύτερη από την ζητούμενη ποσότητα αντικειμένων.", "errorNegativeValue": "Η ποσότητα δεν μπορεί να είναι αρνητική τιμή.", "errorLargeDamagedValue": "Η ποσότητα κατεστραμμένων αντικειμένων + η ποσότητα μη κατεστραμμένων αντικειμένων που ελήφθησαν υπερβαίνει τη συνολική ποσότητα αντικειμένων στην επιστροφή. Μειώστε την ποσότητα των μη κατεστραμμένων αντικειμένων."}}, "toast": {"canceledSuccessfully": "Η επιστροφή ακυρώθηκε με επιτυχία", "confirmedSuccessfully": "Η επιστροφή επιβεβαιώθηκε με επιτυχία"}, "panel": {"title": "Ξεκίνησε η επιστροφή", "description": "Υπάρχει ένα ανοιχτό αίτημα επιστροφής προς ολοκλήρωση"}}, "claims": {"create": "Δημιουργ<PERSON>α απαίτησης", "confirm": "Επιβεβαίωση απαίτησης", "confirmText": "Είστε έτοιμοι να επιβεβαιώσετε μια απαίτηση. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "manage": "Διαχείριση απαίτησης", "outbound": "Εξερχόμενα", "outboundItemAdded": "{{itemsCount}}x προστέθηκαν μέσω απαίτησης", "outboundTotal": "Συνολικό ποσό εξερχομένων", "outboundShipping": "Εξερχόμενη αποστολή", "outboundShippingHint": "Επιλέξτε ποια μέθοδο θέλετε να χρησιμοποιήσετε.", "refundAmount": "Εκτιμώμενη διαφορά", "activeChangeError": "Υπάρχει μια ενεργή αλλαγή παραγγελίας σε αυτήν την παραγγελία. Ολοκληρώστε ή απορρίψτε την προηγούμενη αλλαγή.", "actions": {"cancelClaim": {"successToast": "Η απαίτηση ακυρώθηκε με επιτυχία."}}, "cancel": {"title": "Ακύρωση απαίτησης", "description": "Είστε σίγουροι ότι θέλετε να ακυρώσετε την απαίτηση;"}, "tooltips": {"onlyReturnShippingOptions": "Αυτή η λίστα θα αποτελείται μόνο από επιλογές αποστολής επιστροφής."}, "toast": {"canceledSuccessfully": "Η απαίτηση ακυρώθηκε με επιτυχία", "confirmedSuccessfully": "Η απαίτηση επιβεβαιώθηκε με επιτυχία"}, "panel": {"title": "Ξεκίνησε η απαίτηση", "description": "Υπάρχει ένα ανοιχτό αίτημα απαίτησης προς ολοκλήρωση"}}, "exchanges": {"create": "Δημιουργ<PERSON><PERSON> ανταλλαγής", "manage": "Διαχείριση ανταλλαγής", "confirm": "Επιβεβαίωση ανταλλαγής", "confirmText": "Είστε έτοιμοι να επιβεβαιώσετε μια ανταλλαγή. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "outbound": "Εξερχόμενα", "outboundItemAdded": "{{itemsCount}}x προστέθηκαν μέσω ανταλλαγής", "outboundTotal": "Συνολικό ποσό εξερχομένων", "outboundShipping": "Εξερχόμενη αποστολή", "outboundShippingHint": "Επιλέξτε ποια μέθοδο θέλετε να χρησιμοποιήσετε.", "refundAmount": "Εκτιμώμενη διαφορά", "activeChangeError": "Υπάρχει μια ενεργή αλλαγή παραγγελίας σε αυτήν την παραγγελία. Ολοκληρώστε ή απορρίψτε την προηγούμενη αλλαγή.", "actions": {"cancelExchange": {"successToast": "Η ανταλλαγή ακυρώθηκε με επιτυχία."}}, "cancel": {"title": "Ακύρωση ανταλλαγής", "description": "Είστε σίγουροι ότι θέλετε να ακυρώσετε την ανταλλαγή;"}, "tooltips": {"onlyReturnShippingOptions": "Αυτή η λίστα θα αποτελείται μόνο από επιλογές αποστολής επιστροφής."}, "toast": {"canceledSuccessfully": "Η ανταλλαγή ακυρώθηκε με επιτυχία", "confirmedSuccessfully": "Η ανταλλαγή επιβεβαιώθηκε με επιτυχία"}, "panel": {"title": "Ξεκίνησε η ανταλλαγή", "description": "Υπάρχει ένα ανοιχτό αίτημα ανταλλαγής προς ολοκλήρωση"}}, "reservations": {"allocatedLabel": "Εκχωρημένο", "notAllocatedLabel": "Μη εκχωρημένο"}, "allocateItems": {"action": "Εκχώρηση αντικειμένων", "title": "Εκχώρηση αντικειμένων παραγγελίας", "locationDescription": "Επιλέξτε από ποια τοποθεσία θέλετε να εκχωρήσετε.", "itemsToAllocate": "Αντικείμενα προς εκχώρηση", "itemsToAllocateDesc": "Επιλέξτε τον αριθμό των αντικειμένων που θέλετε να εκχωρήσετε", "search": "Αναζήτηση αντικειμένων", "consistsOf": "Αποτελείται από {{num}}x αντικείμενα αποθέματος", "requires": "Απαιτεί {{num}} ανά παραλλαγή", "toast": {"created": "Τα αντικείμενα εκχωρήθηκαν με επιτυχία"}, "error": {"quantityNotAllocated": "Υπάρχουν μη εκχωρημένα αντικείμενα."}}, "shipment": {"title": "Σημείωση εκπλήρωσης ως αποσταλμένη", "trackingNumber": "Αριθμός παρακολούθησης", "addTracking": "Προσθήκη αριθμού παρακολούθησης", "sendNotification": "Αποστολή ειδοποίησης", "sendNotificationHint": "Ειδοποιήστε τον πελάτη για αυτήν την αποστολή.", "toastCreated": "Η αποστολή δημιουργήθηκε με επιτυχία."}, "fulfillment": {"cancelWarning": "Είστε έτοιμοι να ακυρώσετε μια εκπλήρωση. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "markAsDeliveredWarning": "Είστε έτοιμοι να σημειώσετε την εκπλήρωση ως παραδομένη. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "differentOptionSelected": "Η επιλεγμένη επιλογή αποστολής είναι διαφορετική από αυτήν που επέλεξε ο πελάτης.", "disabledItemTooltip": "Η επιλεγμένη επιλογή αποστολής δεν επιτρέπει την εκπλήρωση αυτού του στοιχείου", "unfulfilledItems": "Μη εκπληρωμένα αντικείμενα", "statusLabel": "Κατάσταση εκπλήρωσης", "statusTitle": "Κατάσταση εκπλήρωσης", "fulfillItems": "Εκπλήρωση αντικειμένων", "awaitingFulfillmentBadge": "Σε αναμονή εκπλήρωσης", "requiresShipping": "Απαιτεί αποστολή", "number": "Εκπλήρωση #{{number}}", "itemsToFulfill": "Αντικείμενα προς εκπλήρωση", "create": "Δημιουργ<PERSON>α εκπλήρωσης", "available": "Διαθέσιμο", "inStock": "Σε απόθεμα", "markAsShipped": "Σημείωση ως αποσταλμένη", "markAsDelivered": "Σημείωση ως παραδομένη", "itemsToFulfillDesc": "Επιλέξτε αντικείμενα και ποσότητες προς εκπλήρωση", "locationDescription": "Επιλέξτε από ποια τοποθεσία θέλετε να εκπληρώσετε τα αντικείμενα.", "sendNotificationHint": "Ειδοποιήστε τους πελάτες για την δημιουργηθείσα εκπλήρωση.", "methodDescription": "Επιλέξτε μια διαφορετική μέθοδο αποστολής από αυτήν που επέλεξε ο πελάτης", "error": {"wrongQuantity": "Μόνο ένα αντικείμενο είναι διαθέσιμο για εκπλήρωση", "wrongQuantity_other": "Η ποσότητα πρέπει να είναι αριθμός μεταξύ 1 και {{number}}", "noItems": "Δεν υπάρχουν αντικείμενα προς εκπλήρωση.", "noShippingOption": "Ο τρόπος αποστολής είναι απαραίτητος", "noLocation": "Η τοποθεσία είναι απαραίτητη"}, "status": {"notFulfilled": "Μη εκπληρωμένη", "partiallyFulfilled": "Με<PERSON>ι<PERSON><PERSON><PERSON> εκπληρωμένη", "fulfilled": "Εκπληρωμένη", "partiallyShipped": "Μ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αποσταλμένη", "shipped": "Αποσταλμένη", "delivered": "Παραδομένη", "partiallyDelivered": "Μερικ<PERSON><PERSON> παραδομένη", "partiallyReturned": "Με<PERSON>ι<PERSON><PERSON><PERSON> επιστραφέντα", "returned": "Επιστραφέντα", "canceled": "Ακυρωμένη", "requiresAction": "Απαιτείται ενέργεια"}, "toast": {"created": "Η εκπλήρωση δημιουργήθηκε με επιτυχία", "canceled": "Η εκπλήρωση ακυρώθηκε με επιτυχία", "fulfillmentShipped": "Δεν είναι δυνατή η ακύρωση μιας ήδη αποσταλμένης εκπλήρωσης", "fulfillmentDelivered": "Η εκπλήρωση σημειώθηκε με επιτυχία ως παραδομένη"}, "trackingLabel": "Παρα<PERSON><PERSON>λούθηση", "shippingFromLabel": "Αποστολή από", "itemsLabel": "Αντικείμενα"}, "refund": {"title": "Δημιουρ<PERSON><PERSON><PERSON> επιστροφής χρημάτων", "sendNotificationHint": "Ειδοποιήστε τους πελάτες για την δημιουργηθείσα επιστροφή χρημάτων.", "systemPayment": "Πληρωμή συστήματος", "systemPaymentDesc": "Μία ή περισσότερες από τις πληρωμές σας είναι πληρωμές συστήματος. Λάβετε υπόψη ότι οι καταγραφές και οι επιστροφές χρημάτων δεν γίνονται από την Medusa για τέτοιες πληρωμές.", "error": {"amountToLarge": "Δεν είναι δυνατή η επιστροφή χρημάτων μεγαλύτερου ποσού από το αρχικό ποσό της παραγγελίας.", "amountNegative": "Το ποσό επιστροφής χρημάτων πρέπει να είναι θετικός αριθμός.", "reasonRequired": "Επιλέξτε έναν λόγο επιστροφής χρημάτων."}}, "customer": {"contactLabel": "Επαφή", "editEmail": "Επεξεργασία email", "transferOwnership": "Μεταφ<PERSON><PERSON><PERSON> ιδιοκτησίας", "editBillingAddress": "Επεξεργασία διεύθυνσης χρέωσης", "editShippingAddress": "Επεξεργασία διεύθυνσης αποστολής"}, "activity": {"header": "Δραστηριότητα", "showMoreActivities_one": "Εμφάνιση {{count}} ακόμη δραστηριότητας", "showMoreActivities_other": "Εμφάνιση {{count}} ακόμη δραστηριοτήτων", "comment": {"label": "Σχόλιο", "placeholder": "Αφήστε ένα σχόλιο", "addButtonText": "Προσθήκη σχολίου", "deleteButtonText": "Διαγρα<PERSON><PERSON> σχολίου"}, "from": "Από", "to": "<PERSON>ρ<PERSON>", "events": {"common": {"toReturn": "Για επιστροφή", "toSend": "<PERSON><PERSON>α αποστολή"}, "placed": {"title": "Η παραγγελία υποβλήθηκε", "fromSalesChannel": "από {{salesChannel}}"}, "canceled": {"title": "Η παραγγελία ακυρώθηκε"}, "payment": {"awaiting": "Σε αναμονή πληρωμής", "captured": "Η πληρωμή καταγράφηκε", "canceled": "Η πληρωμή ακυρώθηκε", "refunded": "Η πληρωμή επιστράφηκε"}, "fulfillment": {"created": "Τα αντικείμενα εκπληρώθηκαν", "canceled": "Η εκπλήρωση ακυρώθηκε", "shipped": "Τα αντικείμενα αποστάλθηκαν", "delivered": "Τα αντικείμενα παραδόθηκαν", "items_one": "{{count}} αντικείμενο", "items_other": "{{count}} αντικείμενα"}, "return": {"created": "Ζητήθηκε επιστροφή #{{returnId}}", "canceled": "Η επιστροφή #{{returnId}} ακυρώθηκε", "received": "Η επιστροφή #{{returnId}} ελήφθη", "items_one": "Επιστράφηκε {{count}} αντικείμενο", "items_other": "Επιστράφηκαν {{count}} αντικείμενα"}, "note": {"comment": "Σχόλιο", "byLine": "από {{author}}"}, "claim": {"created": "Ζητήθηκε απαίτηση #{{claimId}}", "canceled": "Η απαίτηση #{{claimId}} ακυρώθηκε", "itemsInbound": "{{count}} αντικ<PERSON>ίμεν<PERSON> προς επιστροφή", "itemsOutbound": "{{count}} αντικ<PERSON>ίμεν<PERSON> προς αποστολή"}, "exchange": {"created": "Ζητήθηκε ανταλλαγή #{{exchangeId}}", "canceled": "Η ανταλλαγή #{{exchangeId}} ακυρώθηκε", "itemsInbound": "{{count}} αντικ<PERSON>ίμεν<PERSON> προς επιστροφή", "itemsOutbound": "{{count}} αντικ<PERSON>ίμεν<PERSON> προς αποστολή"}, "edit": {"requested": "Ζητήθηκε επεξεργασία παραγγελίας #{{editId}}", "confirmed": "Η επεξεργασία παραγγελίας #{{editId}} επιβεβαιώθηκε"}, "transfer": {"requested": "Ζητήθηκε μεταφορά παραγγελίας #{{transferId}}", "confirmed": "Η μεταφορά παραγγελίας #{{transferId}} επιβεβαιώθηκε", "declined": "Η μεταφορά παραγγελίας #{{transferId}} απορρίφθηκε"}, "update_order": {"shipping_address": "Η διεύθυνση αποστολής ενημερώθηκε", "billing_address": "Η διεύθυνση χρέωσης ενημερώθηκε", "email": "Το email ενημερώθηκε"}}}, "fields": {"displayId": "ID εμφάνισης", "refundableAmount": "Ποσό που μπορεί να επιστραφεί", "returnableQuantity": "Ποσότητα που μπορεί να επιστραφεί"}}, "draftOrders": {"domain": "Πρόχειρες παραγγελίες", "deleteWarning": "Είστε έτοιμοι να διαγράψετε την πρόχειρη παραγγελία {{id}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "paymentLinkLabel": "Σύνδεσμος πληρωμής", "cartIdLabel": "ID καλαθιού", "markAsPaid": {"label": "Σημείωση ως πληρωμένη", "warningTitle": "Σημείωση ως πληρωμένη", "warningDescription": "Είστε έτοιμοι να σημειώσετε την πρόχειρη παραγγελία ως πληρωμένη. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί και η είσπραξη της πληρωμής δεν θα είναι δυνατή αργότερα."}, "status": {"open": "Ανοιχτή", "completed": "Ολοκληρωμένη"}, "create": {"createDraftOrder": "Δημιουργία πρόχειρης παραγγελίας", "createDraftOrderHint": "Δημιουργήστε μια νέα πρόχειρη παραγγελία για να διαχειριστείτε τις λεπτομέρειες μιας παραγγελίας πριν υποβληθεί.", "chooseRegionHint": "Επιλογή περιοχής", "existingItemsLabel": "Υπάρχοντα αντικείμενα", "existingItemsHint": "Προσθέστε υπάρχοντα προϊόντα στην πρόχειρη παραγγελία.", "customItemsLabel": "Προσαρμοσμένα αντικείμενα", "customItemsHint": "Προσθέστε προσαρμοσμένα αντικείμενα στην πρόχειρη παραγγελία.", "addExistingItemsAction": "Προσθήκη υπαρχόντων αντικειμένων", "addCustomItemAction": "Προσθήκη προσαρμοσμένου αντικειμένου", "noCustomItemsAddedLabel": "Δεν έχουν προστεθεί ακόμη προσαρμοσμένα αντικείμενα", "noExistingItemsAddedLabel": "Δεν έχουν προστεθεί ακόμη υπάρχοντα αντικείμενα", "chooseRegionTooltip": "Επιλέξτε μια περιοχή πρώτα", "useExistingCustomerLabel": "Χρή<PERSON>η υπ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πελάτη", "addShippingMethodsAction": "Προσθήκη μεθόδων αποστολής", "unitPriceOverrideLabel": "Παράκαμψη τιμής μονάδας", "shippingOptionLabel": "Επιλογή αποστολής", "shippingOptionHint": "Επιλέξτε την επιλογή αποστολής για την πρόχειρη παραγγελία.", "shippingPriceOverrideLabel": "Παράκαμψη τιμής αποστολής", "shippingPriceOverrideHint": "Παράκαμψη της τιμής αποστολής για την πρόχειρη παραγγελία.", "sendNotificationLabel": "Αποστολή ειδοποίησης", "sendNotificationHint": "Στείλτε μια ειδοποίηση στον πελάτη όταν δημιουργηθεί η πρόχειρη παραγγελία."}, "validation": {"requiredEmailOrCustomer": "Απαιτείται email ή πελάτης.", "requiredItems": "Απαιτείτ<PERSON>ι τουλάχιστον ένα αντικείμενο.", "invalidEmail": "Το email πρέπει να είναι μια έγκυρη διεύθυνση email."}}, "stockLocations": {"domain": "Τοποθεσίες & Αποστολή", "list": {"description": "Διαχειριστείτε τις τοποθεσίες αποθέματος και τις επιλογές αποστολής του καταστήματός σας."}, "create": {"header": "Δημιουργία τοποθεσίας αποθέματος", "hint": "Μια τοποθεσία αποθέματος είναι μια φυσική τοποθεσία όπου αποθηκεύονται και αποστέλλονται προϊόντα.", "successToast": "Η τοποθεσία {{name}} δημιουργήθηκε με επιτυχία."}, "edit": {"header": "Επεξεργασία τοποθεσίας αποθέματος", "viewInventory": "Προβολή αποθέματος", "successToast": "Η τοποθεσία {{name}} ενημερώθηκε με επιτυχία."}, "delete": {"confirmation": "Είστε έτοιμοι να διαγράψετε την τοποθεσία αποθέματος {{name}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί."}, "fulfillmentProviders": {"header": "Πάροχοι εκπλήρωσης", "shippingOptionsTooltip": "Αυτό το αναπτυ<PERSON><PERSON><PERSON>μενο μενού θα αποτελείται μόνο από παρόχους που είναι ενεργοποιημένοι για αυτήν την τοποθεσία. Προσθέστε τους στην τοποθεσία εάν το αναπτυσσόμενο μενού είναι απενεργοποιημένο.", "label": "Συνδεδεμένοι πάροχοι εκπλήρωσης", "connectedTo": "Συνδεδεμένο με {{count}} από {{total}} παρόχους εκπλήρωσης", "noProviders": "Αυτή η τοποθεσία αποθέματος δεν είναι συνδεδεμένη με κανέναν πάροχο εκπλήρωσης.", "action": "Σύνδεση παρόχων", "successToast": "Οι πάροχοι εκπλήρωσης για την τοποθεσία αποθέματος ενημερώθηκαν με επιτυχία."}, "fulfillmentSets": {"pickup": {"header": "Παραλαβή"}, "shipping": {"header": "Αποστολή"}, "disable": {"confirmation": "Είστε σίγουροι ότι θέλετε να απενεργοποιήσετε το {{name}}; Αυτό θα διαγράψει όλες τις σχετικές ζώνες υπηρεσιών και επιλογές αποστολής και δεν μπορεί να αναιρεθεί.", "pickup": "Η παραλαβή απενεργοποιήθηκε με επιτυχία.", "shipping": "Η αποστολή απενεργοποιήθηκε με επιτυχία."}, "enable": {"pickup": "Η παραλαβή ενεργοποιήθηκε με επιτυχία.", "shipping": "Η αποστολή ενεργοποιήθηκε με επιτυχία."}}, "sidebar": {"header": "Διαμόρφωση αποστολής", "shippingProfiles": {"label": "Προ<PERSON><PERSON><PERSON> αποστολής", "description": "Ομαδοποίηση προϊόντων ανάλογα με τις απαιτήσεις αποστολής"}}, "salesChannels": {"header": "Κανάλια πωλήσεων", "label": "Συνδεδεμένα κανάλια πωλήσεων", "connectedTo": "Συνδεδεμένο με {{count}} από {{total}} κανάλια πωλήσεων", "noChannels": "Η τοποθεσία δεν είναι συνδεδεμένη με κανένα κανάλι πωλήσεων.", "action": "Σύνδεση καναλιών πωλήσεων", "successToast": "Τα κανάλια πωλήσεων ενημερώθηκαν με επιτυχία."}, "shippingOptions": {"create": {"shipping": {"header": "Δημιουργία επιλογής αποστολής για {{zone}}", "hint": "Δημιουργήστε μια νέα επιλογή αποστολής για να ορίσετε τον τρόπο αποστολής προϊόντων από αυτήν την τοποθεσία.", "label": "Επιλο<PERSON><PERSON><PERSON> αποστολής", "successToast": "Η επιλογή αποστολής {{name}} δημιουργήθηκε με επιτυχία."}, "returns": {"header": "Δημιουργία επιλογής επιστροφής για {{zone}}", "hint": "Δημιουργήστε μια νέα επιλογή επιστροφής για να ορίσετε τον τρόπο επιστροφής προϊόντων σε αυτήν την τοποθεσία.", "label": "Επιλο<PERSON><PERSON>ς επιστροφής", "successToast": "Η επιλογή επιστροφής {{name}} δημιουργήθηκε με επιτυχία."}, "tabs": {"details": "Λεπτομέρειες", "prices": "Τιμές"}, "action": "Δημιουργία επιλογής"}, "delete": {"confirmation": "Είστε έτοιμοι να διαγράψετε την επιλογή αποστολής {{name}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "successToast": "Η επιλογή αποστολής {{name}} διαγράφηκε με επιτυχία."}, "edit": {"header": "Επεξεργα<PERSON>ία επιλογής αποστολής", "action": "Επεξεργασία επιλογής", "successToast": "Η επιλογή αποστολής {{name}} ενημερώθηκε με επιτυχία."}, "pricing": {"action": "Επεξεργασία τιμών"}, "conditionalPrices": {"header": "Υπό Όρους Τιμές για {{name}}", "description": "Διαχειριστείτε τις υπό όρους τιμές για αυτή την επιλογή αποστολής με βάση το συνολικό ποσό του καλαθιού.", "attributes": {"cartItemTotal": "Συνολικό ποσό καλαθιού"}, "summaries": {"range": "Αν το <0>{{attribute}}</0> είναι μεταξύ <1>{{gte}}</1> και <2>{{lte}}</2>", "greaterThan": "Αν το <0>{{attribute}}</0> ≥ <1>{{gte}}</1>", "lessThan": "Αν το <0>{{attribute}}</0> ≤ <1>{{lte}}</1>"}, "actions": {"addPrice": "Προσθήκη τιμής", "manageConditionalPrices": "Διαχείριση υπό όρους τιμών"}, "rules": {"amount": "Τιμή επιλογής αποστολής", "gte": "Ελάχιστο συνολικό ποσό καλαθιού", "lte": "Μέγιστο συνολικό ποσό καλαθιού"}, "customRules": {"label": "Προσαρμοσμένοι κανόνες", "tooltip": "Αυτή η υπό όρους τιμή έχει κανόνες που δεν μπορούν να διαχειριστούν στον πίνακα ελέγχου.", "eq": "Το συνολικό ποσό καλαθιού πρέπει να είναι ίσο με", "gt": "Το συνολικό ποσό καλαθιού πρέπει να είναι μεγαλύτερο από", "lt": "Το συνολικ<PERSON> ποσό καλαθιού πρέπει να είναι μικρότερο από"}, "errors": {"amountRequired": "Η τιμή επιλογής αποστολής είναι υποχρεωτική", "minOrMaxRequired": "Πρέπει να παρέχεται τουλάχιστον ένα από τα ελάχιστο ή μέγιστο συνολικό ποσό καλαθιού", "minGreaterThanMax": "Το ελάχιστο συνολικό ποσό καλαθιού πρέπει να είναι μικρότερο ή ίσο με το μέγιστο συνολικό ποσό καλαθιού", "duplicateAmount": "Η τιμή επιλογής αποστολής πρέπει να είναι μοναδική για κάθε συνθήκη", "overlappingConditions": "Οι συνθήκες πρέπει να είναι μοναδικές σε όλους τους κανόνες τιμών"}}, "fields": {"count": {"shipping_one": "{{count}} επιλογή αποστολής", "shipping_other": "{{count}} επιλογ<PERSON>ς αποστολής", "returns_one": "{{count}} επιλογή επιστροφής", "returns_other": "{{count}} επιλογές επιστροφής"}, "priceType": {"label": "Τύπος τιμής", "options": {"fixed": {"label": "Σταθερή", "hint": "Η τιμή της επιλογής αποστολής είναι σταθερή και δεν αλλάζει ανάλογα με τα περιεχόμενα της παραγγελίας."}, "calculated": {"label": "Υπολογιζόμενη", "hint": "Η τιμή της επιλογής αποστολής υπολογίζεται από τον πάροχο εκπλήρωσης κατά την ολοκλήρωση αγοράς."}}}, "enableInStore": {"label": "Ενεργοποίηση στο κατάστημα", "hint": "Εάν οι πελάτες μπορούν να χρησιμοποιήσουν αυτήν την επιλογή κατά την ολοκλήρωση αγοράς."}, "provider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εκπλήρωσης", "profile": "Προ<PERSON><PERSON><PERSON> αποστολής", "fulfillmentOption": "Επιλογή εκπλήρωσης"}}, "serviceZones": {"create": {"headerPickup": "Δημιουρ<PERSON><PERSON><PERSON> ζώνης υπηρεσιών για παραλαβή από {{location}}", "headerShipping": "Δημιου<PERSON><PERSON><PERSON><PERSON> ζώνης υπηρεσιών για αποστολή από {{location}}", "action": "Δημιου<PERSON><PERSON><PERSON><PERSON> ζώνης υπηρεσιών", "successToast": "Η ζώνη υπηρ<PERSON><PERSON><PERSON><PERSON>ν {{name}} δημιουργήθηκε με επιτυχία."}, "edit": {"header": "Επεξερ<PERSON><PERSON><PERSON><PERSON><PERSON> ζώνης υπηρεσιών", "successToast": "Η ζώνη υπηρ<PERSON><PERSON><PERSON>ών {{name}} ενημερώθηκε με επιτυχία."}, "delete": {"confirmation": "Είστε έτοιμοι να διαγράψετε τη ζώνη υπηρεσιών {{name}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "successToast": "Η ζώνη υπηρ<PERSON><PERSON><PERSON>ών {{name}} διαγράφηκε με επιτυχία."}, "manageAreas": {"header": "Διαχείριση περιοχών για {{name}}", "action": "Διαχείριση περιοχών", "label": "Περιοχ<PERSON>ς", "hint": "Επιλέξτε τις γεωγραφικές περιοχές που καλύπτει η ζώνη υπηρεσιών.", "successToast": "Οι περιοχές για {{name}} ενημερώθηκαν με επιτυχία."}, "fields": {"noRecords": "Δεν υπάρχ<PERSON>υν ζώνες υπηρεσιών για να προσθέσετε επιλογές αποστολής.", "tip": "Μια ζώνη υπηρεσιών είναι μια συλλογή γεωγρα<PERSON><PERSON><PERSON><PERSON>ν ζωνών ή περιοχών. Χρησιμοποιείται για να περιορίσει τις διαθέσιμες επιλογές αποστολής σε ένα καθορισμένο σύνολο τοποθεσιών."}}}, "shippingProfile": {"domain": "Προ<PERSON><PERSON><PERSON> αποστολής", "subtitle": "Ομαδοποιήστε προϊόντα με παρόμοιες απαιτήσεις αποστολής σε προφίλ.", "create": {"header": "Δημιουργ<PERSON>α προφίλ αποστολής", "hint": "Δημιουργήστε ένα νέο προφίλ αποστολής για να ομαδοποιήσετε προϊόντα με παρόμοιες απαιτήσεις αποστολής.", "successToast": "Το προ<PERSON>ί<PERSON> αποστολής {{name}} δημιουργήθηκε με επιτυχία."}, "delete": {"title": "Διαγραφή προφίλ αποστολής", "description": "Είστε έτοιμοι να διαγράψετε το προφίλ αποστολής {{name}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "successToast": "Το προφίλ αποστολής {{name}} διαγράφηκε με επιτυχία."}, "tooltip": {"type": "Εισαγάγετε τον τύπο προφίλ αποστολής, για παράδειγμα: Βαρ<PERSON>, Υπερμεγέθης, <PERSON>ό<PERSON><PERSON> φορτίο κ.λπ."}}, "taxRegions": {"domain": "Φορολογικ<PERSON>ς περιοχές", "list": {"hint": "Διαχειριστείτε τι χρεώνετε τους πελάτες σας όταν ψωνίζουν από διαφορετικές χώρες και περιοχές."}, "delete": {"confirmation": "Είστε έτοιμοι να διαγράψετε μια φορολογική περιοχή. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "successToast": "Η φορολογική περιοχή διαγράφηκε με επιτυχία."}, "create": {"header": "Δημιουργ<PERSON><PERSON> φορολογικής περιοχής", "hint": "Δημιουργήστε μια νέα φορολογική περιοχή για να ορίσετε φορολογικούς συντελεστές για μια συγκεκριμένη χώρα.", "errors": {"rateIsRequired": "Απαιτείτ<PERSON><PERSON> φορολογικ<PERSON>ς συντελεστής κατά τη δημιουργία ενός προεπιλεγμένου φορολογικού συντελεστή.", "nameIsRequired": "Απαιτε<PERSON><PERSON><PERSON><PERSON> όνομα κατά τη δημιουργ<PERSON>α ενός προεπιλεγμένου φορολογικού συντελεστή."}, "successToast": "Η φορολογική περιοχή δημιουργήθηκε με επιτυχία."}, "province": {"header": "Επαρχίες", "create": {"header": "Δημιουργ<PERSON>α φορολογικής περιοχής επαρχίας", "hint": "Δημιουργήστε μια νέα φορολογική περιοχή για να ορίσετε φορολογικούς συντελεστές για μια συγκεκριμένη επαρχία."}}, "state": {"header": "Πολιτείες", "create": {"header": "Δημιουργ<PERSON><PERSON> φορολογικής περιοχής πολιτείας", "hint": "Δημιουργήστε μια νέα φορολογική περιοχή για να ορίσετε φορολογικούς συντελεστές για μια συγκεκριμένη πολιτεία."}}, "stateOrTerritory": {"header": "Πολιτείες ή Επικράτειες", "create": {"header": "Δημιουργ<PERSON><PERSON> φορολογικής περιοχής πολιτείας/επικράτειας", "hint": "Δημιουργήστε μια νέα φορολογική περιοχή για να ορίσετε φορολογικούς συντελεστές για μια συγκεκριμένη πολιτεία/επικράτεια."}}, "county": {"header": "Κομητείες", "create": {"header": "Δημιουργ<PERSON><PERSON> φορολογικής περιοχής κομητείας", "hint": "Δημιουργήστε μια νέα φορολογική περιοχή για να ορίσετε φορολογικούς συντελεστές για μια συγκεκριμένη κομητεία."}}, "region": {"header": "Περιοχ<PERSON>ς", "create": {"header": "Δημιουργ<PERSON><PERSON> φορολογικής περιοχής", "hint": "Δημιουργήστε μια νέα φορολογική περιοχή για να ορίσετε φορολογικούς συντελεστές για μια συγκεκριμένη περιοχή."}}, "department": {"header": "Τμήματα", "create": {"header": "Δημιουργ<PERSON><PERSON> φορολογικής περιοχής τμήματος", "hint": "Δημιουργήστε μια νέα φορολογική περιοχή για να ορίσετε φορολογικούς συντελεστές για ένα συγκεκριμένο τμήμα."}}, "territory": {"header": "Επικράτειες", "create": {"header": "Δημιουργ<PERSON><PERSON> φορολογικής περιοχής επικράτειας", "hint": "Δημιουργήστε μια νέα φορολογική περιοχή για να ορίσετε φορολογικούς συντελεστές για μια συγκεκριμένη επικράτεια."}}, "prefecture": {"header": "Νομοί", "create": {"header": "Δημιουργ<PERSON><PERSON> φορολογικής περιοχής νομού", "hint": "Δημιουργήστε μια νέα φορολογική περιοχή για να ορίσετε φορολογικούς συντελεστές για ένα συγκεκριμένο νομό."}}, "district": {"header": "Περιφέρειες", "create": {"header": "Δημιουργ<PERSON><PERSON> φορολογικής περιοχής περιφέρειας", "hint": "Δημιουργήστε μια νέα φορολογική περιοχή για να ορίσετε φορολογικούς συντελεστές για μια συγκεκριμένη περιφέρεια."}}, "governorate": {"header": "Κυβερνεία", "create": {"header": "Δημιουργ<PERSON><PERSON> φορολογικής περιοχής κυβερνείου", "hint": "Δημιουργήστε μια νέα φορολογική περιοχή για να ορίσετε φορολογικούς συντελεστές για ένα συγκεκριμένο κυβερνείο."}}, "canton": {"header": "Καντόνια", "create": {"header": "Δημιουργ<PERSON><PERSON> φορολογικής περιοχής καντονιού", "hint": "Δημιουργήστε μια νέα φορολογική περιοχή για να ορίσετε φορολογικούς συντελεστές για ένα συγκεκριμένο καντόνι."}}, "emirate": {"header": "Εμιράτα", "create": {"header": "Δημιουργ<PERSON><PERSON> φορολογικής περιοχής εμιράτου", "hint": "Δημιουργήστε μια νέα φορολογική περιοχή για να ορίσετε φορολογικούς συντελεστές για ένα συγκεκριμένο εμιράτο."}}, "sublevel": {"header": "Υποεπίπεδα", "create": {"header": "Δημιουργ<PERSON><PERSON> φορολογικής περιοχής υποεπιπέδου", "hint": "Δημιουργήστε μια νέα φορολογική περιοχή για να ορίσετε φορολογικούς συντελεστές για ένα συγκεκριμένο υποεπίπεδο."}}, "taxOverrides": {"header": "Παρεκκλίσεις", "create": {"header": "Δημιουργ<PERSON>α παρέκκλισης", "hint": "Δημιουργήστε έναν φορολογι<PERSON><PERSON> συντελεστή που παρακάμπτει τους προεπιλεγμένους φορολογικούς συντελεστές για επιλεγμένες συνθήκες."}, "edit": {"header": "Επεξεργασία παρέκκλισης", "hint": "Επεξεργαστείτε τον φορολογι<PERSON><PERSON> συντελεστή που παρακάμπτει τους προεπιλεγμένους φορολογικούς συντελεστές για επιλεγμένες συνθήκες."}}, "taxRates": {"create": {"header": "Δημιουργ<PERSON><PERSON> φορολογικού συντελεστή", "hint": "Δημιουργήστε έναν νέο φορολο<PERSON>ι<PERSON><PERSON> συντελεστή για να ορίσετε τον φορολογικό συντελεστή για μια περιοχή.", "successToast": "Ο φορολογι<PERSON><PERSON>ς συντελεστής δημιουργήθηκε με επιτυχία."}, "edit": {"header": "Επεξεργασ<PERSON>α φορολογικού συντελεστή", "hint": "Επεξεργαστείτε τον φορολογικ<PERSON> συντελεστή για να ορίσετε τον φορολογικό συντελεστή για μια περιοχή.", "successToast": "Ο φορολογ<PERSON><PERSON><PERSON>ς συντελεστής ενημερώθηκε με επιτυχία."}, "delete": {"confirmation": "Είστε έτοιμοι να διαγράψετε τον φορολογικό συντελεστή {{name}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "successToast": "Ο φορολογι<PERSON><PERSON>ς συντελεστής διαγράφηκε με επιτυχία."}}, "fields": {"isCombinable": {"label": "Συνδυάσιμος", "hint": "<PERSON><PERSON><PERSON> αυτός ο φορολογικός συντελεστής μπορεί να συνδυαστεί με τον προεπιλεγμένο συντελεστή από τη φορολογική περιοχή.", "true": "Συνδυάσιμος", "false": "Μη συνδυάσιμος"}, "defaultTaxRate": {"label": "Προεπιλε<PERSON><PERSON><PERSON><PERSON>ος φορολογικ<PERSON>ς συντελεστής", "tooltip": "Ο προεπιλεγμένος φορολογικός συντελεστής για αυτήν την περιοχή. Ένα παράδειγμα είναι ο τυπικός συντελεστής ΦΠΑ για μια χώρα ή περιοχή.", "action": "Δημιουργία προεπιλεγμένου φορολογικού συντελεστή"}, "taxRate": "Φορ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> συντελεστής", "taxCode": "Φορ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> κωδικός", "targets": {"label": "Στόχοι", "hint": "Επιλέξτε τους στόχους στους οποίους θα εφαρμοστεί αυτός ο φορολογικός συντελεστής.", "options": {"product": "Προϊόντα", "productCollection": "Συλλογές προϊόντων", "productTag": "Ετικέτες προϊόντων", "productType": "Τύποι προϊόντων", "customerGroup": "Ομάδες πελατών"}, "operators": {"in": "σε", "on": "σε", "and": "και"}, "placeholders": {"product": "Αναζήτηση για προϊόντα", "productCollection": "Αναζήτηση για συλλογές προϊόντων", "productTag": "Αναζήτηση για ετικέτες προϊόντων", "productType": "Αναζήτηση για τύπους προϊόντων", "customerGroup": "Αναζήτηση για ομάδες πελατών"}, "tags": {"product": "Προϊόν", "productCollection": "Συλλογή προϊόντων", "productTag": "Ετικέτα προϊόντος", "productType": "Τύπος προϊόντος", "customerGroup": "Ομάδα πελατών"}, "modal": {"header": "Προσθήκη στόχων"}, "values_one": "{{count}} τιμή", "values_other": "{{count}} τιμές", "numberOfTargets_one": "{{count}} στόχος", "numberOfTargets_other": "{{count}} στόχοι", "additionalValues_one": "και {{count}} ακόμη τιμή", "additionalValues_other": "και {{count}} ακόμη τιμές", "action": "Προσθήκη στόχου"}, "sublevels": {"labels": {"province": "Επαρχία", "state": "Πολιτεία", "region": "Περιοχή", "stateOrTerritory": "Πολιτεία/Επικράτεια", "department": "Τμήμα", "county": "Κομητεία", "territory": "Επικράτεια", "prefecture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "district": "Περιφέρεια", "governorate": "Κυβερνείο", "emirate": "Εμιράτο", "canton": "Καντόνι", "sublevel": "Κωδικ<PERSON>ς υποεπιπέδου"}, "placeholders": {"province": "Επιλέξτε επαρχία", "state": "Επιλέξτε πολιτεία", "region": "Επιλέξτε περιοχή", "stateOrTerritory": "Επιλέξτε πολιτεία/επικράτεια", "department": "Επιλέξτε τμήμα", "county": "Επιλέξτε κομητεία", "territory": "Επιλέξτε επικράτεια", "prefecture": "Επιλέξτε νομό", "district": "Επιλέξτε περιφέρεια", "governorate": "Επιλέξτε κυβερνείο", "emirate": "Επιλέξτε εμιράτο", "canton": "Επιλέξτε καντόνι"}, "tooltips": {"sublevel": "Εισαγάγετε τον κωδικό ISO 3166-2 για τη φορολογική περιοχή υποεπιπέδου.", "notPartOfCountry": "Το {{province}} δεν φαίνεται να είναι μέρος του {{country}}. Ελέγξτε ξανά εάν αυτό είναι σωστό."}, "alert": {"header": "Οι περιοχές υποεπιπέδου είναι απενεργοποιημένες για αυτήν τη φορολογική περιοχή", "description": "Οι περιοχές υποεπιπέδου είναι απενεργοποιημένες για αυτήν την περιοχή από προεπιλογή. Μπορείτε να τις ενεργοποιήσετε για να δημιουργήσετε περιοχές υποεπιπέδου όπως επαρχίες, πολιτείες ή επικράτειες.", "action": "Ενεργοποίηση περιοχών υποεπιπέδου"}}, "noDefaultRate": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> προεπιλεγμένο συντελεστή", "tooltip": "Αυτή η φορολογική περιοχή δεν έχει προεπιλεγμένο φορολογικό συντελεστή. Εάν υπάρχει ένας τυπικός συντελεστής, όπως ο ΦΠΑ μιας χώρας, προσθέστε τον σε αυτήν την περιοχή."}}}, "promotions": {"domain": "Προωθήσεις", "sections": {"details": "Λεπτομέρειες προώθησης"}, "tabs": {"template": "Τύπος", "details": "Λεπτομέρειες", "campaign": "Καμπάνια"}, "fields": {"type": "Τύπος", "value_type": "Τύπος τιμής", "value": "Τιμή", "campaign": "Καμπάνια", "method": "Μέθοδος", "allocation": "Εκχώρηση", "addCondition": "Προσθήκη συνθήκης", "clearAll": "Εκκαθάριση όλων", "amount": {"tooltip": "Επιλέξτε τον κωδικό νομίσματος για να ενεργοποιήσετε τη ρύθμιση του ποσού"}, "conditions": {"rules": {"title": "Ποιος μπορεί να χρησιμοποιήσει αυτόν τον κωδικό;", "description": "Ποιος πελάτης επιτρέπεται να χρησιμοποιήσει τον κωδικό προώθησης; Ο κωδικός προώθησης μπορεί να χρησιμοποιηθεί από όλους τους πελάτες εάν δεν τροποποιηθεί."}, "target-rules": {"title": "Σε ποια αντικείμενα θα εφαρμοστεί η προώθηση;", "description": "Η προώθηση θα εφαρμοστεί σε αντικείμενα που πληρούν τις ακόλουθες συνθήκες."}, "buy-rules": {"title": "Τι πρέπει να υπάρχει στο καλάθι για να ξεκλειδωθεί η προώθηση;", "description": "<PERSON><PERSON>ν αυτές οι συνθήκες ταιρι<PERSON><PERSON><PERSON><PERSON><PERSON>, ενεργοποιούμε την προώθηση στα целевые αντικείμενα."}}}, "tooltips": {"campaignType": "Ο κωδικός νομίσματος πρέπει να είναι επιλεγμένος στην προώθηση για να οριστεί ένας προϋπολογισμός δαπανών."}, "errors": {"requiredField": "Απαιτούμενο πεδίο", "promotionTabError": "Διορθώστε τα σφάλματα στην καρτέλα Προώθηση πριν συνεχίσετε"}, "toasts": {"promotionCreateSuccess": "Η προώθηση ({{code}}) δημιουργήθηκε με επιτυχία."}, "create": {}, "edit": {"title": "Επεξερ<PERSON><PERSON><PERSON><PERSON><PERSON> λεπτομερειών προώθησης", "rules": {"title": "Επεξεργασία όρων χρήσης"}, "target-rules": {"title": "Επεξεργα<PERSON><PERSON>α όρων αντικειμένων"}, "buy-rules": {"title": "Επεξεργα<PERSON><PERSON><PERSON> κανόνων αγοράς"}}, "campaign": {"header": "Καμπάνια", "edit": {"header": "Επεξεργασία καμπάνιας", "successToast": "Ενημερώθηκε με επιτυχία η καμπάνια της προώθησης."}, "actions": {"goToCampaign": "Μετάβαση στην καμπάνια"}}, "campaign_currency": {"tooltip": "Αυτό είναι το νόμισμα της προώθησης. Αλλάξτε το από την καρτέλα Λεπτομέρειες."}, "form": {"required": "Απαιτείται", "and": "ΚΑΙ", "selectAttribute": "Επιλογή χαρακτηριστικού", "campaign": {"existing": {"title": "Υπάρχουσα καμπάνια", "description": "Προσθήκη προώθησης σε μια υπάρχουσα καμπάνια.", "placeholder": {"title": "Δεν υπάρχουν υπάρχουσες καμπάνιες", "desc": "Μπορείτε να δημιουργήσετε μία για να παρακολουθείτε πολλές προωθήσεις και να ορίζετε όρια προϋπολογισμού."}}, "new": {"title": "Νέα καμπάνια", "description": "Δημιουργήστε μια νέα καμπάνια για αυτήν την προώθηση."}, "none": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> καμπάνια", "description": "Συνεχίστε χωρίς να συσχετίσετε την προώθηση με καμπάνια"}}, "status": {"label": "Κατάσταση", "draft": {"title": "Πρόχειρη", "description": "Οι πελάτες δε θα μπορούν να χρησιμοποιήσουν τον κωδικό ακόμα"}, "active": {"title": "Ενεργή", "description": "Οι πελάτες θα μπορούν να χρησιμοποιήσουν τον κωδικό"}, "inactive": {"title": "Ανενεργή", "description": "Οι πελάτες δε θα μπορούν να χρησιμοποιήσουν τον κωδικό πλέον"}}, "method": {"label": "Μέθοδος", "code": {"title": "<PERSON>ω<PERSON><PERSON><PERSON><PERSON><PERSON> προώθησης", "description": "Οι πελάτες πρέπει να εισάγουν αυτόν τον κωδικό κατά την ολοκλήρωση αγοράς"}, "automatic": {"title": "Αυτόματη", "description": "Οι πελάτες θα δουν αυτήν την προώθηση κατά την ολοκλήρωση αγοράς"}}, "max_quantity": {"title": "Μέγιστη ποσότητα", "description": "Μέγιστη ποσότητα αντικειμένων στα οποία ισχύει αυτή η προώθηση."}, "type": {"standard": {"title": "Τυπική", "description": "Μια τυπική προώθηση"}, "buyget": {"title": "Αγορά X λήψη Y", "description": "Προώθηση αγοράς X λήψης Y"}}, "allocation": {"each": {"title": "Κάθε", "description": "Εφαρμόζει την τιμή σε κάθε αντικείμενο"}, "across": {"title": "Σε όλα", "description": "Εφαρμόζει την τιμή σε όλα τα αντικείμενα"}}, "code": {"title": "Κω<PERSON>ικ<PERSON>ς", "description": "Ο κωδικός που θα εισάγουν οι πελάτες σας κατά την ολοκλήρωση αγοράς."}, "value": {"title": "Τιμή προώθησης"}, "value_type": {"fixed": {"title": "Τιμή προώθησης", "description": "Το ποσό που θα αφαιρεθεί. π.χ. 100"}, "percentage": {"title": "Τιμή προώθησης", "description": "Το ποσοστό έκπτωσης από το ποσό. π.χ. 8%"}}}, "deleteWarning": "Είστε έτοιμοι να διαγράψετε την προώθηση {{code}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "createPromotionTitle": "Δημιουργ<PERSON>α προώθησης", "type": "Τύπος προώθησης", "conditions": {"add": "Προσθήκη συνθήκης", "list": {"noRecordsMessage": "Προσθέστε μια συνθήκη για να περιορίσετε σε ποια αντικείμενα ισχύει η προώθηση."}}}, "campaigns": {"domain": "Καμπάνιες", "details": "Λεπτομέρειες καμπάνιας", "status": {"active": "Ενεργή", "expired": "Ληγμένη", "scheduled": "Προγραμματισμένη"}, "delete": {"title": "Είστε σίγουροι;", "description": "Είστε έτοιμοι να διαγράψετε την καμπάνια '{{name}}'. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "successToast": "Η καμπάνια '{{name}}' δημιουργήθηκε με επιτυχία."}, "edit": {"header": "Επεξεργασία καμπάνιας", "description": "Επεξεργαστείτε τις λεπτομέρειες της καμπάνιας.", "successToast": "Η καμπάνια '{{name}}' ενημερώθηκε με επιτυχία."}, "configuration": {"header": "Διαμόρφωση", "edit": {"header": "Επεξεργασία διαμόρφωσης καμπάνιας", "description": "Επεξεργαστείτε τη διαμόρφωση της καμπάνιας.", "successToast": "Η διαμόρφωση της καμπάνιας ενημερώθηκε με επιτυχία."}}, "create": {"title": "Δημιουργ<PERSON>α καμπάνιας", "description": "Δημιουργήστε μια διαφημιστική καμπάνια.", "hint": "Δημιουργήστε μια διαφημιστική καμπάνια.", "header": "Δημιουργ<PERSON>α καμπάνιας", "successToast": "Η καμπάνια '{{name}}' δημιουργήθηκε με επιτυχία."}, "fields": {"name": "Όνομα", "identifier": "Αναγνωριστικό", "start_date": "Ημερομηνία έναρξης", "end_date": "Ημερομηνία λήξης", "total_spend": "Προϋπολογισμός που δαπανήθηκε", "total_used": "Προϋπολογισμός που χρησιμοποιήθηκε", "budget_limit": "Όριο προϋπολογισμού", "campaign_id": {"hint": "Μόνο οι καμπάνιες με τον ίδιο κωδικ<PERSON> νομίσματος με την προώθηση εμφανίζονται σε αυτήν τη λίστα."}}, "budget": {"create": {"hint": "Δημιουργήστε έναν προϋπολογισμό για την καμπάνια.", "header": "Προϋπολογισμός καμπάνιας"}, "details": "Προϋπολογισμός καμπάνιας", "fields": {"type": "Τύπος", "currency": "Νόμισμα", "limit": "Όριο", "used": "Χρησιμοποιήθηκε"}, "type": {"spend": {"title": "Δαπάνη", "description": "Ορίστε ένα όριο στο συνολικό ποσό έκπτωσης όλων των χρήσεων προώθησης."}, "usage": {"title": "<PERSON>ρ<PERSON><PERSON><PERSON>", "description": "Ορίστε ένα όριο για το πόσες φορές μπορεί να χρησιμοποιηθεί η προώθηση."}}, "edit": {"header": "Επεξεργασία προϋπολογισμού καμπάνιας"}}, "promotions": {"remove": {"title": "Αφαίρεση προώθησης από καμπάνια", "description": "Είστε έτοιμοι να αφαιρέσετε {{count}} προώθηση(εις) από την καμπάνια. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί."}, "alreadyAdded": "Αυτή η προώθηση έχει ήδη προστεθεί στην καμπάνια.", "alreadyAddedDiffCampaign": "Αυτή η προώθηση έχει ήδη προστεθεί σε μια διαφορετική καμπάνια ({{name}}).", "currencyMismatch": "Το νόμισμα της προώθησης και της καμπάνιας δεν ταιριάζει", "toast": {"success": "Προστέθηκαν με επιτυχία {{count}} προώθηση(εις) στην καμπάνια"}, "add": {"list": {"noRecordsMessage": "Δημιουργήστε πρώτα μια προώθηση."}}, "list": {"noRecordsMessage": "Δεν υπάρχουν προωθήσεις στην καμπάνια."}}, "deleteCampaignWarning": "Είστε έτοιμοι να διαγράψετε την καμπάνια {{name}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "totalSpend": "{{amount}} {{currency}}"}, "priceLists": {"domain": "Τιμο<PERSON>ατάλογοι", "subtitle": "Δημιουργήστε εκπτώσεις ή παρακάμψτε τιμές για συγκεκριμένες συνθήκες.", "delete": {"confirmation": "Είστε έτοιμοι να διαγράψετε τον τιμοκατάλογο {{title}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "successToast": "Ο τιμοκα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{title}} διαγράφηκε με επιτυχία."}, "create": {"header": "Δημιουργ<PERSON>α τιμοκαταλόγου", "subheader": "Δημιουργήστε έναν νέο τιμοκατάλογο για να διαχειριστείτε τις τιμές των προϊόντων σας.", "tabs": {"details": "Λεπτομέρειες", "products": "Προϊόντα", "prices": "Τιμές"}, "successToast": "Ο τιμοκα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{title}} δημιουργήθηκε με επιτυχία.", "products": {"list": {"noRecordsMessage": "Δημιουργήστε πρώτα ένα προϊόν."}}}, "edit": {"header": "Επεξεργασία τιμοκαταλόγου", "successToast": "Ο τιμοκα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{title}} ενημερώθηκε με επιτυχία."}, "configuration": {"header": "Διαμόρφωση", "edit": {"header": "Επεξεργασία διαμόρφωσης τιμοκαταλόγου", "description": "Επεξεργαστείτε τη διαμόρφωση του τιμοκαταλόγου.", "successToast": "Η διαμόρφωση του τιμοκαταλόγου ενημερώθηκε με επιτυχία."}}, "products": {"header": "Προϊόντα", "actions": {"addProducts": "Προσθήκη προϊόντων", "editPrices": "Επεξεργασία τιμών"}, "delete": {"confirmation_one": "Είστε έτοιμοι να διαγράψετε τις τιμές για {{count}} προϊόν στον τιμοκατάλογο. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "confirmation_other": "Είστε έτοιμοι να διαγράψετε τις τιμές για {{count}} προϊόντα στον τιμοκατάλογο. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "successToast_one": "Διαγράφηκαν με επιτυχία οι τιμές για {{count}} προϊόν.", "successToast_other": "Διαγράφηκαν με επιτυχία οι τιμές για {{count}} προϊόντα."}, "add": {"successToast": "Οι τιμές προστέθηκαν με επιτυχία στον τιμοκατάλογο."}, "edit": {"successToast": "Οι τιμές ενημερώθηκαν με επιτυχία."}}, "fields": {"priceOverrides": {"label": "Παρεκκλίσεις τιμών", "header": "Παρεκκλίσεις τιμών"}, "status": {"label": "Κατάσταση", "options": {"active": "Ενεργή", "draft": "Πρόχειρο", "expired": "Ληγμένη", "scheduled": "Προγραμματισμένη"}}, "type": {"label": "Τύπος", "hint": "Επιλέξτε τον τύπο του τιμοκαταλόγου που θέλετε να δημιουργήσετε.", "options": {"sale": {"label": "Έκπτωση", "description": "Οι τιμές έκπτωσης είναι προσωρινές αλλαγές τιμών για προϊόντα."}, "override": {"label": "Παράκαμψη", "description": "Οι παρεκκλίσεις χρησιμοποιούνται συνήθως για τη δημιουργία τιμών ειδικών για τον πελάτη."}}}, "startsAt": {"label": "Ο τιμοκα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> έχει ημερομηνία έναρξης;", "hint": "Προγραμματίστε τον τιμοκατ<PERSON><PERSON>ο<PERSON><PERSON> να ενεργοποιηθεί στο μέλλον."}, "endsAt": {"label": "Ο τιμοκα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> έχει ημερομηνία λήξης;", "hint": "Προγραμματίστε τον τιμοκατ<PERSON>λογο να απενεργοποιηθεί στο μέλλον."}, "customerAvailability": {"header": "Επιλέξτε ομάδες πελατών", "label": "Διαθεσιμότητα πελατών", "hint": "Επιλέξτε σε ποιες ομάδες πελατών θα πρέπει να εφαρμοστεί ο τιμοκατάλογος.", "placeholder": "Αναζήτηση για ομάδες πελατών", "attribute": "Ομάδες πελατών"}}}, "profile": {"domain": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON>", "manageYourProfileDetails": "Διαχειριστείτε τα στοιχεία του προφίλ σας.", "fields": {"languageLabel": "Γλώσσα", "usageInsightsLabel": "Στοιχεία χρήσης"}, "edit": {"header": "Επεξεργασία προφίλ", "languageHint": "Η γλώσσα που θέλετε να χρησιμοποιήσετε στον πίνακα ελέγχου διαχειριστή. Αυτό δεν αλλάζει τη γλώσσα του καταστήματός σας.", "languagePlaceholder": "Επιλέξτε γλώσσα", "usageInsightsHint": "Μοιραστείτε τα στοιχεία χρήσης και βοηθήστε μας να βελτιώσουμε την Medusa. Μπορείτε να διαβάσετε περισσότερα σχετικά με το τι συλλέγουμε και πώς το χρησιμοποιούμε στην τεκμηρίωσή μας."}, "toast": {"edit": "Οι αλλαγές προφίλ αποθηκεύτηκαν"}}, "users": {"domain": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON>", "editUser": "Επεξεργασία χρήστη", "inviteUser": "Πρόσκληση χρήστη", "inviteUserHint": "Προσκαλ<PERSON>στε έναν νέο χρήστη στο κατάστημά σας.", "sendInvite": "Αποστολή πρόσκλησης", "pendingInvites": "Εκκρεμείς προσκλήσεις", "deleteInviteWarning": "Είστε έτοιμοι να διαγράψετε την πρόσκληση για {{email}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "resendInvite": "Επαναποστολή πρόσκλησης", "copyInviteLink": "Αντιγραφ<PERSON> συνδέσμου πρόσκλησης", "expiredOnDate": "Έληξε στις {{date}}", "validFromUntil": "Ισχύει από {{from}} - {{until}}", "acceptedOnDate": "Έγινε αποδεκτή στις {{date}}", "inviteStatus": {"accepted": "Έγινε αποδεκτή", "pending": "Εκκρεμεί", "expired": "Έληξε"}, "roles": {"admin": "Διαχειριστής", "developer": "Προγραμματιστής", "member": "<PERSON><PERSON><PERSON><PERSON>"}, "list": {"empty": {"heading": "Δε βρέθηκαν χρήστες", "description": "Μό<PERSON>ις προσκληθεί ένας χρήστης, θα εμφανιστεί εδώ."}, "filtered": {"heading": "Κανένα αποτέλεσμα", "description": "Δε βρέθηκε κανένας χρήστης με αυτά τα κριτήρια φιλτραρίσματος."}}, "deleteUserWarning": "Είστε έτοιμοι να διαγράψετε τον χρήστη {{name}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "deleteUserSuccess": "Ο χρήστης {{name}} διαγράφηκε επιτυχώς.", "invite": "Πρόσκληση"}, "store": {"domain": "Κατάστημα", "manageYourStoresDetails": "Διαχειριστείτε τα στοιχεία του καταστήματός σας", "editStore": "Επεξεργ<PERSON><PERSON><PERSON><PERSON> καταστήματος", "defaultCurrency": "Προεπιλεγμένο νόμισμα", "defaultRegion": "Προεπιλεγμένη περιοχή", "swapLinkTemplate": "Πρότυπο συνδέσμου ανταλλαγής", "paymentLinkTemplate": "Πρότυπο συνδέσμου πληρωμής", "inviteLinkTemplate": "Πρότυπο συνδέσμου πρόσκλησης", "currencies": "Νομίσματα", "addCurrencies": "Προσθήκη νομισμάτων", "enableTaxInclusivePricing": "Ενεργοποίηση τιμολόγησης με ΦΠΑ", "disableTaxInclusivePricing": "Απενεργοποίηση τιμολόγησης με ΦΠΑ", "removeCurrencyWarning_one": "Είστε έτοιμοι να αφαιρέσετε {{count}} νόμισμα από το κατάστημά σας. Βεβαιωθείτε ότι έχετε αφαιρέσει όλες τις τιμές που χρησιμοποιούν το νόμισμα πριν συνεχίσετε.", "removeCurrencyWarning_other": "Είστε έτοιμοι να αφαιρέσετε {{count}} νομίσματα από το κατάστημά σας. Βεβαιωθείτε ότι έχετε αφαιρέσει όλες τις τιμές που χρησιμοποιούν τα νομίσματα πριν συνεχίσετε.", "currencyAlreadyAdded": "Το νόμισμα έχει ήδη προστεθεί στο κατάστημά σας.", "edit": {"header": "Επεξεργ<PERSON><PERSON><PERSON><PERSON> καταστήματος"}, "toast": {"update": "Το κατάστημα ενημερώθηκε με επιτυχία", "currenciesUpdated": "Τα νομίσματα ενημερώθηκαν με επιτυχία", "currenciesRemoved": "Τα νομίσματα αφαιρέθηκαν με επιτυχία από το κατάστημα", "updatedTaxInclusivitySuccessfully": "Η τιμολόγηση με ΦΠΑ ενημερώθηκε με επιτυχία"}}, "regions": {"domain": "Περιοχ<PERSON>ς", "subtitle": "Μια περιοχή είναι μια περιοχή στην οποία πουλάτε προϊόντα. Μπορεί να καλύπτει πολλές χώρες και έχει διαφορετικούς φορολογικούς συντελεστές, παρόχους και νόμισμα.", "createRegion": "Δημιουργία περιοχής", "createRegionHint": "Διαχειριστείτε φορολογικο<PERSON>ς συντελεστές και παρόχους για ένα σύνολο χωρών.", "addCountries": "Προσθήκη χωρών", "editRegion": "Επεξεργασία περιοχής", "countriesHint": "Προσθέστε τις χώρες που περιλαμβάνονται σε αυτήν την περιοχή.", "deleteRegionWarning": "Είστε έτοιμοι να διαγράψετε την περιοχή {{name}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "removeCountriesWarning_one": "Είστε έτοιμοι να αφαιρέσετε {{count}} χώρα από την περιοχή. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "removeCountriesWarning_other": "Είστε έτοιμοι να αφαιρέσετε {{count}} χώρες από την περιοχή. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "removeCountryWarning": "Είστε έτοιμοι να αφαιρέσετε τη χώρα {{name}} από την περιοχή. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "automaticTaxesHint": "Όταν είναι ενεργοποιημένο, οι φόροι θα υπολογίζονται μόνο κατά την ολοκλήρωση αγοράς με βάση τη διεύθυνση αποστολής.", "taxInclusiveHint": "Όταν είναι ενεργοποιημένο, οι τιμές στην περιοχή θα περιλαμβάνουν ΦΠΑ.", "providersHint": "Προσθέστε ποιοι πάροχοι πληρωμών είναι διαθέσιμοι σε αυτήν την περιοχή.", "shippingOptions": "Επιλο<PERSON><PERSON><PERSON> αποστολής", "deleteShippingOptionWarning": "Είστε έτοιμοι να διαγράψετε την επιλογή αποστολής {{name}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "return": "Επιστροφή", "outbound": "Εξερχόμενα", "priceType": "Τύπος τιμής", "flatRate": "Σταθερή τιμή", "calculated": "Υπολογιζόμενη", "list": {"noRecordsMessage": "Δημιουργήστε μια περιοχή για τις περιοχές στις οποίες πουλάτε."}, "toast": {"delete": "Η περιοχή διαγράφηκε με επιτυχία", "edit": "Η επεξεργασία περιοχής αποθηκεύτηκε", "create": "Η περιοχή δημιουργήθηκε με επιτυχία", "countries": "Οι χώρες της περιοχής ενημερώθηκαν με επιτυχία"}, "shippingOption": {"createShippingOption": "Δημιουργ<PERSON>α επιλογής αποστολής", "createShippingOptionHint": "Δημιουργήστε μια νέα επιλογή αποστολής για την περιοχή.", "editShippingOption": "Επεξεργα<PERSON>ία επιλογής αποστολής", "fulfillmentMethod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εκπλήρωσης", "type": {"outbound": "Εξερχόμενα", "outboundHint": "Χρησιμοποιήστε αυτό εάν δημιουργείτε μια επιλογή αποστολής για την αποστολή προϊόντων στον πελάτη.", "return": "Επιστροφή", "returnHint": "Χρησιμοποιήστε αυτό εάν δημιουργείτε μια επιλογή αποστολής για τον πελάτη να σας επιστρέψει προϊόντα."}, "priceType": {"label": "Τύπος τιμής", "flatRate": "Σταθερή τιμή", "calculated": "Υπολογιζόμενη"}, "availability": {"adminOnly": "Μόνο διαχειριστής", "adminOnlyHint": "Όταν είναι ενεργοποιημένη, η επιλογή αποστολής θα είναι διαθέσιμη μόνο στον πίνακα ελέγχου διαχειριστή και όχι στο κατάστημα."}, "taxInclusiveHint": "Όταν είναι ενεργοποιημένη, η τιμή της επιλογής αποστολής θα περιλαμβάνει ΦΠΑ.", "requirements": {"label": "Απαιτή<PERSON><PERSON>ις", "hint": "Καθορίστε τις απαιτήσεις για την επιλογή αποστολής."}}}, "taxes": {"domain": "Φορολογικ<PERSON>ς περιοχές", "domainDescription": "Διαχειριστείτε τη φορολογική σας περιοχή", "countries": {"taxCountriesHint": "Οι φορολογικές ρυθμίσεις ισχύουν για τις χώρες που αναφέρονται."}, "settings": {"editTaxSettings": "Επεξεργα<PERSON>ία φορολογικών ρυθμίσεων", "taxProviderLabel": "Φορ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πάροχος", "systemTaxProviderLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> φορολογικού συστήματος", "calculateTaxesAutomaticallyLabel": "Αυτόματος υπολογισμός φόρων", "calculateTaxesAutomaticallyHint": "Όταν είναι ενεργοποιημένη, οι φορολογικοί συντελεστές θα υπολογίζονται αυτόματα και θα εφαρμόζονται στα καλάθια. Όταν είναι απενεργοποιημένη, οι φόροι πρέπει να υπολογίζονται χειροκίνητα κατά την ολοκλήρωση αγοράς. Οι χειροκίνητοι φόροι συνιστώνται για χρήση με τρίτους φορολογικούς παρόχους.", "applyTaxesOnGiftCardsLabel": "Εφαρμογή φόρων σε δωροκάρτες", "applyTaxesOnGiftCardsHint": "Όταν είναι ενεργοποιημένη, οι φόροι θα εφαρμόζονται στις δωροκάρτες κατά την ολοκλήρωση αγοράς. Σε ορισμένες χώρες, οι φορολογικοί κανονισμοί απαιτούν την εφαρμογή φόρων στις δωροκάρτες κατά την αγορά.", "defaultTaxRateLabel": "Προεπιλε<PERSON><PERSON><PERSON><PERSON>ος φορολογικ<PERSON>ς συντελεστής", "defaultTaxCodeLabel": "Προεπιλεγ<PERSON><PERSON><PERSON>ος φορολογικ<PERSON>ς κωδικός"}, "defaultRate": {"sectionTitle": "Προεπιλε<PERSON><PERSON><PERSON><PERSON>ος φορολογικ<PERSON>ς συντελεστής"}, "taxRate": {"sectionTitle": "Φορολογι<PERSON><PERSON><PERSON> συντελεστές", "createTaxRate": "Δημιουργ<PERSON><PERSON> φορολογικού συντελεστή", "createTaxRateHint": "Δημιουργ<PERSON><PERSON>τε έναν νέο φορολογικ<PERSON> συντελεστή για την περιοχή.", "deleteRateDescription": "Είστε έτοιμοι να διαγράψετε τον φορολογικό συντελεστή {{name}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "editTaxRate": "Επεξεργασ<PERSON>α φορολογικού συντελεστή", "editRateAction": "Επεξεργ<PERSON><PERSON><PERSON>α συντελεστή", "editOverridesAction": "Επεξεργα<PERSON>ία παρεκκλίσεων", "editOverridesTitle": "Επεξεργα<PERSON><PERSON><PERSON> παρεκκλίσεων φορολογικού συντελεστή", "editOverridesHint": "Καθορίστε τις παρεκκλίσεις για τον φορολογικό συντελεστή.", "deleteTaxRateWarning": "Είστε έτοιμοι να διαγράψετε τον φορολογικό συντελεστή {{name}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "productOverridesLabel": "Παρεκκλίσεις προϊόντων", "productOverridesHint": "Καθορίστε τις παρεκκλίσεις προϊόντων για τον φορολογικό συντελεστή.", "addProductOverridesAction": "Προσθήκη παρεκκλίσεων προϊόντων", "productTypeOverridesLabel": "Παρεκκλίσεις τύπου προϊόντος", "productTypeOverridesHint": "Καθορίστε τις παρεκκλίσεις τύπου προϊόντος για τον φορολογικό συντελεστή.", "addProductTypeOverridesAction": "Προσθήκη παρεκκλίσεων τύπου προϊόντος", "shippingOptionOverridesLabel": "Παρεκκλίσεις επιλογής αποστολής", "shippingOptionOverridesHint": "Καθορίστε τις παρεκκλίσεις επιλογής αποστολής για τον φορολογικό συντελεστή.", "addShippingOptionOverridesAction": "Προσθήκη παρεκκλίσεων επιλογής αποστολής", "productOverridesHeader": "Προϊόντα", "productTypeOverridesHeader": "Τύποι προϊόντων", "shippingOptionOverridesHeader": "Επιλο<PERSON><PERSON><PERSON> αποστολής"}}, "locations": {"domain": "Τοποθεσίες", "editLocation": "Επεξεργασία τοποθεσίας", "addSalesChannels": "Προσθήκη καναλιών πωλήσεων", "noLocationsFound": "Δεν βρέθηκαν τοποθεσίες", "selectLocations": "Επιλέξτε τοποθεσίες που διαθέτουν το αντικείμενο.", "deleteLocationWarning": "Είστε έτοιμοι να διαγράψετε την τοποθεσία {{name}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "removeSalesChannelsWarning_one": "Είστε έτοιμοι να αφαιρέσετε {{count}} κανάλι πωλήσεων από την τοποθεσία.", "removeSalesChannelsWarning_other": "Είστε έτοιμοι να αφαιρέσετε {{count}} κανάλια πωλήσεων από την τοποθεσία.", "toast": {"create": "Η τοποθεσία δημιουργήθηκε με επιτυχία", "update": "Η τοποθεσία ενημερώθηκε με επιτυχία", "removeChannel": "Το κανάλι πωλήσεων αφαιρέθηκε με επιτυχία"}}, "reservations": {"domain": "Κρατήσεις", "subtitle": "Διαχειριστείτε την κρατημένη ποσότητα των αντικειμένων αποθέματος.", "deleteWarning": "Είστε έτοιμοι να διαγράψετε μια κράτηση. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί."}, "salesChannels": {"domain": "Κανάλια πωλήσεων", "subtitle": "Διαχειριστείτε τα online και offline κανάλια στα οποία πουλάτε προϊόντα.", "createSalesChannel": "Δημιουργ<PERSON><PERSON> καναλιού πωλήσεων", "createSalesChannelHint": "Δημιουργήστε ένα νέο κανάλι πωλήσεων για να πουλήσετε τα προϊόντα σας.", "enabledHint": "Καθορίστε εάν το κανάλι πωλήσεων είναι ενεργοποιημένο.", "removeProductsWarning_one": "Είστε έτοιμοι να αφαιρέσετε {{count}} προϊόν από {{sales_channel}}.", "removeProductsWarning_other": "Είστε έτοιμοι να αφαιρέσετε {{count}} προϊόντα από {{sales_channel}}.", "addProducts": "Προσθήκη προϊόντων", "editSalesChannel": "Επεξεργασία καναλιού πωλήσεων", "productAlreadyAdded": "Το προϊόν έχει ήδη προστεθεί στο κανάλι πωλήσεων.", "deleteSalesChannelWarning": "Είστε έτοιμοι να διαγράψετε το κανάλι πωλήσεων {{name}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "toast": {"create": "Το κανάλι πωλήσεων δημιουργήθηκε με επιτυχία", "update": "Το κανάλι πωλήσεων ενημερώθηκε με επιτυχία", "delete": "Το κανάλι πωλήσεων διαγράφηκε με επιτυχία"}, "tooltip": {"cannotDeleteDefault": "Δεν είναι δυνατή η διαγραφή του προεπιλεγμένου καναλιού πωλήσεων"}, "products": {"list": {"noRecordsMessage": "Δεν υπάρχουν προϊόντα στο κανάλι πωλήσεων."}, "add": {"list": {"noRecordsMessage": "Δημιουργήστε πρώτα ένα προϊόν."}}}}, "apiKeyManagement": {"domain": {"publishable": "Κλειδιά API που μπορούν να δημοσιευτούν", "secret": "Μυστικά κλειδιά API"}, "subtitle": {"publishable": "Διαχειριστείτε τα κλειδιά API που χρησιμοποιούνται στο κατάστημα για να περιορίσετε το εύρος των αιτημάτων σε συγκεκριμένα κανάλια πωλήσεων.", "secret": "Διαχειριστείτε τα κλειδιά API που χρησιμοποιούνται για τον έλεγχο ταυτότητας των χρηστών διαχειριστή στις εφαρμογές διαχειριστή."}, "status": {"active": "Ενεργό", "revoked": "Ανακληθέν"}, "type": {"publishable": "Δημοσιεύσιμο", "secret": "Μυστικό"}, "create": {"createPublishableHeader": "Δημιουργία κλειδιού API που μπορεί να δημοσιευτεί", "createPublishableHint": "Δημιουργήστε ένα νέο κλειδί API που μπορεί να δημοσιευτεί για να περιορίσετε το εύρος των αιτημάτων σε συγκεκριμένα κανάλια πωλήσεων.", "createSecretHeader": "Δημιουργ<PERSON>α μυστικού κλειδιού API", "createSecretHint": "Δημιουργήστε ένα νέο μυστικ<PERSON> κλειδί API για να αποκτήσετε πρόσβαση στο API της Medusa ως χρήστης διαχειριστής με έλεγχο ταυτότητας.", "secretKeyCreatedHeader": "Δημιουργήθηκε μυστικό κλειδί", "secretKeyCreatedHint": "Το νέο σας μυστικό κλειδί έχει δημιουργηθεί. Αντιγράψτε το και αποθηκεύστε το με ασφάλεια τώρα. Αυτή είναι η μόνη φορά που θα εμφανιστεί.", "copySecretTokenSuccess": "Το μυστικό κλειδί αντιγράφηκε στο πρόχειρο.", "copySecretTokenFailure": "Αποτυχία αντιγραφής του μυστικού κλειδιού στο πρόχειρο.", "successToast": "Το κλειδί API δημιουργήθηκε με επιτυχία."}, "edit": {"header": "Επεξεργασία κλειδιού API", "description": "Επεξεργαστείτε τον τίτλο του κλειδιού API.", "successToast": "Το κλειδί API {{title}} ενημερώθηκε με επιτυχία."}, "salesChannels": {"title": "Προσθήκη καναλιών πωλήσεων", "description": "Προσθέστε τα κανάλια πωλήσεων στα οποία θα πρέπει να περιορίζεται το κλειδί API.", "successToast_one": "{{count}} καν<PERSON><PERSON>ι πωλήσεων προστέθηκε με επιτυχία στο κλειδί API.", "successToast_other": "{{count}} καν<PERSON><PERSON><PERSON>α πωλήσεων προστέθηκαν με επιτυχία στο κλειδί API.", "alreadyAddedTooltip": "Το κανάλι πωλήσεων έχει ήδη προστεθεί στο κλειδί API.", "list": {"noRecordsMessage": "Δεν υπάρχουν κανάλια πωλήσεων στο εύρος του κλειδιού API που μπορεί να δημοσιευτεί."}}, "delete": {"warning": "Είστε έτοιμοι να διαγράψετε το κλειδί API {{title}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "successToast": "Το κλειδί API {{title}} διαγράφηκε με επιτυχία."}, "revoke": {"warning": "Είστε έτοιμοι να ανακαλέσετε το κλειδί API {{title}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "successToast": "Το κλειδί API {{title}} ανακλήθηκε με επιτυχία."}, "addSalesChannels": {"list": {"noRecordsMessage": "Δημιουργήστε πρώτα ένα κανάλι πωλήσεων."}}, "removeSalesChannel": {"warning": "Είστε έτοιμοι να αφαιρέσετε το κανάλι πωλήσεων {{name}} από το κλειδί API. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "warningBatch_one": "Είστε έτοιμοι να αφαιρέσετε {{count}} κανάλι πωλήσεων από το κλειδί API. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "warningBatch_other": "Είστε έτοιμοι να αφαιρέσετε {{count}} κανάλια πωλήσεων από το κλειδί API. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "successToast": "Το κανάλι πωλήσεων αφαιρέθηκε με επιτυχία από το κλειδί API.", "successToastBatch_one": "{{count}} καν<PERSON><PERSON><PERSON> πωλήσεων αφαιρέθηκε με επιτυχία από το κλειδί API.", "successToastBatch_other": "{{count}} καν<PERSON><PERSON><PERSON><PERSON> πωλήσεων αφαιρέθηκαν με επιτυχία από το κλειδί API."}, "actions": {"revoke": "Ανάκληση κλειδιού API", "copy": "Αντιγραφή κλειδιού API", "copySuccessToast": "Το κλειδί API αντιγράφηκε στο πρόχειρο."}, "table": {"lastUsedAtHeader": "Τελευταία χρήση στις", "createdAtHeader": "Ανακλήθηκε στις"}, "fields": {"lastUsedAtLabel": "Τελευταία χρήση στις", "revokedByLabel": "Ανακλήθηκε από", "revokedAtLabel": "Ανακλήθηκε στις", "createdByLabel": "Δημιουργήθηκε από"}}, "returnReasons": {"domain": "Λόγοι επιστροφής", "subtitle": "Διαχειριστείτε τους λόγους για τα επιστρεφόμενα αντικείμενα.", "calloutHint": "Διαχειριστείτε τους λόγους για να κατηγοριοποιήσετε τις επιστροφές.", "editReason": "Επεξεργ<PERSON><PERSON><PERSON>α λόγου επιστροφής", "create": {"header": "Προσθήκη λόγου επιστροφής", "subtitle": "Καθορίστε τους πιο συνηθισμένους λόγους για επιστροφές.", "hint": "Δημιουργήστε έναν νέο λόγο επιστροφής για να κατηγοριοποιήσετε τις επιστροφές.", "successToast": "Ο λόγος επιστροφής {{label}} δημιουργήθηκε με επιτυχία."}, "edit": {"header": "Επεξεργ<PERSON><PERSON><PERSON>α λόγου επιστροφής", "subtitle": "Επεξεργαστείτε την τιμή του λόγου επιστροφής.", "successToast": "Ο λόγος επιστροφής {{label}} ενημερώθηκε με επιτυχία."}, "delete": {"confirmation": "Είστε έτοιμοι να διαγράψετε τον λόγο επιστροφής {{label}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "successToast": "Ο λόγος επιστροφής {{label}} διαγράφηκε με επιτυχία."}, "fields": {"value": {"label": "Τιμή", "placeholder": "wrong_size", "tooltip": "Η τιμή πρέπει να είναι ένα μονα<PERSON>ικ<PERSON> αναγνωριστικό για τον λόγο επιστροφής."}, "label": {"label": "Ετικέτα", "placeholder": "<PERSON><PERSON><PERSON><PERSON> μέγεθος"}, "description": {"label": "Περιγραφή", "placeholder": "Ο πελάτης έλαβε λάθος μέγεθος"}}}, "login": {"forgotPassword": "Ξεχάσατε τον κωδικό πρόσβασης; - Ε<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ά", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON> ήρθατε στην Medus<PERSON>", "hint": "Συνδεθείτε για να αποκτήσετε πρόσβαση στην περιοχή του λογαριασμού"}, "invite": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> ήρθατε στην Medus<PERSON>", "hint": "Δημιουργήστε τον λογαριασμ<PERSON> σας παρακάτω", "backToLogin": "Επιστροφή στη σύνδεση", "createAccount": "Δημιουργ<PERSON>α λογαριασμού", "alreadyHaveAccount": "Έχετε ήδη λογαριασμό; - Σύνδεση", "emailTooltip": "Το email σας δεν μπορεί να αλλάξει. Ε<PERSON>ν θέλετε να χρησιμοποιήσετε άλλο email, πρέπει να σταλεί μια νέα πρόσκληση.", "invalidInvite": "Η πρόσκληση δεν είναι έγκυρη ή έχει λήξει.", "successTitle": "Ο λογαριασμός σας έχει εγγραφεί", "successHint": "Ξεκινήστε με την <PERSON><PERSON>a Admin αμέσως.", "successAction": "Έναρξη <PERSON><PERSON><PERSON>", "invalidTokenTitle": "Το διακριτικ<PERSON> πρόσκλησης δεν είναι έγκυρο", "invalidTokenHint": "Δοκιμάστε να ζητήσετε έναν νέο σύνδεσμο πρόσκλησης.", "passwordMismatch": "Οι κωδικοί πρόσβασης δεν ταιριάζουν", "toast": {"accepted": "Η πρόσκληση έγινε αποδεκτή με επιτυχία"}}, "resetPassword": {"title": "Επανα<PERSON><PERSON><PERSON><PERSON> κωδικού πρόσβασης", "hint": "Εισαγάγετε το email σας παρακάτω και θα σας στείλουμε οδηγίες για το πώς να επαναφέρετε τον κωδικό πρόσβασής σας.", "email": "Email", "sendResetInstructions": "Αποστ<PERSON><PERSON><PERSON> οδηγιών επαναφορ<PERSON>ς", "backToLogin": "Επιστροφή στη σύνδεση", "newPasswordHint": "Επιλέξτε έναν νέο κωδικό πρόσβασης παρακάτω.", "invalidTokenTitle": "Το διακριτικ<PERSON> επαναφοράς δεν είναι έγκυρο", "invalidTokenHint": "Δοκιμάστε να ζητήσετε έναν νέο σύνδεσμο επαναφοράς.", "expiredTokenTitle": "Το διακριτικ<PERSON> επαναφορ<PERSON>ς έχει λήξει", "goToResetPassword": "Μετάβαση στην επανα<PERSON><PERSON><PERSON><PERSON> κωδικού πρόσβασης", "resetPassword": "Επανα<PERSON><PERSON><PERSON><PERSON> κωδικού πρόσβασης", "newPassword": "<PERSON><PERSON><PERSON> κωδικ<PERSON>ς πρόσβασης", "repeatNewPassword": "Επανάληψη νέου κωδικού πρόσβασης", "tokenExpiresIn": "Το διακριτικό λήγει σε {{time}} λεπτά", "successfulRequestTitle": "<PERSON><PERSON>ς στάλθηκε ένα email με επιτυχία", "successfulRequest": "Σας στείλαμε ένα email το οποίο μπορείτε να χρησιμοποιήσετε για να επαναφέρετε τον κωδικό πρόσβασής σας. Ελέγξτε τον φάκελο ανεπιθύμητης αλληλογραφίας εάν δεν το έχετε λάβει μετά από λίγα λεπτά.", "successfulResetTitle": "Η επαναφορά κωδικού πρόσβασης ολοκληρώθηκε με επιτυχία", "successfulReset": "Συνδεθείτε στη σελίδα σύνδεσης.", "passwordMismatch": "Οι κωδικοί πρόσβασης δεν ταιριάζουν", "invalidLinkTitle": "Ο σύνδεσμος επαναφ<PERSON>ρ<PERSON>ς δεν είναι έγκυρος", "invalidLinkHint": "Δοκιμάστε να επαναφέρετε τον κωδικό πρόσβασής σας ξανά."}, "workflowExecutions": {"domain": "Ροές εργασίας", "subtitle": "Δείτε και παρακολουθήστε τις εκτελέσεις ροών εργασίας στην εφαρμογή σας Medus<PERSON>.", "transactionIdLabel": "ID συναλλαγής", "workflowIdLabel": "ID ροής εργασίας", "progressLabel": "Πρ<PERSON><PERSON><PERSON>ος", "stepsCompletedLabel_one": "{{completed}} από {{count}} βήμα", "stepsCompletedLabel_other": "{{completed}} από {{count}} βήματα", "list": {"noRecordsMessage": "Δεν έχουν εκτελεστεί ακόμη ροές εργασίας."}, "history": {"sectionTitle": "Ιστορικό", "runningState": "Εκτελείται...", "awaitingState": "Σε αναμονή", "failedState": "Απέτυχε", "skippedState": "Παραλείφθηκε", "skippedFailureState": "Παραλείφθηκε (Αποτυχία)", "definitionLabel": "Ορισμός", "outputLabel": "Έξοδος", "compensateInputLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αποζημίωσης", "revertedLabel": "Αναιρέθηκε", "errorLabel": "Σφάλμα"}, "state": {"done": "Ολοκληρώθηκε", "failed": "Απέτυχε", "reverted": "Αναιρέθηκε", "invoking": "Κλήση", "compensating": "Αποζημίωση", "notStarted": "Δεν έχει ξεκινήσει"}, "transaction": {"state": {"waitingToCompensate": "Σε αναμονή αποζημίωσης"}}, "step": {"state": {"skipped": "Παραλείφθηκε", "skippedFailure": "Παραλείφθηκε (Αποτυχία)", "dormant": "Αδ<PERSON><PERSON><PERSON><PERSON>ς", "timeout": "Λήξη χρόνου"}}}, "productTypes": {"domain": "Τύποι προϊόντων", "subtitle": "Οργανώστε τα προϊόντα σας σε τύπους.", "create": {"header": "Δημιουργία τύπου προϊόντος", "hint": "Δημιουργήστε έναν νέο τύπο προϊόντος για να κατηγοριοποιήσετε τα προϊόντα σας.", "successToast": "Ο τύπος προϊόντος {{value}} δημιουργήθηκε με επιτυχία."}, "edit": {"header": "Επεξεργασία τύπου προϊόντος", "successToast": "Ο τύπος προϊόντος {{value}} ενημερώθηκε με επιτυχία."}, "delete": {"confirmation": "Είστε έτοιμοι να διαγράψετε τον τύπο προϊόντος {{value}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "successToast": "Ο τύπος προϊόντος {{value}} διαγράφηκε με επιτυχία."}, "fields": {"value": "Τιμή"}}, "productTags": {"domain": "Ετικέτες προϊόντων", "create": {"header": "Δημιουργία ετικέτας προϊόντος", "subtitle": "Δημιουργήστε μια νέα ετικέτα προϊόντος για να κατηγοριοποιήσετε τα προϊόντα σας.", "successToast": "Η ετικέτα προϊόντος {{value}} δημιουργήθηκε με επιτυχία."}, "edit": {"header": "Επεξεργασία ετικέτας προϊόντος", "subtitle": "Επεξεργαστείτε την τιμή της ετικέτας προϊόντος.", "successToast": "Η ετικέτα προϊόντος {{value}} ενημερώθηκε με επιτυχία."}, "delete": {"confirmation": "Είστε έτοιμοι να διαγράψετε την ετικέτα προϊόντος {{value}}. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "successToast": "Η ετικέτα προϊόντος {{value}} διαγράφηκε με επιτυχία."}, "fields": {"value": "Τιμή"}}, "notifications": {"domain": "Ειδοποιήσεις", "emptyState": {"title": "Δεν υπάρχουν ειδοποιήσεις", "description": "Δεν έχετε ειδοποιήσεις αυτήν τη στιγμή, αλλ<PERSON> μόλις τις έχετε, θα εμφανίζονται εδώ."}, "accessibility": {"description": "Οι ειδοποιήσεις σχετικά με τις δραστηριότητες της Medusa θα εμφανίζονται εδώ."}}, "errors": {"serverError": "Σφάλμα διακομιστή - Δοκιμάστε ξανά αργότερα.", "invalidCredentials": "<PERSON><PERSON><PERSON><PERSON> email ή κωδικός πρόσβασης"}, "statuses": {"scheduled": "Προγραμματισμένη", "expired": "Ληγμένη", "active": "Ενεργή", "inactive": "Ανενεργή", "draft": "Πρόχειρη", "enabled": "Ενεργοποιημένη", "disabled": "Απενεργοποιημένη"}, "labels": {"productVariant": "Παραλλαγή προϊόντος", "prices": "Τιμές", "available": "Διαθέσιμο", "inStock": "Σε απόθεμα", "added": "Προστέθηκε", "removed": "Αφαιρέθηκε", "from": "Από", "to": "<PERSON>ρ<PERSON>", "beaware": "Προσοχή", "loading": "Φόρτωση"}, "fields": {"amount": "Ποσό", "refundAmount": "Ποσ<PERSON> επιστροφής χρημάτων", "name": "Όνομα", "default": "Προεπιλογή", "lastName": "Επώνυμο", "firstName": "Όνομα", "title": "Τίτλος", "customTitle": "Προσαρμοσμένος τίτλος", "manageInventory": "Διαχείριση αποθέματος", "inventoryKit": "Έχει κιτ αποθέματος", "inventoryItems": "Αντικείμενα αποθέματος", "inventoryItem": "Αντικείμενο αποθέματος", "requiredQuantity": "Απαιτούμενη ποσότητα", "description": "Περιγραφή", "email": "Email", "password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβασης", "repeatPassword": "Επανάληψη κωδικού πρόσβασης", "confirmPassword": "Επιβεβαίωση κωδικού πρόσβασης", "newPassword": "<PERSON><PERSON><PERSON> κωδικ<PERSON>ς πρόσβασης", "repeatNewPassword": "Επανάληψη νέου κωδικού πρόσβασης", "categories": "Κατηγορίες", "shippingMethod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αποστολής", "configurations": "Διαμορφώσεις", "conditions": "Συνθήκες", "category": "Κατηγορία", "collection": "Συλλογή", "discountable": "Με δυνατότητα έκπτωσης", "handle": "<PERSON><PERSON>", "subtitle": "Υπότιτλος", "by": "Από", "item": "Αντικείμενο", "qty": "ποσ.", "limit": "Όριο", "tags": "Ετικέτες", "type": "Τύπος", "reason": "Λόγ<PERSON>", "none": "κανένας", "all": "όλα", "search": "Αναζήτηση", "percentage": "Ποσοστό", "sales_channels": "Κανάλια πωλήσεων", "customer_groups": "Ομάδες πελατών", "product_tags": "Ετικέτες προϊόντων", "product_types": "Τύποι προϊόντων", "product_collections": "Συλλογές προϊόντων", "status": "Κατάσταση", "code": "Κω<PERSON>ικ<PERSON>ς", "value": "Τιμή", "disabled": "Απενεργοποιημένο", "dynamic": "Δυναμικό", "normal": "Κανονικό", "years": "Χρόνια", "months": "Μήνες", "days": "Ημέρες", "hours": "Ώρες", "minutes": "Λεπτά", "totalRedemptions": "Συνολικ<PERSON>ς εξαργυρώσεις", "countries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paymentProviders": "Πάροχοι πληρωμών", "refundReason": "<PERSON><PERSON><PERSON><PERSON> επιστροφής χρημάτων", "fulfillmentProviders": "Πάροχοι εκπλήρωσης", "fulfillmentProvider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εκπλήρωσης", "providers": "Πάροχ<PERSON>ι", "availability": "Διαθεσιμότητα", "inventory": "Απόθεμα", "optional": "Προαιρετικό", "note": "Σημείωση", "automaticTaxes": "Αυτόματοι φόροι", "taxInclusivePricing": "Τιμολόγηση με ΦΠΑ", "currency": "Νόμισμα", "address": "Διεύθυνση", "address2": "Διαμέρισμα, σουίτα κ.λπ.", "city": "Π<PERSON><PERSON><PERSON>", "postalCode": "Ταχυδρο<PERSON><PERSON><PERSON><PERSON><PERSON> κώδικας", "country": "Χώ<PERSON><PERSON>", "state": "Πολιτεία", "province": "Επαρχία", "company": "Εταιρεία", "phone": "Τηλέφωνο", "metadata": "Μεταδεδομένα", "selectCountry": "Επιλέξτε χώρα", "products": "Προϊόντα", "variants": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς", "orders": "Παραγγελίες", "account": "Λογ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς", "total": "Σύνολο παραγγελίας", "paidTotal": "Συνολικό ποσό που καταγράφηκε", "totalExclTax": "Σύνολο χωρίς ΦΠΑ", "subtotal": "Μερικό σύνολο", "shipping": "Αποστολή", "outboundShipping": "Εξερχόμενη αποστολή", "returnShipping": "Αποστολή επιστροφής", "tax": "ΦΠΑ", "created": "Δημιουργήθηκε", "key": "Κλειδί", "customer": "Πελάτης", "date": "Ημερομηνία", "order": "Παραγγελία", "fulfillment": "Εκπλήρωση", "provider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "payment": "Πληρωμή", "items": "Αντικείμενα", "salesChannel": "Κανάλι πωλήσεων", "region": "Περιοχή", "discount": "Έκπτωση", "role": "Ρόλος", "sent": "Στάλθηκε", "salesChannels": "Κανάλια πωλήσεων", "product": "Προϊόν", "createdAt": "Δημιουργήθηκε", "updatedAt": "Ενημερώθηκε", "revokedAt": "Ανακλήθηκε στις", "true": "Αληθής", "false": "<PERSON><PERSON><PERSON><PERSON><PERSON>ς", "giftCard": "Δωροκάρτα", "tag": "Ετικέτα", "dateIssued": "Ημερομηνία έκδοσης", "issuedDate": "Ημερομηνία έκδοσης", "expiryDate": "Ημερομηνία λήξης", "price": "Τιμή", "priceTemplate": "Τιμή {{regionOrCurrency}}", "height": "Ύψος", "width": "<PERSON>λ<PERSON><PERSON><PERSON>", "length": "<PERSON><PERSON><PERSON><PERSON>", "weight": "<PERSON><PERSON><PERSON><PERSON>", "midCode": "Κωδικός MID", "hsCode": "Κωδικός HS", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "Ποσότητα αποθέματος", "barcode": "Barcode", "countryOfOrigin": "Χώρα προέλευσης", "material": "Υλικό", "thumbnail": "Μικρογραφία", "sku": "SKU", "managedInventory": "Διαχειριζόμενο απόθεμα", "allowBackorder": "Να επιτρέπονται οι παραγγελίες εκτός αποθέματος", "inStock": "Σε απόθεμα", "location": "Τοποθεσία", "quantity": "Ποσότητα", "variant": "Παραλλαγή", "id": "ID", "parent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minSubtotal": "Ελάχιστο μερικό σύνολο", "maxSubtotal": "Μέγιστο μερικό σύνολο", "shippingProfile": "Προ<PERSON><PERSON><PERSON> αποστολής", "summary": "Σύνοψη", "details": "Λεπτομέρειες", "label": "Ετικέτα", "rate": "Συντελεστής", "requiresShipping": "Απαιτεί αποστολή", "unitPrice": "Τιμή μονάδας", "startDate": "Ημερομηνία έναρξης", "endDate": "Ημερομηνία λήξης", "draft": "Πρόχειρο", "values": "Τιμές"}, "quotes": {"domain": "Προσφορές", "title": "Προσφορές", "subtitle": "Διαχειριστείτε προσφορές και προτάσεις πελατών", "noQuotes": "Δεν βρέθηκαν προσφορές", "noQuotesDescription": "Δεν υπάρχουν προσφορές αυτήν τη στιγμή. Δημιουργήστε μία από το κατάστημα.", "table": {"id": "ID Προσφοράς", "customer": "Πελάτης", "status": "Κατάσταση", "company": "Εταιρεία", "amount": "Ποσό", "createdAt": "Δημιουργήθηκε", "updatedAt": "Ενημερώθηκε", "actions": "Ενέργειες"}, "status": {"pending_merchant": "Εκκρεμεί από έμπορο", "pending_customer": "Εκκρεμεί από πελάτη", "merchant_rejected": "Απορρίφθηκε από έμπορο", "customer_rejected": "Απορρίφθηκε από πελάτη", "accepted": "Έγινε δεκτή", "unknown": "Άγνωστη"}, "actions": {"sendQuote": "Αποστολή προσφοράς", "rejectQuote": "Απόρριψη προσφοράς", "viewOrder": "Προβολή παραγγελίας"}, "details": {"header": "Λεπτομέρειες προσφοράς", "quoteSummary": "Σύνοψη προσφοράς", "customer": "Πελάτης", "company": "Εταιρεία", "items": "Αντικείμενα", "total": "Σύνολο", "subtotal": "Μερικό σύνολο", "shipping": "Αποστολή", "tax": "ΦΠΑ", "discounts": "Εκπτώσεις", "originalTotal": "Αρχικό σύνολο", "quoteTotal": "Σύνολο προσφοράς", "messages": "Μηνύματα", "actions": "Ενέργειες", "sendMessage": "Αποστολή μηνύματος", "send": "Αποστολή", "pickQuoteItem": "Επιλογή αντικειμένου προσφοράς", "selectQuoteItem": "Επιλέξτε ένα αντικείμενο προσφοράς για σχόλιο", "selectItem": "Επιλογή αντικειμένου", "manage": "Διαχείριση", "phone": "Τηλέφωνο", "spendingLimit": "Όριο δαπανών", "name": "Όνομα", "manageQuote": "Διαχείριση προσφοράς", "noItems": "Δεν υπάρχουν αντικείμενα σε αυτήν την προσφορά", "noMessages": "Δεν υπάρχουν μηνύματα για αυτήν την προσφορά"}, "items": {"title": "Προϊόν", "quantity": "Ποσότητα", "unitPrice": "Τιμή μονάδας", "total": "Σύνολο"}, "messages": {"admin": "Διαχειριστής", "customer": "Πελάτης", "placeholder": "Πληκτρολογήστε το μήνυμά σας εδώ..."}, "filters": {"status": "Φιλτράρισμα κατά κατάσταση"}, "confirmations": {"sendTitle": "Αποστολή προσφοράς", "sendDescription": "Είστε βέβαιοι ότι θέλετε να στείλετε αυτήν την προσφορά στον πελάτη;", "rejectTitle": "Απόρριψη προσφοράς", "rejectDescription": "Είστε βέβαιοι ότι θέλετε να απορρίψετε αυτήν την προσφορά;"}, "acceptance": {"message": "Η προσφορά έγινε δεκτή"}, "toasts": {"sendSuccess": "Η προσφορά στάλθηκε με επιτυχία στον πελάτη", "sendError": "Αποτυχ<PERSON><PERSON> αποστολής προσφοράς", "rejectSuccess": "Η προσφορά του πελάτη απορρίφθηκε με επιτυχία", "rejectError": "Αποτυχία απόρριψης προσφοράς", "messageSuccess": "Το μήνυμα στάλθηκε με επιτυχία στον πελάτη", "messageError": "Αποτυχ<PERSON>α αποστολής μηνύματος", "updateSuccess": "Η προσφορά ενημερώθηκε με επιτυχία"}, "manage": {"overridePriceHint": "Παράκαμψη της αρχικής τιμής για αυτό το αντικείμενο", "updatePrice": "Ενημέρωση τιμής"}}, "companies": {"domain": "Εταιρείες", "title": "Εταιρείες", "subtitle": "Διαχειριστείτε επιχειρηματικές σχέσεις", "noCompanies": "Δεν βρέθηκαν εταιρείες", "noCompaniesDescription": "Δημιουργήστε την πρώτη σας εταιρεία για να ξεκινήσετε.", "notFound": "Η εταιρεία δεν βρέθηκε", "table": {"name": "Όνομα", "phone": "Τηλέφωνο", "email": "Email", "address": "Διεύθυνση", "employees": "Υπάλληλοι", "customerGroup": "Ομάδα πελατών", "actions": "Ενέργειες"}, "fields": {"name": "Όνομα εταιρείας", "email": "Email", "phone": "Τηλέφωνο", "website": "Ιστοσελίδα", "address": "Διεύθυνση", "city": "Π<PERSON><PERSON><PERSON>", "state": "Πολιτεία", "zip": "Ταχυδρο<PERSON><PERSON><PERSON><PERSON><PERSON> κώδικας", "zipCode": "Ταχυδρο<PERSON><PERSON><PERSON><PERSON><PERSON> κώδικας", "country": "Χώ<PERSON><PERSON>", "currency": "Νόμισμα", "logoUrl": "URL λογότυπου", "description": "Περιγραφή", "employees": "Υπάλληλοι", "customerGroup": "Ομάδα πελατών", "approvalSettings": "Ρυθμίσεις έγκρισης"}, "placeholders": {"name": "Εισάγετε όνομα εταιρείας", "email": "Εισάγετε διεύθυνση email", "phone": "Εισάγετε αριθμό τηλεφώνου", "website": "Εισάγετε URL ιστοσελίδας", "address": "Εισάγετε διεύθυνση", "city": "Εισάγετε πόλη", "state": "Εισάγετε πολιτεία", "zip": "Εισάγετε ταχυδρομικό κώδικα", "logoUrl": "Εισάγετε URL λογότυπου", "description": "Εισάγετε περιγραφή εταιρείας", "selectCountry": "Επιλέξτε χώρα", "selectCurrency": "Επιλέξτε νόμισμα"}, "validation": {"nameRequired": "Το όνομα εταιρείας είναι υποχρεωτικό", "emailRequired": "Το email είναι υποχρεωτικό", "emailInvalid": "Μη έγκυρη διεύθυνση email", "addressRequired": "Η διεύθυνση είναι υποχρεωτική", "cityRequired": "Η πόλη είναι υποχρεωτική", "stateRequired": "Η πολιτεία είναι υποχρεωτική", "zipRequired": "Ο ταχυδρο<PERSON><PERSON><PERSON><PERSON><PERSON> κώδικας είναι υποχρεωτικός"}, "create": {"title": "Δημιουργία εταιρείας", "description": "Δημιουργήστε μια νέα εταιρεία για τη διαχείριση επιχειρηματικών σχέσεων.", "submit": "Δημιουργία εταιρείας"}, "edit": {"title": "Επεξεργασία εταιρείας", "submit": "Ενημέρωση εταιρείας"}, "details": {"actions": "Ενέργειες"}, "approvals": {"requiresAdminApproval": "Απαιτεί έγκριση διαχειριστή", "requiresSalesManagerApproval": "Απαιτεί έγκριση διευθυντή πωλήσεων", "noApprovalRequired": "Δεν απαιτείται έγκριση"}, "deleteWarning": "Αυτό θα διαγράψει μόνιμα την εταιρεία και όλα τα σχετικά δεδομένα.", "approvalSettings": {"title": "Ρυθμίσεις έγκρισης", "requiresAdminApproval": "Απαιτεί έγκριση διαχειριστή", "requiresSalesManagerApproval": "Απαιτεί έγκριση διευθυντή πωλήσεων", "requiresAdminApprovalDesc": "Οι παραγγελίες από αυτήν την εταιρεία απαιτούν έγκριση διαχειριστή πριν την επεξεργασία", "requiresSalesManagerApprovalDesc": "Οι παραγγελίες από αυτήν την εταιρεία απαιτούν έγκριση διευθυντή πωλήσεων πριν την επεξεργασία", "updateSuccess": "Οι ρυθμίσεις έγκρισης ενημερώθηκαν επιτυχώς", "updateError": "Αποτυχία ενημέρωσης ρυθμίσεων έγκρισης"}, "customerGroup": {"title": "Διαχείριση ομάδας πελατών", "hint": "Αντιστοιχίστε αυτήν την εταιρεία σε μια ομάδα πελατών για να εφαρμόσετε τιμές και δικαιώματα ομάδας.", "name": "Όνομα ομάδας πελατών", "groupName": "Ομάδα πελατών", "actions": "Ενέργειες", "add": "Προσθήκη", "remove": "Αφαίρεση", "description": "Διαχειριστείτε ομάδες πελατών για αυτήν την εταιρεία", "noGroups": "Δεν υπάρχουν διαθέσιμες ομάδες πελατών", "addSuccess": "Η εταιρεία προστέθηκε επιτυχώς στην ομάδα πελατών", "addError": "Αποτυχία προσθήκης εταιρείας στην ομάδα πελατών", "removeSuccess": "Η εταιρεία αφαιρέθηκε επιτυχώς από την ομάδα πελατών", "removeError": "Αποτυχία αφαίρεσης εταιρείας από την ομάδα πελατών"}, "actions": {"edit": "Επεξεργασία εταιρείας", "editDetails": "Επεξεργ<PERSON><PERSON><PERSON><PERSON> λεπτομερειών", "manageCustomerGroup": "Διαχείριση ομάδας πελατών", "approvalSettings": "Ρυθμίσεις έγκρισης", "delete": "Διαγρα<PERSON>ή εταιρείας", "confirmDelete": "Επιβεβαίωση διαγραφής"}, "delete": {"title": "Διαγρα<PERSON>ή εταιρείας", "description": "Είστε βέβαιοι ότι θέλετε να διαγράψετε αυτήν την εταιρεία; Αυτή η ενέργεια δεν μπορεί να αναιρεθεί."}, "employees": {"title": "Υπάλληλοι", "noEmployees": "Δεν βρέθηκαν υπάλληλοι για αυτήν την εταιρεία", "name": "Όνομα", "email": "Email", "phone": "Τηλέφωνο", "role": "Ρόλος", "spendingLimit": "Όριο δαπανών", "admin": "Διαχειριστής", "employee": "Υπάλληλος", "add": "Προσθήκη υπαλλήλου", "create": {"title": "Δημιουργ<PERSON><PERSON> υπαλλήλου", "success": "Ο υπάλληλος δημιουργήθηκε επιτυχώς", "error": "Αποτυχία δημιουργ<PERSON>ας υπαλλήλου"}, "form": {"details": "Λεπτομερείς πληροφορίες", "permissions": "Δικαιώματα", "firstName": "Όνομα", "lastName": "Επώνυμο", "email": "Email", "phone": "Τηλέφωνο", "spendingLimit": "Όριο δαπανών", "adminAccess": "Πρόσβαση διαχειριστή", "isAdmin": "Είναι διαχειριστής", "isAdminDesc": "Παραχώρηση δικαιωμάτων διαχειριστή σε αυτόν τον υπάλληλο", "isAdminTooltip": "Οι διαχειριστές μπορούν να διαχειρίζονται τις ρυθμίσεις εταιρείας και άλλους υπαλλήλους", "firstNamePlaceholder": "Εισάγετε όνομα", "lastNamePlaceholder": "Εισάγετε επώνυμο", "emailPlaceholder": "Εισάγετε διεύθυνση email", "phonePlaceholder": "Εισάγετε αριθμό τηλεφώνου", "spendingLimitPlaceholder": "Εισάγετε όριο δαπανών", "save": "Αποθήκευση", "saving": "Αποθήκευση..."}, "delete": {"confirmation": "Είστε βέβαιοι ότι θέλετε να διαγράψετε αυτόν τον υπάλληλο;", "success": "Ο υπάλληλος διαγράφηκε επιτυχώς"}, "edit": {"title": "Επεξεργασία υπαλλήλου"}, "toasts": {"updateSuccess": "Ο υπάλληλος ενημερώθηκε επιτυχώς", "updateError": "Αποτυχ<PERSON>α ενημέρωσης υπαλλήλου"}}, "toasts": {"createSuccess": "Η εταιρεία δημιουργήθηκε επιτυχώς", "createError": "Αποτυχία δημιουργίας εταιρείας", "updateSuccess": "Η εταιρεία ενημερώθηκε επιτυχώς", "updateError": "Αποτυχία ενημέρωσης εταιρείας", "deleteSuccess": "Η εταιρεία διαγράφηκε επιτυχώς", "deleteError": "Αποτυχία διαγραφής εταιρείας"}}, "approvals": {"domain": "Εγκρίσεις", "title": "Εγκρίσεις", "subtitle": "Διαχείριση ροών εργασίας έγκρισης", "noApprovals": "Δεν βρέθηκαν εγκρίσεις", "noApprovalsDescription": "Δεν υπάρχουν εγκρίσεις προς εξέταση αυτήν τη στιγμή.", "table": {"id": "ID", "type": "Τύπος", "company": "Εταιρεία", "customer": "Πελάτης", "amount": "Ποσό", "status": "Κατάσταση", "createdAt": "Δημιουργήθηκε"}, "status": {"pending": "Εκκρεμεί", "approved": "Εγκρίθηκε", "rejected": "Απορρίφθηκε", "expired": "Έληξε", "unknown": "Άγνωστο"}, "details": {"header": "Λεπτομέρειες έγκρισης", "summary": "Περίληψη έγκρισης", "company": "Εταιρεία", "customer": "Πελάτης", "order": "Παραγγελία", "amount": "Ποσό", "updatedAt": "Ενημερώθηκε", "reason": "Λόγ<PERSON>", "actions": "Ενέργειες"}, "actions": {"approve": "Έγκριση", "reject": "Απόρριψη", "confirmApprove": "Επιβεβαίωση έγκρισης", "confirmReject": "Επιβεβαίωση απόρριψης", "reasonPlaceholder": "Εισάγε<PERSON><PERSON> λόγο (προαιρετικ<PERSON>)..."}, "filters": {"status": "Φιλτράρισμα κατά κατάσταση"}, "toasts": {"approveSuccess": "Εγκρίθηκε επιτυχώς", "approveError": "Αποτυχ<PERSON>α έγκρισης", "rejectSuccess": "Απορρίφθηκε επιτυχώς", "rejectError": "Αποτυχία απόρριψης"}}, "dateTime": {"years_one": "Έτος", "years_other": "Έτη", "months_one": "Μήνας", "months_other": "Μήνες", "weeks_one": "Εβδομάδα", "weeks_other": "Εβδομάδες", "days_one": "Ημέρα", "days_other": "Ημέρες", "hours_one": "Ώρα", "hours_other": "Ώρες", "minutes_one": "Λεπτό", "minutes_other": "Λεπτά", "seconds_one": "Δευτερ<PERSON><PERSON>επτο", "seconds_other": "Δευτερόλεπτα"}}