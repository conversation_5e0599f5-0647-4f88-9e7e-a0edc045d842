{"$schema": "./$schema.json", "general": {"ascending": "По возрастанию", "descending": "По убыванию", "add": "Добавить", "start": "Начало", "end": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "open": "Открыть", "close": "Закрыть", "apply": "Применить", "range": "Диа<PERSON>азон", "search": "Поиск", "of": "из", "results": "результа<PERSON>ов", "pages": "стран<PERSON><PERSON>", "next": "Далее", "prev": "Назад", "is": "является", "timeline": "Временная шкала", "success": "Успешно", "warning": "Предупреждение", "tip": "Подсказка", "error": "Ошибка", "select": "Выбрать", "selected": "Выбрано", "enabled": "Включено", "disabled": "Отключено", "expired": "Истек срок", "active": "Активный", "revoked": "Отозван", "new": "Новый", "modified": "Изменен", "added": "Добавлено", "removed": "Удалено", "admin": "Администратор", "store": "Мага<PERSON>ин", "details": "Детали", "items_one": "{{count}} товар", "items_other": "{{count}} товаров", "countSelected": "{{count}} выбрано", "countOfTotalSelected": "{{count}} из {{total}} выбрано", "plusCount": "+ {{count}}", "plusCountMore": "+ еще {{count}}", "areYouSure": "Вы уверены?", "noRecordsFound": "Записи не найдены", "typeToConfirm": "Введите {val} для подтверждения:", "noResultsTitle": "Нет результатов", "noResultsMessage": "Попробуйте изменить фильтры или запрос", "noSearchResults": "Нет результатов поиска", "noSearchResultsFor": "Нет результатов для <0>'{{query}}'</0>", "noRecordsTitle": "Нет записей", "noRecordsMessage": "Нет записей для отображения", "unsavedChangesTitle": "Вы уверены, что хотите покинуть эту форму?", "unsavedChangesDescription": "У вас есть несохраненные изменения, которые будут потеряны при выходе.", "includesTaxTooltip": "Цены в этом столбце включают налог.", "excludesTaxTooltip": "Цены в этом столбце указаны без налога.", "noMoreData": "Больше данных нет", "actions": "Действия"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}} кл<PERSON>ч", "numberOfKeys_other": "{{count}} ключей", "drawer": {"header_one": "JSON <0>· {{count}} ключ</0>", "header_other": "JSON <0>· {{count}} ключей</0>", "description": "Просмотр JSON данных для этого объекта."}}, "metadata": {"header": "Метаданные", "numberOfKeys_one": "{{count}} кл<PERSON>ч", "numberOfKeys_other": "{{count}} ключей", "edit": {"header": "Редактировать метаданные", "description": "Редактировать метаданные для этого объекта.", "successToast": "Метаданные успешно обновлены.", "actions": {"insertRowAbove": "Вставить строку выше", "insertRowBelow": "Вставить строку ниже", "deleteRow": "Удалить строку"}, "labels": {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "Значение"}, "complexRow": {"label": "Некоторые строки отключены", "description": "Этот объект содержит сложные метаданные, такие как массивы или объекты, которые нельзя редактировать здесь. Для редактирования отключенных строк используйте API напрямую.", "tooltip": "Эта строка отключена, так как содержит сложные данные."}}}, "validation": {"mustBeInt": "Значение должно быть целым числом.", "mustBePositive": "Значение должно быть положительным числом."}, "actions": {"save": "Сохранить", "saveAsDraft": "Сохранить как черновик", "copy": "Копировать", "copied": "Скопировано", "duplicate": "Дублировать", "publish": "Опубликовать", "create": "Создать", "delete": "Удалить", "remove": "Удалить", "revoke": "Отозвать", "cancel": "Отмена", "forceConfirm": "Принудительное подтверждение", "continueEdit": "Продолжить редактирование", "enable": "Включить", "disable": "Отключить", "undo": "Отменить", "complete": "Завершить", "viewDetails": "Просмотреть детали", "back": "Назад", "close": "Закрыть", "showMore": "Показать больше", "continue": "Продолжить", "continueWithEmail": "Продолжить с Email", "idCopiedToClipboard": "ID скопирован в буфер обмена", "addReason": "Добавить причину", "addNote": "Добавить заметку", "reset": "Сбросить", "confirm": "Подтвердить", "edit": "Редактировать", "addItems": "Добавить элементы", "download": "Скачать", "clear": "Очистить", "clearAll": "Очистить все", "apply": "Применить", "add": "Добавить", "select": "Выбрать", "browse": "Просмотреть", "logout": "Выйти", "hide": "Скрыть", "export": "Экспорт", "import": "Импорт", "cannotUndo": "Это действие нельзя отменить"}, "operators": {"in": "В"}, "app": {"search": {"label": "Поиск", "title": "Поиск", "description": "Поиск по всему магазину, включая заказы, товары, клиентов и другое.", "allAreas": "Все разделы", "navigation": "Навигация", "openResult": "Открыть результат", "showMore": "Показать больше", "placeholder": "Перейти или найти что-либо...", "noResultsTitle": "Результаты не найдены", "noResultsMessage": "По вашему запросу ничего не найдено.", "emptySearchTitle": "Введите для поиска", "emptySearchMessage": "Введите ключевое слово или фразу для поиска.", "loadMore": "Загрузить еще {{count}}", "groups": {"all": "Все разделы", "customer": "Клиенты", "customerGroup": "Группы клиентов", "product": "Товары", "productVariant": "Вариан<PERSON>ы товаров", "inventory": "Склад", "reservation": "Резервации", "category": "Категории", "collection": "Коллекции", "order": "Заказы", "promotion": "Акции", "campaign": "Кампании", "priceList": "Прайс-листы", "user": "Пользователи", "region": "Регионы", "taxRegion": "Налоговые регионы", "returnReason": "Причины возврата", "salesChannel": "Каналы продаж", "productType": "Типы товаров", "productTag": "Теги товаров", "location": "Локации", "shippingProfile": "Профили доставки", "publishableApiKey": "Публичные API-ключи", "secretApiKey": "Секретные API-ключи", "command": "Команды", "navigation": "Навигация"}}, "keyboardShortcuts": {"pageShortcut": "Перейти к", "settingShortcut": "Настройки", "commandShortcut": "Команды", "then": "затем", "navigation": {"goToOrders": "Заказы", "goToProducts": "Товары", "goToCollections": "Коллекции", "goToCategories": "Категории", "goToCustomers": "Клиенты", "goToCustomerGroups": "Группы клиентов", "goToInventory": "Склад", "goToReservations": "Резервации", "goToPriceLists": "Прайс-листы", "goToPromotions": "Акции", "goToCampaigns": "Кампании"}, "settings": {"goToSettings": "Настройки", "goToStore": "Мага<PERSON>ин", "goToUsers": "Пользователи", "goToRegions": "Регионы", "goToTaxRegions": "Налоговые регионы", "goToSalesChannels": "Каналы продаж", "goToProductTypes": "Типы товаров", "goToLocations": "Локации", "goToPublishableApiKeys": "Публичные API-ключи", "goToSecretApiKeys": "Секретные API-ключи", "goToWorkflows": "Рабочие процессы", "goToProfile": "Профиль", "goToReturnReasons": "Причины возврата"}}, "menus": {"user": {"documentation": "Документация", "changelog": "История изменений", "shortcuts": "Горячие клавиши", "profileSettings": "Настройки профиля", "theme": {"label": "Тема", "dark": "Темная", "light": "Светлая", "system": "Системная"}}, "store": {"label": "Мага<PERSON>ин", "storeSettings": "Настройки магазина"}, "actions": {"logout": "Выйти"}}, "nav": {"accessibility": {"title": "Навигация", "description": "Навигационное меню панели управления."}, "common": {"extensions": "Расширения"}, "main": {"store": "Мага<PERSON>ин", "storeSettings": "Настройки магазина"}, "settings": {"header": "Настройки", "general": "Общие", "developer": "Разработчик", "myAccount": "<PERSON>ой аккаунт"}}}, "dataGrid": {"columns": {"view": "Вид", "resetToDefault": "Сбросить по умолчанию", "disabled": "Изменение видимых столбцов отключено."}, "shortcuts": {"label": "Горячие клавиши", "commands": {"undo": "Отменить", "redo": "Повторить", "copy": "Копировать", "paste": "Вставить", "edit": "Редактировать", "delete": "Удалить", "clear": "Очистить", "moveUp": "Переместить вверх", "moveDown": "Переместить вниз", "moveLeft": "Переместить влево", "moveRight": "Переместить вправо", "moveTop": "Переместить в начало", "moveBottom": "Переместить в конец", "selectDown": "Выбрать вниз", "selectUp": "Выбрать вверх", "selectColumnDown": "Выбрать столбец вниз", "selectColumnUp": "Выбрать столбец вверх", "focusToolbar": "Перейти к панели инструментов", "focusCancel": "Отменить фокус"}}, "errors": {"fixError": "Исправить ошибку", "count_one": "{{count}} ошибка", "count_other": "{{count}} ошибок"}}, "filters": {"date": {"today": "Сегодня", "lastSevenDays": "Последние 7 дней", "lastThirtyDays": "Последние 30 дней", "lastNinetyDays": "Последние 90 дней", "lastTwelveMonths": "Последние 12 месяцев", "custom": "Произвольный", "from": "От", "to": "До", "starting": "Начиная с", "ending": "Заканчивая"}, "compare": {"lessThan": "Меньше чем", "greaterThan": "Больше чем", "exact": "Точно", "range": "Диа<PERSON>азон", "lessThanLabel": "меньше чем {{value}}", "greaterThanLabel": "больше чем {{value}}", "andLabel": "и"}, "radio": {"yes": "Да", "no": "Нет", "true": "Истина", "false": "Ложь"}, "addFilter": "Добавить фильтр", "sortLabel": "Сортировка", "filterLabel": "Фильтры", "searchLabel": "Поиск", "sorting": {"alphabeticallyAsc": "От А до Я", "alphabeticallyDesc": "От Я до А", "dateAsc": "Сначала новые", "dateDesc": "Сначала старые"}}, "errorBoundary": {"badRequestTitle": "400 - Некорректный запрос", "badRequestMessage": "Сервер не смог обработать запрос из-за неверного синтаксиса.", "notFoundTitle": "404 - Страница не найдена", "notFoundMessage": "Проверьте URL и попробуйте снова, или воспользуйтесь поиском, чтобы найти нужную страницу.", "internalServerErrorTitle": "500 - Внутренняя ошибка сервера", "internalServerErrorMessage": "На сервере произошла непредвиденная ошибка. Пожалуйста, повторите попытку позже.", "defaultTitle": "Произошла ошибка", "defaultMessage": "При отображении этой страницы произошла непредвиденная ошибка.", "noMatchMessage": "Страница, которую вы ищете, не существует.", "backToDashboard": "Вернуться на главную"}, "addresses": {"title": "Адреса", "shippingAddress": {"header": "Адрес доставки", "editHeader": "Редактировать адрес доставки", "editLabel": "Адрес доставки", "label": "Адрес доставки"}, "billingAddress": {"header": "Платежный адрес", "editHeader": "Редактировать платежный адрес", "editLabel": "Платежный адрес", "label": "Платежный адрес", "sameAsShipping": "Совпадает с адресом доставки"}, "contactHeading": "Кон<PERSON><PERSON><PERSON>т", "locationHeading": "Местоположение"}, "email": {"editHeader": "Редактировать email", "editLabel": "Email", "label": "Email"}, "transferOwnership": {"header": "Передать право собственности", "label": "Передать право собственности", "details": {"order": "Детали заказа", "draft": "Детали черновика"}, "currentOwner": {"label": "Текущий владелец", "hint": "Текущий владелец заказа."}, "newOwner": {"label": "Новый владелец", "hint": "Новый владелец, которому будет передан заказ."}, "validation": {"mustBeDifferent": "Новый владелец должен отличаться от текущего владельца.", "required": "Необходимо указать нового владельца."}}, "sales_channels": {"availableIn": "Доступно в <0>{{x}}</0> из <1>{{y}}</1> каналов продаж"}, "products": {"domain": "Товары", "list": {"noRecordsMessage": "Создайте свой первый товар, чтобы начать продажи."}, "edit": {"header": "Редактировать товар", "description": "Редактировать детали товара.", "successToast": "Товар {{title}} был успешно обновлен."}, "create": {"title": "Создать товар", "description": "Создать новый товар.", "header": "Общие", "tabs": {"details": "Детали", "organize": "Организация", "variants": "Варианты", "inventory": "Комплекты запасов"}, "errors": {"variants": "Выберите хотя бы один вариант.", "options": "Создайте хотя бы одну опцию.", "uniqueSku": "SKU должен быть уникальным."}, "inventory": {"heading": "Комплекты запасов", "label": "Добавить складские позиции в комплект варианта.", "itemPlaceholder": "Выберите складскую позицию", "quantityPlaceholder": "Сколько этих элементов нужно для комплекта?"}, "variants": {"header": "Варианты", "subHeadingTitle": "Да, это товар с вариантами", "subHeadingDescription": "Если не отмечено, мы создадим для вас вариант по умолчанию", "optionTitle": {"placeholder": "Размер"}, "optionValues": {"placeholder": "Маленький, Средний, Большой"}, "productVariants": {"label": "Варианты товара", "hint": "Этот порядок повлияет на то, как варианты будут отображаться в вашем магазине.", "alert": "Добавьте опции, чтобы создать варианты.", "tip": "Варианты, которые не выбраны, не будут созданы. Вы всегда можете добавить и отредактировать варианты позже."}, "productOptions": {"label": "Опции товара", "hint": "Укажите опции для товара, например, цвет, размер и т.д."}}, "successToast": "Товар {{title}} был успешно создан."}, "export": {"header": "Экспорт списка товаров", "description": "Экспорт списка товаров в CSV файл.", "success": {"title": "Обрабатываем ваш экспорт", "description": "Экспорт данных может занять несколько минут. Мы уведомим вас, когда он будет готов."}, "filters": {"title": "Фильтры", "description": "Примените фильтры в предварительном просмотре таблицы, чтобы настроить этот вид"}, "columns": {"title": "Столбцы", "description": "Настройте экспортируемые данные под конкретные нужды"}}, "import": {"header": "Импорт списка товаров", "uploadLabel": "Импорт товаров", "uploadHint": "Перетащите CSV файл или нажмите для загрузки", "description": "Импортируйте товары, предоставив CSV файл в установленном формате", "template": {"title": "Не уверены, как составить список?", "description": "Скачайте шаблон ниже, чтобы убедиться, что вы следуете правильному формату."}, "upload": {"title": "Загрузить CSV файл", "description": "С помощью импорта вы можете добавлять или обновлять товары. Для обновления существующих товаров необходимо использовать существующий идентификатор и handle. Перед импортом вам будет предложено подтверждение.", "preprocessing": "Предварительная обработка...", "productsToCreate": "Товары будут созданы", "productsToUpdate": "Товары будут обновлены"}, "success": {"title": "Обрабатываем ваш импорт", "description": "Импорт данных может занять некоторое время. Мы уведомим вас, когда он будет готов."}}, "deleteWarning": "Вы собираетесь удалить товар {{title}}. Это действие нельзя отменить.", "variants": {"header": "Варианты", "empty": {"heading": "Нет вариа<PERSON><PERSON>ов", "description": "Нет вариантов для отображения."}, "filtered": {"heading": "Нет вариа<PERSON><PERSON>ов", "description": "Нет вариа<PERSON>тов, соответствующих текущим критериям фильтров."}}, "attributes": "Атрибуты", "editAttributes": "Редактировать атрибуты", "editOptions": "Редактировать опции", "editPrices": "Редактировать цены", "media": {"label": "Медиа", "editHint": "Добавьте медиа к товару, чтобы представить его в вашем магазине.", "makeThumbnail": "Сделать миниатюрой", "uploadImagesLabel": "Загрузить изображения", "uploadImagesHint": "Перетащите изображения сюда или нажмите для загрузки.", "invalidFileType": "'{{name}}' не является поддерживаемым типом файла. Поддерживаемые типы файлов: {{types}}.", "failedToUpload": "Не удалось загрузить добавленные медиа. Пожалуйста, попробуйте снова.", "deleteWarning_one": "Вы собираетесь удалить {{count}} изображение. Это действие нельзя отменить.", "deleteWarning_other": "Вы собираетесь удалить {{count}} изображений. Это действие нельзя отменить.", "deleteWarningWithThumbnail_one": "Вы собираетесь удалить {{count}} изображение, включая миниатюру. Это действие нельзя отменить.", "deleteWarningWithThumbnail_other": "Вы собираетесь удалить {{count}} изображений, включая миниатюру. Это действие нельзя отменить.", "thumbnailTooltip": "Миниатюра", "galleryLabel": "Галерея", "downloadImageLabel": "Скачать текущее изображение", "deleteImageLabel": "Удалить текущее изображение", "emptyState": {"header": "Нет медиа", "description": "Добавьте медиа к товару, чтобы представить его в вашем магазине.", "action": "Добавить медиа"}, "successToast": "Медиа были успешно обновлены."}, "discountableHint": "Если не отмечено, скидки не будут применяться к этому товару.", "noSalesChannels": "Недоступен ни в одном канале продаж", "variantCount_one": "{{count}} вариант", "variantCount_other": "{{count}} вар<PERSON><PERSON><PERSON><PERSON><PERSON>", "deleteVariantWarning": "Вы собираетесь удалить вариант {{title}}. Это действие нельзя отменить.", "productStatus": {"draft": "Черновик", "published": "Опубликован", "proposed": "Предложен", "rejected": "Отклонен"}, "fields": {"title": {"label": "Название", "hint": "Дайте вашему товару короткое и понятное название.<0/>Рекомендуемая длина для поисковых систем - 50-60 символов.", "placeholder": "Зимняя куртка"}, "subtitle": {"label": "Подзаголовок", "placeholder": "Тепло и уютно"}, "handle": {"label": "<PERSON><PERSON>", "tooltip": "Handle используется для ссылки на товар в вашем магазине. Если не указан, handle будет сгенерирован из названия товара.", "placeholder": "зимняя-куртка"}, "description": {"label": "Описание", "hint": "Дайте вашему товару короткое и понятное описание.<0/>Рекомендуемая длина для поисковых систем - 120-160 символов.", "placeholder": "Теплая и уютная куртка"}, "discountable": {"label": "Скидки", "hint": "Если не отмечено, скидки не будут применяться к этому товару"}, "type": {"label": "Тип"}, "collection": {"label": "Коллекция"}, "categories": {"label": "Категории"}, "tags": {"label": "Теги"}, "sales_channels": {"label": "Каналы продаж", "hint": "Товар будет доступен только в канале продаж по умолчанию, если оставить без изменений."}, "countryOrigin": {"label": "Страна происхождения"}, "material": {"label": "Материал"}, "width": {"label": "Ши<PERSON><PERSON><PERSON>"}, "length": {"label": "Длина"}, "height": {"label": "Высота"}, "weight": {"label": "<PERSON>е<PERSON>"}, "options": {"label": "Опции товара", "hint": "Опции используются для определения цвета, размера и т.д. товара", "add": "Добавить опцию", "optionTitle": "Название опции", "optionTitlePlaceholder": "Цвет", "variations": "Варианты (через запятую)", "variantionsPlaceholder": "Красны<PERSON>, Синий, Зеленый"}, "variants": {"label": "Варианты товара", "hint": "Невыбранные варианты не будут созданы. Этот порядок повлияет на то, как варианты будут расположены в вашем интерфейсе."}, "mid_code": {"label": "MID код"}, "hs_code": {"label": "HS код"}}, "variant": {"edit": {"header": "Редактировать вариант", "success": "Вариант товара был успешно отредактирован"}, "create": {"header": "Детали варианта"}, "deleteWarning": "Вы уверены, что хотите удалить этот вариант?", "pricesPagination": "1 - {{current}} из {{total}} цен", "tableItemAvailable": "{{availableCount}} доступно", "tableItem_one": "{{availableCount}} доступен в {{locationCount}} локации", "tableItem_other": "{{availableCount}} доступно в {{locationCount}} локациях", "inventory": {"notManaged": "Не управляется", "manageItems": "Управление складскими позициями", "notManagedDesc": "Запасы не отслеживаются для этого варианта. Включите 'Управление запасами', чтобы отслеживать количество варианта.", "manageKit": "Управление комплектом запасов", "navigateToItem": "Перейти к складской позиции", "actions": {"inventoryItems": "Перейти к складской позиции", "inventoryKit": "Показать складские позиции"}, "inventoryKit": "Комплект запа<PERSON>ов", "inventoryKitHint": "Состоит ли этот вариант из нескольких складских позиций?", "validation": {"itemId": "Пожалуйста, выберите складскую позицию.", "quantity": "Количество обязательно. Пожалуйста, введите положительное число."}, "header": "Запасы и наличие", "editItemDetails": "Редактировать детали позиции", "manageInventoryLabel": "Управление запасами", "manageInventoryHint": "При включении мы будем изменять количество при создании заказов и возвратов.", "allowBackordersLabel": "Разрешить предзаказы", "allowBackordersHint": "При включении клиенты смогут купить вариант даже при отсутствии в наличии.", "toast": {"levelsBatch": "Уровни запасов обновлены.", "update": "Складская позиция успешно обновлена.", "updateLevel": "Уровень запасов успешно обновлен.", "itemsManageSuccess": "Складские позиции успешно обновлены."}}}, "options": {"header": "Опции", "edit": {"header": "Редактировать опцию", "successToast": "Опция {{title}} была успешно обновлена."}, "create": {"header": "Создать опцию", "successToast": "Опция {{title}} была успешно создана."}, "deleteWarning": "Вы собираетесь удалить опцию товара: {{title}}. Это действие нельзя отменить."}, "organization": {"header": "Организация", "edit": {"header": "Редактировать организацию", "toasts": {"success": "Организация {{title}} успешно обновлена."}}}, "stock": {"heading": "Управление уровнями запасов и местоположениями продукта", "description": "Обновите уровни складских запасов для всех вариантов продукта.", "loading": "Подождите, это может занять некоторое время...", "tooltips": {"alreadyManaged": "Этот складской товар уже доступен для редактирования под {{title}}.", "alreadyManagedWithSku": "Этот складской товар уже доступен для редактирования под {{title}} ({{sku}})."}}, "toasts": {"delete": {"success": {"header": "Товар удален", "description": "{{title}} был успешно удален."}, "error": {"header": "Не удалось удалить товар"}}}}, "collections": {"domain": "Коллекции", "subtitle": "Организуйте товары в коллекции.", "createCollection": "Создать коллекцию", "createCollectionHint": "Создайте новую коллекцию для организации ваших товаров.", "createSuccess": "Коллекция успешно создана.", "editCollection": "Редактировать коллекцию", "handleTooltip": "Handle используется для ссылки на коллекцию в вашем магазине. Если не указан, он будет сгенерирован из названия коллекции.", "deleteWarning": "Вы собираетесь удалить коллекцию {{title}}. Это действие нельзя отменить.", "removeSingleProductWarning": "Вы собираетесь удалить товар {{title}} из коллекции. Это действие нельзя отменить.", "removeProductsWarning_one": "Вы собираетесь удалить {{count}} товар из коллекции. Это действие нельзя отменить.", "removeProductsWarning_other": "Вы собираетесь удалить {{count}} товаров из коллекции. Это действие нельзя отменить.", "products": {"list": {"noRecordsMessage": "В этой коллекции нет товаров."}, "add": {"successToast_one": "Товар успешно добавлен в коллекцию.", "successToast_other": "Товары успешно добавлены в коллекцию."}, "remove": {"successToast_one": "Товар успешно удален из коллекции.", "successToast_other": "Товары успешно удалены из коллекции."}}}, "categories": {"domain": "Категории", "subtitle": "Организуйте товары в категории и управляйте их рейтингом и иерархией.", "create": {"header": "Создать категорию", "hint": "Создайте новую категорию для организации ваших товаров.", "tabs": {"details": "Детали", "organize": "Организация рейтинга"}, "successToast": "Категория {{name}} успешно создана."}, "edit": {"header": "Редактировать категорию", "description": "Редактируйте категорию для обновления ее деталей.", "successToast": "Категория успешно обновлена."}, "delete": {"confirmation": "Вы собираетесь удалить категорию {{name}}. Это действие нельзя отменить.", "successToast": "Категория {{name}} успешно удалена."}, "products": {"add": {"disabledTooltip": "Товар уже находится в этой категории.", "successToast_one": "Добавлен {{count}} товар в категорию.", "successToast_other": "Добавлено {{count}} товаров в категорию."}, "remove": {"confirmation_one": "Вы собираетесь удалить {{count}} товар из категории. Это действие нельзя отменить.", "confirmation_other": "Вы собираетесь удалить {{count}} товаров из категории. Это действие нельзя отменить.", "successToast_one": "Удален {{count}} товар из категории.", "successToast_other": "Удалено {{count}} товаров из категории."}, "list": {"noRecordsMessage": "В этой категории нет товаров."}}, "organize": {"header": "Организация", "action": "Редактировать рейтинг"}, "fields": {"visibility": {"label": "Видимость", "internal": "Внутренняя", "public": "Публичная"}, "status": {"label": "Статус", "active": "Активная", "inactive": "Неактивная"}, "path": {"label": "Путь", "tooltip": "Показать полный путь категории."}, "children": {"label": "Подкатегории"}, "new": {"label": "Новая"}}}, "inventory": {"domain": "Склад", "subtitle": "Управляйте складскими позициями", "reserved": "Зарезервировано", "available": "Доступно", "locationLevels": "Локации", "associatedVariants": "Связанные варианты", "manageLocations": "Управление локациями", "deleteWarning": "Вы собираетесь удалить складскую позицию. Это действие нельзя отменить.", "editItemDetails": "Редактировать детали позиции", "create": {"title": "Создать складскую позицию", "details": "Детали", "availability": "Доступность", "locations": "Локации", "attributes": "Атрибуты", "requiresShipping": "Требует доставки", "requiresShippingHint": "Требует ли складская позиция доставки?", "successToast": "Складская позиция успешно создана."}, "reservation": {"header": "Резервация {{itemName}}", "editItemDetails": "Редактировать резервацию", "lineItemId": "ID позиции", "orderID": "ID заказа", "description": "Описание", "location": "Локация", "inStockAtLocation": "В наличии в этой локации", "availableAtLocation": "Доступно в этой локации", "reservedAtLocation": "Зарезервировано в этой локации", "reservedAmount": "Количество для резервации", "create": "Создать резервацию", "itemToReserve": "Позиция для резервации", "quantityPlaceholder": "Сколько нужно зарезервировать?", "descriptionPlaceholder": "Какой тип резервации?", "successToast": "Резервация успешно создана.", "updateSuccessToast": "Резервация успешно обновлена.", "deleteSuccessToast": "Резервация успешно удалена.", "errors": {"noAvaliableQuantity": "В локации нет доступного количества.", "quantityOutOfRange": "Минимальное количество 1, максимальное количество {{max}}"}}, "adjustInventory": {"errors": {"stockedQuantity": "Количество на складе не может быть меньше зарезервированного количества {{quantity}}."}}, "toast": {"updateLocations": "Локации успешно обновлены.", "updateLevel": "Уровень запасов успешно обновлен.", "updateItem": "Складская позиция успешно обновлена."}, "stock": {"title": "Обновить количество запасов", "description": "Обновите количество складских запасов для выбранных складских позиций.", "action": "Редактировать количество запасов", "placeholder": "Не включено", "disablePrompt_one": "Вы собираете<PERSON>ь отключить {{count}} уровень локации. Это действие нельзя отменить.", "disablePrompt_other": "Вы собираете<PERSON>ь отключить {{count}} уровней локации. Это действие нельзя отменить.", "disabledToggleTooltip": "Невозможно отключить: очистите входящее и/или зарезервированное количество перед отключением.", "successToast": "количество запасов успешно обновлено."}}, "giftCards": {"domain": "Подарочные карты", "editGiftCard": "Редактировать подарочную карту", "createGiftCard": "Создать подарочную карту", "createGiftCardHint": "Создайте подарочную карту вручную, которую можно использовать как способ оплаты в вашем магазине.", "selectRegionFirst": "Сначала выберите регион", "deleteGiftCardWarning": "Вы собираетесь удалить подарочную карту {{code}}. Это действие нельзя отменить.", "balanceHigherThanValue": "Баланс не может быть выше исходной суммы.", "balanceLowerThanZero": "Баланс не может быть отрицательным.", "expiryDateHint": "В разных странах действуют разные правила относительно срока действия подарочных карт. Убедитесь, что вы проверили местные правила перед установкой срока действия.", "regionHint": "Изменение региона подарочной карты также изменит ее валюту, что может повлиять на ее денежную стоимость.", "enabledHint": "Укажите, включена или отключена подарочная карта.", "balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentBalance": "Текущий баланс", "initialBalance": "Начальный баланс", "personalMessage": "Личное сообщение", "recipient": "Получатель"}, "customers": {"domain": "Клиенты", "list": {"noRecordsMessage": "Ваши клиенты появятся здесь."}, "create": {"header": "Создать клиента", "hint": "Создайте нового клиента и управляйте его данными.", "successToast": "Клиент {{email}} успешно создан."}, "groups": {"label": "Группы клиентов", "remove": "Вы уверены, что хотите удалить клиента из группы \"{{name}}\"?", "removeMany": "Вы уверены, что хотите удалить клиента из следующих групп: {{groups}}?", "alreadyAddedTooltip": "Клиент уже в этой группе.", "list": {"noRecordsMessage": "Этот клиент не состоит ни в одной группе."}, "add": {"success": "Клиент добавлен в: {{groups}}.", "list": {"noRecordsMessage": "Сначала создайте группу клиентов."}}, "removed": {"success": "Клиент удален из: {{groups}}.", "list": {"noRecordsMessage": "Сначала создайте группу клиентов."}}}, "edit": {"header": "Редактировать клиента", "emailDisabledTooltip": "Email нельзя изменить для зарегистрированных клиентов.", "successToast": "Клиент {{email}} успешно обновлен."}, "delete": {"title": "Удалить клиента", "description": "Вы собираетесь удалить клиента {{email}}. Это действие нельзя отменить.", "successToast": "Клиент {{email}} успешно удален."}, "fields": {"guest": "Гость", "registered": "Зарегистрирован", "groups": "Группы"}, "registered": "Зарегистрирован", "guest": "Гость", "hasAccount": "Имеет аккаунт"}, "customerGroups": {"domain": "Группы клиентов", "subtitle": "Организуйте клиентов в группы. Группы могут иметь разные акции и цены.", "list": {"empty": {"heading": "Нет групп клиентов", "description": "Нет групп клиентов для отображения."}, "filtered": {"heading": "Нет результатов", "description": "Нет групп клиентов, соответствующих текущим критериям фильтра."}}, "create": {"header": "Создать группу клиентов", "hint": "Создайте новую группу клиентов для сегментации ваших клиентов.", "successToast": "Группа клиентов {{name}} успешно создана."}, "edit": {"header": "Редактировать группу клиентов", "successToast": "Группа клиентов {{name}} успешно обновлена."}, "delete": {"title": "Удалить группу клиентов", "description": "Вы собираетесь удалить группу клиентов {{name}}. Это действие нельзя отменить.", "successToast": "Группа клиентов {{name}} успешно удалена."}, "customers": {"alreadyAddedTooltip": "Клиент уже добавлен в группу.", "add": {"successToast_one": "Клиент успешно добавлен в группу.", "successToast_other": "Клиенты успешно добавлены в группу.", "list": {"noRecordsMessage": "Сначала создайте клиента."}}, "remove": {"title_one": "Удалить клиента", "title_other": "Удалить клиентов", "description_one": "Вы собираетесь удалить {{count}} клиента из группы клиентов. Это действие нельзя отменить.", "description_other": "Вы собираетесь удалить {{count}} клиентов из группы клиентов. Это действие нельзя отменить."}, "list": {"noRecordsMessage": "Эта группа не имеет клиентов."}}}, "orders": {"domain": "Заказы", "claim": "Претензия", "exchange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "return": "Возврат", "cancelWarning": "Вы собираетесь отменить заказ {{id}}. Это действие нельзя отменить.", "orderCanceled": "Заказ успешно отменен", "onDateFromSalesChannel": "{{date}} из {{salesChannel}}", "list": {"noRecordsMessage": "Ваши заказы появятся здесь."}, "status": {"not_paid": "Не оплачен", "pending": "В обработке", "completed": "Завер<PERSON>ен", "draft": "Черновик", "archived": "В архиве", "canceled": "Отменен", "requires_action": "Требует действия"}, "summary": {"requestReturn": "Запросить возврат", "allocateItems": "Распределить товары", "editOrder": "Редактировать заказ", "editOrderContinue": "Продолжить редактирование заказа", "inventoryKit": "Состоит из {{count}}x складских товаров", "itemTotal": "Сумма товаров", "shippingTotal": "Сумма доставки", "discountTotal": "Сумма скидок", "taxTotalIncl": "Сумма налога (включено)", "itemSubtotal": "Подытог товаров", "shippingSubtotal": "Подытог доставки", "discountSubtotal": "Подытог скидок", "taxTotal": "Сумма налога"}, "transfer": {"title": "Передать владение", "requestSuccess": "Запрос на передачу заказа: {{email}}.", "currentOwner": "Текущий владелец", "newOwner": "Новый владелец", "currentOwnerDescription": "Клиент, в настоящее время связанный с этим заказом.", "newOwnerDescription": "Клиент, которому вы хотите передать этот заказ."}, "payment": {"title": "Плате<PERSON>и", "isReadyToBeCaptured": "Пла<PERSON><PERSON><PERSON> <0/> готов к получению.", "totalPaidByCustomer": "Общая сумма, оплаченная клиентом", "capture": "Получить платеж", "capture_short": "Получить", "refund": "Возврат", "markAsPaid": "Отметить как оплаченный", "statusLabel": "Статус платежа", "statusTitle": "Статус платежа", "status": {"notPaid": "Не оплачен", "authorized": "Авторизован", "partiallyAuthorized": "Частично авторизован", "awaiting": "<PERSON>ж<PERSON><PERSON><PERSON><PERSON><PERSON>", "captured": "Получен", "partiallyRefunded": "Частично возвращен", "partiallyCaptured": "Частично получен", "refunded": "Возвращен", "canceled": "Отменен", "requiresAction": "Требует действия"}, "capturePayment": "Будет получен платеж на сумму {{amount}}.", "capturePaymentSuccess": "Платеж на сумму {{amount}} успешно получен", "markAsPaidPayment": "Платеж на сумму {{amount}} будет отмечен как оплаченный.", "markAsPaidPaymentSuccess": "Платеж на сумму {{amount}} успешно отмечен как оплаченный", "createRefund": "Создать возврат", "refundPaymentSuccess": "Возврат суммы {{amount}} выполнен успешно", "createRefundWrongQuantity": "Количество должно быть числом от 1 до {{number}}", "refundAmount": "Вернуть {{ amount }}", "paymentLink": "Копировать ссылку на оплату для {{ amount }}", "selectPaymentToRefund": "Выберите платеж для возврата"}, "edits": {"title": "Редактировать заказ", "confirm": "Подтвердить редактирование", "confirmText": "Вы собираетесь подтвердить редактирование заказа. Это действие нельзя отменить.", "cancel": "Отменить редактирование", "currentItems": "Текущие товары", "currentItemsDescription": "Настройте количество товара или удалите.", "addItemsDescription": "Вы можете добавить новые товары в заказ.", "addItems": "Добавить товары", "amountPaid": "Оплаченная сумма", "newTotal": "Новая сумма", "differenceDue": "Разница к оплате", "create": "Редактировать заказ", "currentTotal": "Текущая сумма", "noteHint": "Добавить внутреннюю заметку к редактированию", "cancelSuccessToast": "Редактирование заказа отменено", "createSuccessToast": "Запрос на редактирование заказа создан", "activeChangeError": "На заказе уже есть активное изменение (возврат, претензия, обмен и т.д.). Завершите или отмените изменение перед редактированием заказа.", "panel": {"title": "Создан запрос на редактирование заказа", "titlePending": "Редактирование заказа ожидает"}, "toast": {"canceledSuccessfully": "Редактирование заказа отменено", "confirmedSuccessfully": "Редактирование заказа подтверждено"}, "validation": {"quantityLowerThanFulfillment": "Нельзя установить количество меньше или равное собранному количеству"}}, "edit": {"email": {"title": "Изменить email", "requestSuccess": "Email заказа изменен на {{email}}."}, "shippingAddress": {"title": "Изменить адрес доставки", "requestSuccess": "Адрес доставки заказа обновлен."}, "billingAddress": {"title": "Изменить платежный адрес", "requestSuccess": "Платежный адрес заказа обновлен."}}, "returns": {"create": "Создать возврат", "confirm": "Подтвердить возврат", "confirmText": "Вы собираетесь подтвердить возврат. Это действие нельзя отменить.", "inbound": "Входящий", "outbound": "Исходящий", "sendNotification": "Отправить уведомление", "sendNotificationHint": "Уведомить клиента о возврате.", "returnTotal": "Сумма возврата", "inboundTotal": "Входящая сумма", "refundAmount": "Сумма возврата", "outstandingAmount": "Остаток к оплате", "reason": "Причина", "reasonHint": "Выберите причину, по которой клиент хочет вернуть товары.", "note": "Заметка", "noInventoryLevel": "Нет уровня запасов", "noInventoryLevelDesc": "Выбранное местоположение не имеет уровня запасов для выбранных товаров. Запрос на возврат можно создать, но его нельзя принять, пока не будет создан уровень запасов для выбранного местоположения.", "noteHint": "Вы можете написать любой текст, если хотите что-то уточнить.", "location": "Местоположение", "locationHint": "Выберите местоположение, куда вы хотите вернуть товары.", "inboundShipping": "Обратная доставка", "inboundShippingHint": "Выберите метод, который вы хотите использовать.", "returnableQuantityLabel": "Количество для возврата", "refundableAmountLabel": "Сумма для возврата", "returnRequestedInfo": "Запрошен возврат {{requestedItemsCount}}x товаров", "returnReceivedInfo": "Получено {{requestedItemsCount}}x товаров возврата", "itemReceived": "Товары получены", "returnRequested": "Возврат запрошен", "damagedItemReceived": "Получены поврежденные товары", "damagedItemsReturned": "Возвращено {{quantity}}x поврежденных товаров", "activeChangeError": "На этом заказе есть активное изменение. Завершите или отмените предыдущее изменение.", "cancel": {"title": "Отменить возврат", "description": "Вы уверены, что хотите отменить запрос на возврат?"}, "placeholders": {"noReturnShippingOptions": {"title": "Не найдены варианты обратной доставки", "hint": "Не созданы варианты обратной доставки для этого местоположения. Вы можете создать один в <LinkComponent>Местоположения и доставка</LinkComponent>."}, "outboundShippingOptions": {"title": "Не найдены варианты исходящей доставки", "hint": "Не созданы варианты исходящей доставки для этого местоположения. Вы можете создать один в <LinkComponent>Местоположения и доставка</LinkComponent>."}}, "receive": {"action": "Получить товары", "receiveItems": "{{ returnType }} {{ id }}", "restockAll": "Пополнить все товары", "itemsLabel": "Полученные товары", "title": "Получить товары для #{{returnId}}", "sendNotificationHint": "Уведомить клиента о полученном возврате.", "inventoryWarning": "Помните, что мы автоматически скорректируем уровни запасов на основе указанных выше данных.", "writeOffInputLabel": "Сколько товаров повреждено?", "toast": {"success": "Возврат успешно получен.", "errorLargeValue": "Количество больше, чем количество заказанных товаров.", "errorNegativeValue": "Количество не может быть отрицательным значением.", "errorLargeDamagedValue": "Количество поврежденных товаров + количество полученных неповрежденных товаров превышает общее количество товаров в возврате. Уменьшите количество неповрежденных товаров."}}, "toast": {"canceledSuccessfully": "Возврат успешно отменен", "confirmedSuccessfully": "Возврат успешно подтвержден"}, "panel": {"title": "Инициирован возврат", "description": "Существует открытый запрос на возврат для обработки"}}, "claims": {"create": "Создать претензию", "confirm": "Подтвердить претензию", "confirmText": "Вы собираетесь подтвердить претензию. Это действие нельзя отменить.", "manage": "Управлять претензией", "outbound": "Исходящий", "outboundItemAdded": "{{itemsCount}}x добавлено через претензию", "outboundTotal": "Исходящая сумма", "outboundShipping": "Исходящая доставка", "outboundShippingHint": "Выберите метод, который вы хотите использовать.", "refundAmount": "Расчетная разница", "activeChangeError": "На этом заказе есть активное изменение. Завершите или отмените предыдущее изменение.", "actions": {"cancelClaim": {"successToast": "Претензия успешно отменена."}}, "cancel": {"title": "Отменить претензию", "description": "Вы уверены, что хотите отменить претензию?"}, "tooltips": {"onlyReturnShippingOptions": "Этот список будет содержать только варианты обратной доставки."}, "toast": {"canceledSuccessfully": "Претензия успешно отменена", "confirmedSuccessfully": "Претензия успешно подтверждена"}, "panel": {"title": "Инициирована претензия", "description": "Существует открытый запрос на претензию для обработки"}}, "exchanges": {"create": "Создать обмен", "manage": "Управлять обменом", "confirm": "Подтвердить обмен", "confirmText": "Вы собираетесь подтвердить обмен. Это действие нельзя отменить.", "outbound": "Исходящий", "outboundItemAdded": "{{itemsCount}}x добавлено через обмен", "outboundTotal": "Исходящая сумма", "outboundShipping": "Исходящая доставка", "outboundShippingHint": "Выберите метод, который вы хотите использовать.", "refundAmount": "Расчетная разница", "activeChangeError": "На этом заказе есть активное изменение. Завершите или отмените предыдущее изменение.", "actions": {"cancelExchange": {"successToast": "Обмен успешно отменен."}}, "cancel": {"title": "Отменить обмен", "description": "Вы уверены, что хотите отменить обмен?"}, "tooltips": {"onlyReturnShippingOptions": "Этот список будет содержать только варианты обратной доставки."}, "toast": {"canceledSuccessfully": "Обмен успешно отменен", "confirmedSuccessfully": "Обмен успешно подтвержден"}, "panel": {"title": "Инициир<PERSON>ан обмен", "description": "Существует открытый запрос на обмен для обработки"}}, "reservations": {"allocatedLabel": "Распределено", "notAllocatedLabel": "Не распределено"}, "allocateItems": {"action": "Распределить товары", "title": "Распределить товары заказа", "locationDescription": "Выберите местоположение, из которого вы хотите распределить.", "itemsToAllocate": "Товары для распределения", "itemsToAllocateDesc": "Выберите количество товаров, которые вы хотите распределить", "search": "Поиск товаров", "consistsOf": "Состоит из {{num}}x складских товаров", "requires": "Требуется {{num}} на вариант", "toast": {"created": "Товары успешно распределены"}, "error": {"quantityNotAllocated": "Есть нераспределенные товары."}}, "shipment": {"title": "Отметить фулфилмент как отправленный", "trackingNumber": "Номер отслеживания", "addTracking": "Добавить номер отслеживания", "sendNotification": "Отправить уведомление", "sendNotificationHint": "Уведомить клиента об этой отправке.", "toastCreated": "Отправка успешно создана."}, "fulfillment": {"cancelWarning": "Вы собираетесь отменить фулфилмент. Это действие нельзя отменить.", "markAsDeliveredWarning": "Вы собираетесь отметить фулфилмент как доставленный. Это действие нельзя отменить.", "unfulfilledItems": "Несобранные товары", "statusLabel": "Статус фулфилмента", "statusTitle": "Статус фулфилмента", "fulfillItems": "Собрать товары", "awaitingFulfillmentBadge": "Ожида<PERSON>т фулфилмента", "requiresShipping": "Требует доставки", "number": "Фулфилмент #{{number}}", "itemsToFulfill": "Товары для фулфилмента", "create": "Создать фулфилмент", "available": "Доступно", "inStock": "В наличии", "markAsShipped": "Отметить как отправленный", "markAsDelivered": "Отметить как доставленный", "itemsToFulfillDesc": "Выберите товары и количества для фулфилмента", "locationDescription": "Выберите местоположение, из которого вы хотите собрать товары.", "sendNotificationHint": "Уведомить клиентов о созданном фулфилменте.", "methodDescription": "Выберите другой метод доставки, чем выбранный клиентом", "error": {"wrongQuantity": "Только один товар доступен для фулфилмента", "wrongQuantity_other": "Количество должно быть числом от 1 до {{number}}", "noItems": "Нет товаров для фулфилмента."}, "status": {"notFulfilled": "Не собрано", "partiallyFulfilled": "Частично собрано", "fulfilled": "Собрано", "partiallyShipped": "Частично отправлено", "shipped": "Отправлено", "delivered": "Доставлено", "partiallyDelivered": "Частично доставлено", "partiallyReturned": "Частично возвращено", "returned": "Возвращено", "canceled": "Отменено", "requiresAction": "Требует действия"}, "toast": {"created": "Фулфилмент успешно создан", "canceled": "Фулфилмент успешно отменен", "fulfillmentShipped": "Нельзя отменить уже отправленный фулфилмент", "fulfillmentDelivered": "Фулфилмент успешно отмечен как доставленный"}, "trackingLabel": "Отслеживание", "shippingFromLabel": "Отправка из", "itemsLabel": "Товары"}, "refund": {"title": "Создать возврат", "sendNotificationHint": "Уведомить клиентов о созданном возврате.", "systemPayment": "Системный платеж", "systemPaymentDesc": "Один или несколько ваших платежей являются системными платежами. Помните, что выполнения и возвраты не поддерживаются Medusa для таких платежей.", "error": {"amountToLarge": "Нельзя вернуть больше, чем первоначальную сумму заказа.", "amountNegative": "Сумма возврата должна быть положительным числом.", "reasonRequired": "Выберите причину возврата."}}, "customer": {"contactLabel": "Кон<PERSON><PERSON><PERSON>т", "editEmail": "Редактировать email", "transferOwnership": "Передать владение", "editBillingAddress": "Редактировать адрес для выставления счета", "editShippingAddress": "Редактировать адрес доставки"}, "activity": {"header": "Активность", "showMoreActivities_one": "Показать еще {{count}} активность", "showMoreActivities_other": "Показать еще {{count}} активностей", "comment": {"label": "Комментарий", "placeholder": "Добавить комментарий", "addButtonText": "Добавить комментарий", "deleteButtonText": "Удалить комментарий"}, "from": "От", "to": "До", "events": {"common": {"toReturn": "Для возврата", "toSend": "Для отправки"}, "placed": {"title": "Заказ размещен", "fromSalesChannel": "из {{salesChannel}}"}, "canceled": {"title": "Заказ отменен"}, "payment": {"awaiting": "Ожида<PERSON>т оплаты", "captured": "Платеж получен", "canceled": "Платеж отменен", "refunded": "Платеж возвращен"}, "fulfillment": {"created": "Товары собраны", "canceled": "Сборка отменена", "shipped": "Товары отправлены", "delivered": "Товары доставлены", "items_one": "{{count}} товар", "items_other": "{{count}} товаров"}, "return": {"created": "Возврат #{{returnId}} запрошен", "canceled": "Возврат #{{returnId}} отменен", "received": "Возврат #{{returnId}} получен", "items_one": "{{count}} товар возвращен", "items_other": "{{count}} товаров возвращено"}, "note": {"comment": "Комментарий", "byLine": "от {{author}}"}, "claim": {"created": "Претензия #{{claimId}} создана", "canceled": "Претензия #{{claimId}} отменена", "itemsInbound": "{{count}} товар для возврата", "itemsOutbound": "{{count}} товар для отправки"}, "exchange": {"created": "Обмен #{{exchangeId}} создан", "canceled": "Обмен #{{exchangeId}} отменен", "itemsInbound": "{{count}} товар для возврата", "itemsOutbound": "{{count}} товар для отправки"}, "edit": {"requested": "Редактирование заказа #{{editId}} запрошено", "confirmed": "Редактирование заказа #{{editId}} подтверждено"}, "transfer": {"requested": "Передача заказа #{{transferId}} запрошена", "confirmed": "Передача заказа #{{transferId}} подтверждена", "declined": "Передача отклонена"}, "update_order": {"shipping_address": "Адрес доставки обновлен", "billing_address": "Адрес для выставления счета обновлен", "email": "Email обновлен"}}}, "fields": {"displayId": "ID для отображения", "refundableAmount": "Сумма для возврата", "returnableQuantity": "Количество для возврата"}}, "draftOrders": {"domain": "Черновики заказов", "deleteWarning": "Вы собираетесь удалить черновик заказа {{id}}. Это действие нельзя отменить.", "paymentLinkLabel": "Ссылка на оплату", "cartIdLabel": "ID корзины", "markAsPaid": {"label": "Отметить как оплаченный", "warningTitle": "Отметить как оплаченный", "warningDescription": "Вы собираетесь отметить черновик заказа как оплаченный. Это действие нельзя отменить, и последующее взимание оплаты будет невозможно."}, "status": {"open": "Открыт", "completed": "Завер<PERSON>ен"}, "create": {"createDraftOrder": "Создать черновик заказа", "createDraftOrderHint": "Создайте новый черновик заказа для управления деталями заказа перед его размещением.", "chooseRegionHint": "Выберите регион", "existingItemsLabel": "Существующие товары", "existingItemsHint": "Добавьте существующие товары в черновик заказа.", "customItemsLabel": "Пользовательские товары", "customItemsHint": "Добавьте пользовательские товары в черновик заказа.", "addExistingItemsAction": "Добавить существующие товары", "addCustomItemAction": "Добавить пользовательский товар", "noCustomItemsAddedLabel": "Пользовательские товары еще не добавлены", "noExistingItemsAddedLabel": "Существующие товары еще не добавлены", "chooseRegionTooltip": "Сначала выберите регион", "useExistingCustomerLabel": "Использовать существующего клиента", "addShippingMethodsAction": "Добавить способы доставки", "unitPriceOverrideLabel": "Переопределить цену за единицу", "shippingOptionLabel": "Вариант доставки", "shippingOptionHint": "Выберите вариант доставки для черновика заказа.", "shippingPriceOverrideLabel": "Переопределить стоимость доставки", "shippingPriceOverrideHint": "Переопределить стоимость доставки для черновика заказа.", "sendNotificationLabel": "Отправить уведомление", "sendNotificationHint": "Отправить уведомление клиенту после создания черновика заказа."}, "validation": {"requiredEmailOrCustomer": "Требуется email или клиент.", "requiredItems": "Требуется как минимум один товар.", "invalidEmail": "Email должен быть действительным адресом электронной почты."}}, "stockLocations": {"domain": "Локации и доставка", "list": {"description": "Управляйте складскими локациями и вариантами доставки вашего магазина."}, "create": {"header": "Создать складскую локацию", "hint": "Складская локация - это физическое место, где хранятся и отгружаются товары.", "successToast": "Локация {{name}} успешно создана."}, "edit": {"header": "Редактировать складскую локацию", "viewInventory": "Просмотр з<PERSON><PERSON><PERSON><PERSON><PERSON>", "successToast": "Локация {{name}} успешно обновлена."}, "delete": {"confirmation": "Вы собираетесь удалить складскую локацию {{name}}. Это действие нельзя отменить."}, "fulfillmentProviders": {"header": "Поставщики фулфилмента", "shippingOptionsTooltip": "Этот выпадающий список будет содержать только поставщиков, включенных для этой локации. Добавьте их в локацию, если выпадающий список отключен.", "label": "Подключенные поставщики фулфилмента", "connectedTo": "Подключено {{count}} из {{total}} поставщиков фулфилмента", "noProviders": "Эта складская локация не подключена ни к одному поставщику фулфилмента.", "action": "Подключить поставщиков", "successToast": "Поставщики фулфилмента для складской локации успешно обновлены."}, "fulfillmentSets": {"pickup": {"header": "Самовывоз"}, "shipping": {"header": "Доставка"}, "disable": {"confirmation": "Вы уверены, что хотите отключить {{name}}? Это удалит все связанные сервисные зоны и варианты доставки, и это действие нельзя отменить.", "pickup": "Самовывоз успешно отключен.", "shipping": "Доставка успешно отключена."}, "enable": {"pickup": "Самовывоз успешно включен.", "shipping": "Доставка успешно включена."}}, "sidebar": {"header": "Настройки доставки", "shippingProfiles": {"label": "Профили доставки", "description": "Группируйте товары по требованиям к доставке"}}, "salesChannels": {"header": "Каналы продаж", "label": "Подключенные каналы продаж", "connectedTo": "Подключено {{count}} из {{total}} каналов продаж", "noChannels": "Локация не подключена ни к одному каналу продаж.", "action": "Подключить каналы продаж", "successToast": "Каналы продаж успешно обновлены."}, "shippingOptions": {"create": {"shipping": {"header": "Создать вариант доставки для {{zone}}", "hint": "Создайте новый вариант доставки, чтобы определить, как товары отправляются из этой локации.", "label": "Варианты доставки", "successToast": "Вариант доставки {{name}} успешно создан."}, "returns": {"header": "Создать вариант возврата для {{zone}}", "hint": "Создайте новый вариант возврата, чтобы определить, как товары возвращаются в эту локацию.", "label": "Варианты возврата", "successToast": "Вариант возврата {{name}} успешно создан."}, "tabs": {"details": "Детали", "prices": "Цены"}, "action": "Создать вариант"}, "delete": {"confirmation": "Вы собираетесь удалить вариант доставки {{name}}. Это действие нельзя отменить.", "successToast": "Вариант доставки {{name}} успешно удален."}, "edit": {"header": "Редактировать вариант доставки", "action": "Редактировать вариант", "successToast": "Вариант доставки {{name}} успешно обновлен."}, "pricing": {"action": "Редактировать цены"}, "fields": {"count": {"shipping_one": "{{count}} вариант доставки", "shipping_other": "{{count}} варианта доставки", "returns_one": "{{count}} вариант возврата", "returns_other": "{{count}} варианта возврата"}, "priceType": {"label": "Тип цены", "options": {"fixed": {"label": "Фиксированная", "hint": "Цена варианта доставки фиксирована и не меняется в зависимости от содержимого заказа."}, "calculated": {"label": "Рассчитанная", "hint": "Цена варианта доставки рассчитывается поставщиком фулфилмента во время оформления заказа."}}}, "enableInStore": {"label": "Включить в магазине", "hint": "Могут ли клиенты использовать этот вариант при оформлении заказа."}, "provider": "Поставщик фулфилмента", "profile": "Профиль доставки", "fulfillmentOption": "Вариант фулфилмента"}, "conditionalPrices": {"header": "Условные цены для {{name}}", "description": "Управляйте условными ценами для этого варианта доставки на основе общей стоимости товаров в корзине.", "attributes": {"cartItemTotal": "Общая стоимость товаров в корзине"}, "summaries": {"range": "Если <0>{{attribute}}</0> между <1>{{gte}}</1> и <2>{{lte}}</2>", "greaterThan": "Если <0>{{attribute}}</0> ≥ <1>{{gte}}</1>", "lessThan": "Если <0>{{attribute}}</0> ≤ <1>{{lte}}</1>"}, "actions": {"addPrice": "Добавить цену", "manageConditionalPrices": "Управлять условными ценами"}, "rules": {"amount": "Цена варианта доставки", "gte": "Минимальная общая стоимость товаров", "lte": "Максимальная общая стоимость товаров"}, "customRules": {"label": "Пользовательские правила", "tooltip": "Эта условная цена имеет правила, которыми нельзя управлять в панели управления.", "eq": "Общая стоимость товаров должна быть равна", "gt": "Общая стоимость товаров должна быть больше", "lt": "Общая стоимость товаров должна быть меньше"}, "errors": {"amountRequired": "Цена варианта доставки обязательна", "minOrMaxRequired": "Необходимо указать минимальную или максимальную общую стоимость товаров", "minGreaterThanMax": "Минимальная общая стоимость товаров должна быть меньше или равна максимальной", "duplicateAmount": "Цена варианта доставки должна быть уникальной для каждого условия", "overlappingConditions": "Условия должны быть уникальными для всех правил цен"}}}, "serviceZones": {"create": {"headerPickup": "Создать сервисную зону для самовывоза из {{location}}", "headerShipping": "Создать сервисную зону для доставки из {{location}}", "action": "Создать сервисную зону", "successToast": "Сервисная зона {{name}} успешно создана."}, "edit": {"header": "Редактировать сервисную зону", "successToast": "Сервисная зона {{name}} успешно обновлена."}, "delete": {"confirmation": "Вы собираетесь удалить сервисную зону {{name}}. Это действие нельзя отменить.", "successToast": "Сервисная зона {{name}} успешно удалена."}, "manageAreas": {"header": "Управление областями для {{name}}", "action": "Управление областями", "label": "Области", "hint": "Выберите географические области, которые охватывает сервисная зона.", "successToast": "Области для {{name}} успешно обновлены."}, "fields": {"noRecords": "Нет сервисных зон, к которым можно добавить варианты доставки.", "tip": "Сервисная зона - это набор географических зон или областей. Она используется для ограничения доступных вариантов доставки определенным набором локаций."}}}, "shippingProfile": {"domain": "Профили доставки", "subtitle": "Группируйте товары со схожими требованиями к доставке в профили.", "create": {"header": "Создать профиль доставки", "hint": "Создайте новый профиль доставки для группировки товаров со схожими требованиями к доставке.", "successToast": "Профиль доставки {{name}} успешно создан."}, "delete": {"title": "Удалить профиль доставки", "description": "Вы собираетесь удалить профиль доставки {{name}}. Это действие нельзя отменить.", "successToast": "Профиль доставки {{name}} успешно удален."}, "tooltip": {"type": "Введите тип профиля доставки, например: Тяж<PERSON><PERSON><PERSON><PERSON>, Крупногабаритный, Только фрахт и т.д."}}, "taxRegions": {"domain": "Налоговые регионы", "list": {"hint": "Управляйте налогами, которыми облагаются клиенты при покупках в разных странах и регионах."}, "delete": {"confirmation": "Вы собираетесь удалить налоговый регион. Это действие нельзя отменить.", "successToast": "Налоговый регион успешно удален."}, "create": {"header": "Создать налоговый регион", "hint": "Создайте новый налоговый регион для определения налоговых ставок для конкретной страны.", "errors": {"rateIsRequired": "Налоговая ставка обязательна при создании налоговой ставки по умолчанию.", "nameIsRequired": "Название обязательно при создании налоговой ставки по умолчанию."}, "successToast": "Налоговый регион успешно создан."}, "province": {"header": "Провинции", "create": {"header": "Создать региональный налоговый регион", "hint": "Создайте новый налоговый регион для определения налоговых ставок для конкретной провинции."}}, "state": {"header": "Шта<PERSON>ы", "create": {"header": "Создать налоговый регион штата", "hint": "Создайте новый налоговый регион для определения налоговых ставок для конкретного штата."}}, "stateOrTerritory": {"header": "Штаты или территории", "create": {"header": "Создать налоговый регион штата/территории", "hint": "Создайте новый налоговый регион для определения налоговых ставок для конкретного штата/территории."}}, "county": {"header": "Округа", "create": {"header": "Создать окружной налоговый регион", "hint": "Создайте новый налоговый регион для определения налоговых ставок для конкретного округа."}}, "region": {"header": "Регионы", "create": {"header": "Создать региональный налоговый регион", "hint": "Создайте новый налоговый регион для определения налоговых ставок для конкретного региона."}}, "department": {"header": "Департаменты", "create": {"header": "Создать департаментский налоговый регион", "hint": "Создайте новый налоговый регион для определения налоговых ставок для конкретного департамента."}}, "territory": {"header": "Территории", "create": {"header": "Создать территориальный налоговый регион", "hint": "Создайте новый налоговый регион для определения налоговых ставок для конкретной территории."}}, "prefecture": {"header": "Префектуры", "create": {"header": "Создать префектурный налоговый регион", "hint": "Создайте новый налоговый регион для определения налоговых ставок для конкретной префектуры."}}, "district": {"header": "Районы", "create": {"header": "Создать районный налоговый регион", "hint": "Создайте новый налоговый регион для определения налоговых ставок для конкретного района."}}, "governorate": {"header": "Гу<PERSON><PERSON>рнаторства", "create": {"header": "Создать губернаторский налоговый регион", "hint": "Создайте новый налоговый регион для определения налоговых ставок для конкретного губернаторства."}}, "canton": {"header": "Кантоны", "create": {"header": "Создать кантональный налоговый регион", "hint": "Создайте новый налоговый регион для определения налоговых ставок для конкретного кантона."}}, "emirate": {"header": "Э<PERSON><PERSON><PERSON><PERSON><PERSON>ы", "create": {"header": "Создать налоговый регион эмирата", "hint": "Создайте новый налоговый регион для определения налоговых ставок для конкретного эмирата."}}, "sublevel": {"header": "Подуровни", "create": {"header": "Создать налоговый регион подуровня", "hint": "Создайте новый налоговый регион для определения налоговых ставок для конкретного подуровня."}}, "taxOverrides": {"header": "Переопределения", "create": {"header": "Создать переопределение", "hint": "Создайте налоговую ставку, которая переопределяет налоговые ставки по умолчанию для выбранных условий."}, "edit": {"header": "Редактировать переопределение", "hint": "Редактируйте налоговую ставку, которая переопределяет налоговые ставки по умолчанию для выбранных условий."}}, "taxRates": {"create": {"header": "Создать налоговую ставку", "hint": "Создайте новую налоговую ставку для определения налога для региона.", "successToast": "Налоговая ставка успешно создана."}, "edit": {"header": "Редактировать налоговую ставку", "hint": "Редактируйте налоговую ставку для определения налога для региона.", "successToast": "Налоговая ставка успешно обновлена."}, "delete": {"confirmation": "Вы собираетесь удалить налоговую ставку {{name}}. Это действие нельзя отменить.", "successToast": "Налоговая ставка успешно удалена."}}, "fields": {"isCombinable": {"label": "Комбинируемость", "hint": "Можно ли комбинировать эту налоговую ставку с налоговой ставкой по умолчанию из налогового региона.", "true": "Комбинируемая", "false": "Некомбинируемая"}, "defaultTaxRate": {"label": "Налоговая ставка по умолчанию", "tooltip": "Налоговая ставка по умолчанию для этого региона. Например, стандартная ставка НДС для страны или региона.", "action": "Создать налоговую ставку по умолчанию"}, "taxRate": "Налоговая ставка", "taxCode": "Налоговый код", "targets": {"label": "Цели", "hint": "Выберите цели, к которым будет применяться эта налоговая ставка.", "options": {"product": "Товары", "productCollection": "Коллекции товаров", "productTag": "Теги товаров", "productType": "Типы товаров", "customerGroup": "Группы клиентов"}, "operators": {"in": "В", "on": "НА", "and": "И"}, "placeholders": {"product": "Поиск товаров", "productCollection": "Поиск коллекций товаров", "productTag": "Поиск тегов товаров", "productType": "Поиск типов товаров", "customerGroup": "Поиск групп клиентов"}, "tags": {"product": "<PERSON><PERSON><PERSON><PERSON>", "productCollection": "Коллекция товаров", "productTag": "Тег товара", "productType": "Тип товара", "customerGroup": "Группа клиентов"}, "modal": {"header": "Добавить цели"}, "values_one": "{{count}} значение", "values_other": "{{count}} значений", "numberOfTargets_one": "{{count}} цель", "numberOfTargets_other": "{{count}} целей", "additionalValues_one": "и еще {{count}} значение", "additionalValues_other": "и еще {{count}} значений", "action": "Добавить цель"}, "sublevels": {"labels": {"province": "Провинция", "state": "<PERSON>т<PERSON><PERSON>", "region": "Регион", "stateOrTerritory": "Штат/территория", "department": "Депар<PERSON><PERSON><PERSON><PERSON><PERSON>т", "county": "Округ", "territory": "Территория", "prefecture": "Префектура", "district": "Рай<PERSON>н", "governorate": "Гу<PERSON><PERSON>рнаторство", "emirate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "canton": "Кантон", "sublevel": "Код подуровня"}, "placeholders": {"province": "Выберите провинцию", "state": "Выберите штат", "region": "Выберите регион", "stateOrTerritory": "Выберите штат/территорию", "department": "Выберите департамент", "county": "Выберите округ", "territory": "Выберите территорию", "prefecture": "Выберите префектуру", "district": "Выберите район", "governorate": "Выберите губернаторство", "emirate": "Выберите эмират", "canton": "Выберите кантон"}, "tooltips": {"sublevel": "Введите код ISO 3166-2 для налогового подрегиона.", "notPartOfCountry": "{{province}} не является частью {{country}}. Пожалуйста, проверьте правильность."}, "alert": {"header": "Подуровни отключены для этого налогового региона", "description": "Подуровни по умолчанию отключены для этого региона. Вы можете включить их для создания подуровневых регионов, таких как провинции, штаты или территории.", "action": "Включить подуровни"}}, "noDefaultRate": {"label": "Нет ставки по умолчанию", "tooltip": "У этого налогового региона нет налоговой ставки по умолчанию. Если существует стандартная ставка, например НДС для страны, добавьте ее в этот регион."}}}, "promotions": {"domain": "Акции", "sections": {"details": "Детали акции"}, "tabs": {"template": "Тип", "details": "Подробности", "campaign": "Кампания"}, "fields": {"type": "Тип", "value_type": "Тип значения", "value": "Значение", "campaign": "Кампания", "method": "Метод", "allocation": "Распределение", "addCondition": "Добавить условие", "clearAll": "Очистить все", "amount": {"tooltip": "Выберите валюту, чтобы установить сумму"}, "conditions": {"rules": {"title": "Кто может использовать этот код?", "description": "Какие клиенты могут использовать промокод? Если не указано, промокод может быть использован всеми клиентами."}, "target-rules": {"title": "К каким товарам будет применяться акция?", "description": "Акция будет применена к товарам, соответствующим следующим условиям."}, "buy-rules": {"title": "Что должно быть в корзине для активации акции?", "description": "Если эти условия совпадают, мы активируем акцию на выбранные товары."}}}, "tooltips": {"campaignType": "Чтобы установить бюджет расходов, в акции должна быть выбрана валюта."}, "errors": {"requiredField": "Обязательное поле", "promotionTabError": "Исправьте ошибки на вкладке Акция, прежде чем продолжить"}, "toasts": {"promotionCreateSuccess": "Акция ({{code}}) успешно создана."}, "create": {}, "edit": {"title": "Редактировать детали акции", "rules": {"title": "Редактировать условия использования"}, "target-rules": {"title": "Редактировать условия для товаров"}, "buy-rules": {"title": "Редактировать правила покупки"}}, "campaign": {"header": "Кампания", "edit": {"header": "Редактировать кампанию", "successToast": "Промо-кампания успешно обновлена."}, "actions": {"goToCampaign": "Перейти к кампании"}}, "campaign_currency": {"tooltip": "Это валюта акции. Измените ее на вкладке Подробности."}, "form": {"required": "Обязательно", "and": "И", "selectAttribute": "Выберите атрибут", "campaign": {"existing": {"title": "Существующая кампания", "description": "Добавить акцию в существующую кампанию.", "placeholder": {"title": "Нет существующих кампаний", "desc": "Вы можете создать ее для отслеживания нескольких акций и установки бюджетных лимитов."}}, "new": {"title": "Новая кампания", "description": "Создать новую кампанию для этой акции."}, "none": {"title": "Без кампании", "description": "Продолжить без привязки акции к кампании"}}, "status": {"label": "Статус", "draft": {"title": "Черновик", "description": "Клиенты пока не смогут использовать этот код"}, "active": {"title": "Активно", "description": "Клиенты смогут использовать этот код"}, "inactive": {"title": "Неактивно", "description": "Клиенты больше не смогут использовать этот код"}}, "method": {"label": "Метод", "code": {"title": "Промокод", "description": "Клиенты должны ввести этот код при оформлении заказа"}, "automatic": {"title": "Автоматически", "description": "Клиенты увидят эту акцию при оформлении заказа"}}, "max_quantity": {"title": "Максимальное количество", "description": "Максимальное количество товаров, к которым применяется эта акция."}, "type": {"standard": {"title": "Стандартная", "description": "Стандартная акция"}, "buyget": {"title": "Купи и получи", "description": "Купи X и получи скидку на Y"}}, "allocation": {"each": {"title": "Каждый", "description": "Применяет значение к каждому товару"}, "across": {"title": "Ко всем", "description": "Применяет значение ко всем товарам"}}, "code": {"title": "<PERSON>од", "description": "Код, который ваши клиенты введут при оформлении заказа."}, "value": {"title": "Значение акции"}, "value_type": {"fixed": {"title": "Значение акции", "description": "Сумма скидки, например 100"}, "percentage": {"title": "Значение акции", "description": "Процент скидки от суммы, например 8%"}}}, "deleteWarning": "Вы собираетесь удалить акцию {{code}}. Это действие нельзя отменить.", "createPromotionTitle": "Создать акцию", "type": "Тип акции", "conditions": {"add": "Добавить условие", "list": {"noRecordsMessage": "Добавьте условие, чтобы ограничить товары, к которым применяется акция."}}}, "campaigns": {"domain": "Кампании", "details": "Детали кампании", "status": {"active": "Активная", "expired": "Истекла", "scheduled": "Запланирована"}, "delete": {"title": "Вы уверены?", "description": "Вы собираетесь удалить кампанию \"{{name}}\". Это действие нельзя отменить.", "successToast": "Кампания \"{{name}}\" успешно создана."}, "edit": {"header": "Редактировать кампанию", "description": "Редактировать детали кампании.", "successToast": "Кампания \"{{name}}\" успешно обновлена."}, "configuration": {"header": "Конфигурация", "edit": {"header": "Редактировать конфигурацию кампании", "description": "Редактировать конфигурацию кампании.", "successToast": "Конфигурация кампании успешно обновлена."}}, "create": {"title": "Создать кампанию", "description": "Создать промо-кампанию.", "hint": "Создать промо-кампанию.", "header": "Создать кампанию", "successToast": "Кампания \"{{name}}\" успешно создана."}, "fields": {"name": "Название", "identifier": "Идентификатор", "start_date": "Дата начала", "end_date": "Дата окончания", "total_spend": "Потраченный бюджет", "total_used": "Использованный бюджет", "budget_limit": "<PERSON>имит бюджета", "campaign_id": {"hint": "В этом списке отображаются только кампании с той же валютой, что и акция."}}, "budget": {"create": {"hint": "Создать бюджет кампании.", "header": "Бюджет кампании"}, "details": "Бюджет кампании", "fields": {"type": "Тип", "currency": "Валюта", "limit": "<PERSON>и<PERSON><PERSON><PERSON>", "used": "Использовано"}, "type": {"spend": {"title": "Расходы", "description": "Установить лимит на общую сумму скидок для всех использований акции."}, "usage": {"title": "Использование", "description": "Установить лимит на количество использований акции."}}, "edit": {"header": "Редактировать бюджет кампании"}}, "promotions": {"remove": {"title": "Удалить акцию из кампании", "description": "Вы собираетесь удалить акции из кампании ({{count}}). Это действие нельзя отменить."}, "alreadyAdded": "Эта акция уже добавлена в кампанию.", "alreadyAddedDiffCampaign": "Эта акция уже добавлена в другую кампанию ({{name}}).", "currencyMismatch": "Валюта акции и кампании не совпадает", "toast": {"success": "Успешно добавлено акций ({{count}}) в кампанию"}, "add": {"list": {"noRecordsMessage": "Сначала создайте акцию."}}, "list": {"noRecordsMessage": "В кампании нет акций."}}, "deleteCampaignWarning": "Вы собираетесь удалить кампанию {{name}}. Это действие нельзя отменить.", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "Прайс-листы", "subtitle": "Создавайте распродажи или переопределяйте цены для определенных условий.", "delete": {"confirmation": "Вы собираетесь удалить прайс-лист {{title}}. Это действие нельзя отменить.", "successToast": "Прайс-лист {{title}} успешно удален."}, "create": {"header": "Создать прайс-лист", "subheader": "Создайте новый прайс-лист для управления ценами на ваши товары.", "tabs": {"details": "Детали", "products": "Товары", "prices": "Цены"}, "successToast": "Прайс-лист {{title}} успешно создан.", "products": {"list": {"noRecordsMessage": "Сначала создайте товар."}}}, "edit": {"header": "Редактировать прайс-лист", "successToast": "Прайс-лист {{title}} успешно обновлен."}, "configuration": {"header": "Конфигурация", "edit": {"header": "Редактировать конфигурацию прайс-листа", "description": "Редактировать конфигурацию прайс-листа.", "successToast": "Конфигурация прайс-листа успешно обновлена."}}, "products": {"header": "Товары", "actions": {"addProducts": "Добавить товары", "editPrices": "Редактировать цены"}, "delete": {"confirmation_one": "Вы собираетесь удалить цены для {{count}} товара из прайс-листа. Это действие нельзя отменить.", "confirmation_other": "Вы собираетесь удалить цены для {{count}} товаров из прайс-листа. Это действие нельзя отменить.", "successToast_one": "Цены для {{count}} товара успешно удалены.", "successToast_other": "Цены для {{count}} товаров успешно удалены."}, "add": {"successToast": "Цены успешно добавлены в прайс-лист."}, "edit": {"successToast": "Цены успешно обновлены."}}, "fields": {"priceOverrides": {"label": "Переопределения цен", "header": "Переопределения цен"}, "status": {"label": "Статус", "options": {"active": "Активный", "draft": "Черновик", "expired": "Истек", "scheduled": "Запла<PERSON><PERSON><PERSON>ован"}}, "type": {"label": "Тип", "hint": "Выберите тип прайс-листа, который хотите создать.", "options": {"sale": {"label": "Распродажа", "description": "Цены распродажи - это временные изменения цен на товары."}, "override": {"label": "Переопределение", "description": "Переопределения обычно используются для создания цен для конкретных клиентов."}}}, "startsAt": {"label": "У прайс-листа есть дата начала?", "hint": "Запланируйте активацию прайс-листа в будущем."}, "endsAt": {"label": "У прайс-листа есть дата окончания?", "hint": "Запланируйте деактивацию прайс-листа в будущем."}, "customerAvailability": {"header": "Выберите группы клиентов", "label": "Доступность для клиентов", "hint": "Выберите, к каким группам клиентов будет применяться прайс-лист.", "placeholder": "Поиск групп клиентов", "attribute": "Группы клиентов"}}}, "profile": {"domain": "Профиль", "manageYourProfileDetails": "Управляйте данными вашего профиля.", "fields": {"languageLabel": "Язык", "usageInsightsLabel": "Аналитика использования"}, "edit": {"header": "Редактировать профиль", "languageHint": "Язык, который вы хотите использовать в панели администратора. Это не изменит язык вашего магазина.", "languagePlaceholder": "Выберите язык", "usageInsightsHint": "Поделитесь данными об использовании и помогите нам улучшить Medusa. Подробнее о том, что мы собираем и как используем, можно прочитать в нашей <0>документации</0>."}, "toast": {"edit": "Изменения в профиле сохранены"}}, "users": {"domain": "Пользователи", "editUser": "Редактировать пользователя", "inviteUser": "Пригласить пользователя", "inviteUserHint": "Пригласите нового пользователя в ваш магазин.", "sendInvite": "Отправить приглашение", "pendingInvites": "Ожидающие приглашения", "deleteInviteWarning": "Вы собираетесь удалить приглашение для пользователя {{email}}. Это действие нельзя отменить.", "resendInvite": "Отправить приглашение повторно", "copyInviteLink": "Копировать ссылку приглашения", "expiredOnDate": "Истекло {{date}}", "validFromUntil": "Действительно с <0>{{from}}</0> по <1>{{until}}</1>", "acceptedOnDate": "Принято {{date}}", "inviteStatus": {"accepted": "Принято", "pending": "<PERSON>ж<PERSON><PERSON><PERSON><PERSON><PERSON>", "expired": "Истекло"}, "roles": {"admin": "Администратор", "developer": "Разработчик", "member": "Участник"}, "deleteUserWarning": "Вы собираетесь удалить пользователя {{name}}. Это действие нельзя отменить.", "invite": "Пригласить", "list": {"empty": {"heading": "Пользователи не найдены", "description": "После приглашения пользователя он появится здесь."}, "filtered": {"heading": "Нет результатов", "description": "Нет пользователей, соответствующих текущим критериям фильтра."}}, "deleteUserSuccess": "Пользователь успешно удален"}, "store": {"domain": "Мага<PERSON>ин", "manageYourStoresDetails": "Управляйте данными вашего магазина", "editStore": "Редактировать магазин", "defaultCurrency": "Валюта по умолчанию", "defaultRegion": "Регион по умолчанию", "swapLinkTemplate": "Шаблон ссылки для обмена", "paymentLinkTemplate": "Шаблон ссылки для оплаты", "inviteLinkTemplate": "Шаблон ссылки приглашения", "currencies": "Валюты", "addCurrencies": "Добавить валюты", "enableTaxInclusivePricing": "Включить цены с учетом налога", "disableTaxInclusivePricing": "Отключить цены с учетом налога", "removeCurrencyWarning_one": "Вы собираетесь удалить {{count}} валюту из вашего магазина. Убедитесь, что вы удалили все цены в этой валюте перед продолжением.", "removeCurrencyWarning_other": "Вы собираетесь удалить {{count}} валют из вашего магазина. Убедитесь, что вы удалили все цены в этих валютах перед продолжением.", "currencyAlreadyAdded": "Валюта уже добавлена в ваш магазин.", "edit": {"header": "Редактировать магазин"}, "toast": {"update": "Магазин успешно обновлен", "currenciesUpdated": "Валюты успешно обновлены", "currenciesRemoved": "Валюты успешно удалены из магазина", "updatedTaxInclusivitySuccessfully": "Цены с учетом налога успешно обновлены"}}, "regions": {"domain": "Регионы", "subtitle": "Регион - это область, где вы продаете товары. Он может включать несколько стран и имеет свои налоговые ставки, способы доставки и валюту.", "createRegion": "Создать регион", "createRegionHint": "Управляйте налоговыми ставками и способами доставки для набора стран.", "addCountries": "Добавить страны", "editRegion": "Редактировать регион", "countriesHint": "Добавьте страны, входящие в этот регион.", "deleteRegionWarning": "Вы собираетесь удалить регион {{name}}. Это действие нельзя отменить.", "removeCountriesWarning_one": "Вы собираетесь удалить {{count}} страну из региона. Это действие нельзя отменить.", "removeCountriesWarning_other": "Вы собираетесь удалить {{count}} стран из региона. Это действие нельзя отменить.", "removeCountryWarning": "Вы собираетесь удалить страну {{name}} из региона. Это действие нельзя отменить.", "automaticTaxesHint": "Если включено, налоги будут рассчитываться только при оформлении заказа на основе адреса доставки.", "taxInclusiveHint": "При включении цены в регионе будут включать налог.", "providersHint": "Добавьте, какие платежные системы доступны в этом регионе.", "shippingOptions": "Способы доставки", "deleteShippingOptionWarning": "Вы собираетесь удалить способ доставки {{name}}. Это действие нельзя отменить.", "return": "Возврат", "outbound": "Исходящие", "priceType": "Тип цены", "flatRate": "Фиксированная ставка", "calculated": "Расчетная", "list": {"noRecordsMessage": "Создайте регион для областей, где вы ведете продажи."}, "toast": {"delete": "Регион успешно удален", "edit": "Изменения в регионе сохранены", "create": "Регион успешно создан", "countries": "Страны региона успешно обновлены"}, "shippingOption": {"createShippingOption": "Создать способ доставки", "createShippingOptionHint": "Создайте новый способ доставки для региона.", "editShippingOption": "Редактировать способ доставки", "fulfillmentMethod": "Метод фулфилмента", "type": {"outbound": "Исходящие", "outboundHint": "Используйте этот вариант, если вы создаете способ доставки для отправки товаров клиенту.", "return": "Возврат", "returnHint": "Используйте этот вариант, если вы создаете способ доставки для возврата товаров клиентом."}, "priceType": {"label": "Тип цены", "flatRate": "Фиксированная ставка", "calculated": "Расчетная"}, "availability": {"adminOnly": "Только для администратора", "adminOnlyHint": "При включении способ доставки будет доступен только в панели администратора, но не в магазине."}, "taxInclusiveHint": "При включении цена доставки будет включать налог.", "requirements": {"label": "Требования", "hint": "Укажите требования для способа доставки."}}}, "taxes": {"domain": "Налоговые регионы", "domainDescription": "Управляйте вашим налоговым регионом", "countries": {"taxCountriesHint": "Налоговые настройки применяются к перечисленным странам."}, "settings": {"editTaxSettings": "Редактировать налоговые настройки", "taxProviderLabel": "Налоговый провайдер", "systemTaxProviderLabel": "Системный налоговый провайдер", "calculateTaxesAutomaticallyLabel": "Рассчитывать налоги автоматически", "calculateTaxesAutomaticallyHint": "При включении налоговые ставки будут автоматически рассчитываться и применяться к корзинам. Когда отключено, налоги нужно рассчитывать вручную при оформлении заказа. Для внешних налоговых провайдеров рекомендуется использовать ручные налоги.", "applyTaxesOnGiftCardsLabel": "Применять налоги к подарочным картам", "applyTaxesOnGiftCardsHint": "Если включено, налоги будут применяться к подарочным картам при оформлении заказа. В некоторых странах налоговое законодательство требует взимания налога с подарочных карт при покупке.", "defaultTaxRateLabel": "Налоговая ставка по умолчанию", "defaultTaxCodeLabel": "Налоговый код по умолчанию"}, "defaultRate": {"sectionTitle": "Налоговая ставка по умолчанию"}, "taxRate": {"sectionTitle": "Налоговые ставки", "createTaxRate": "Создать налоговую ставку", "createTaxRateHint": "Создайте новую налоговую ставку для региона.", "deleteRateDescription": "Вы собираетесь удалить налоговую ставку {{name}}. Это действие нельзя отменить.", "editTaxRate": "Редактировать налоговую ставку", "editRateAction": "Редактировать ставку", "editOverridesAction": "Редактировать переопределения", "editOverridesTitle": "Редактировать переопределения налоговой ставки", "editOverridesHint": "Укажите переопределение налоговой ставки.", "deleteTaxRateWarning": "Вы собираетесь удалить налоговую ставку {{name}}. Это действие нельзя отменить.", "productOverridesLabel": "Переопределения для продукта", "productOverridesHint": "Укажите переопределение продукта для налоговой ставки.", "addProductOverridesAction": "Добавить переопределения продукта", "productTypeOverridesLabel": "Переопределения типа продукта", "productTypeOverridesHint": "Укажите переопределение типа продукта для налоговой ставки.", "addProductTypeOverridesAction": "Добавить переопределения типа продукта", "shippingOptionOverridesLabel": "Переопределения способа доставки", "shippingOptionOverridesHint": "Укажите переопределение способа доставки для налоговой ставки.", "addShippingOptionOverridesAction": "Добавить переопределения способа доставки", "productOverridesHeader": "Продукты", "productTypeOverridesHeader": "Типы продуктов", "shippingOptionOverridesHeader": "Способы доставки"}}, "locations": {"domain": "Локации", "editLocation": "Редактировать локацию", "addSalesChannels": "Добавить каналы продаж", "noLocationsFound": "Локации не найдены", "selectLocations": "Выберите локации, где хранится данный товар.", "deleteLocationWarning": "Вы собираетесь удалить локацию {{name}}. Это действие нельзя отменить.", "removeSalesChannelsWarning_one": "Вы собираетесь удалить {{count}} канал продаж из локации.", "removeSalesChannelsWarning_other": "Вы собираетесь удалить {{count}} каналов продаж из локации.", "toast": {"create": "Локация успешно создана", "update": "Локация успешно обновлена", "removeChannel": "Канал продаж успешно удален"}}, "reservations": {"domain": "Резервации", "subtitle": "Управляйте зарезервированным количеством товаров.", "deleteWarning": "Вы собираетесь удалить резервацию. Это действие нельзя отменить."}, "salesChannels": {"domain": "Каналы продаж", "subtitle": "Управляйте онлайн и офлайн каналами, через которые вы продаете товары.", "createSalesChannel": "Создать канал продаж", "createSalesChannelHint": "Создайте новый канал продаж для продажи ваших товаров.", "enabledHint": "Укажите, активен ли канал продаж.", "removeProductsWarning_one": "Вы собираетесь удалить {{count}} товар из {{sales_channel}}.", "removeProductsWarning_other": "Вы собираетесь удалить {{count}} товаров из {{sales_channel}}.", "addProducts": "Добавить товары", "editSalesChannel": "Редактировать канал продаж", "productAlreadyAdded": "Товар уже добавлен в канал продаж.", "deleteSalesChannelWarning": "Вы собираетесь удалить канал продаж {{name}}. Это действие нельзя отменить.", "toast": {"create": "Канал продаж успешно создан", "update": "Канал продаж успешно обновлен", "delete": "Канал продаж успешно удален"}, "products": {"list": {"noRecordsMessage": "В канале продаж нет товаров."}, "add": {"list": {"noRecordsMessage": "Сначала создайте товар."}}}, "tooltip": {"cannotDeleteDefault": "Нельзя удалить канал продаж по умолчанию"}}, "apiKeyManagement": {"domain": {"publishable": "Публичные API-ключи", "secret": "Секретные API-ключи"}, "subtitle": {"publishable": "Управляйте API-ключами, используемыми в магазине для ограничения запросов определенными каналами продаж.", "secret": "Управляйте API-ключами, используемыми для аутентификации администраторов в административных приложениях."}, "status": {"active": "Активный", "revoked": "Отозван"}, "type": {"publishable": "Публичный", "secret": "Секретный"}, "create": {"createPublishableHeader": "Создать публичный API-ключ", "createPublishableHint": "Создайте новый публичный API-ключ для ограничения запросов определенными каналами продаж.", "createSecretHeader": "Создать секретный API-ключ", "createSecretHint": "Создайте новый секретный API-ключ для доступа к API Medusa как авторизованный администратор.", "secretKeyCreatedHeader": "Создан секретный ключ", "secretKeyCreatedHint": "Ваш новый секретный ключ был сгенерирован. Скопируйте и сохраните его сейчас. Это единственный раз, когда он будет показан.", "copySecretTokenSuccess": "Секретный ключ скопирован в буфер обмена.", "copySecretTokenFailure": "Не удалось скопировать секретный ключ в буфер обмена.", "successToast": "API-ключ успешно создан."}, "edit": {"header": "Редактировать API-ключ", "description": "Отредактируйте название API-ключа.", "successToast": "API-ключ {{title}} успешно обновлен."}, "salesChannels": {"title": "Добавить каналы продаж", "description": "Добавьте каналы продаж, к которым должен быть ограничен API-ключ.", "successToast_one": "{{count}} канал продаж успешно добавлен к API-ключу.", "successToast_other": "{{count}} каналов продаж успешно добавлены к API-ключу.", "alreadyAddedTooltip": "Канал продаж уже добавлен к API-ключу.", "list": {"noRecordsMessage": "Нет каналов продаж в области действия публичного API-ключа."}}, "delete": {"warning": "Вы собираетесь удалить API-ключ {{title}}. Это действие нельзя отменить.", "successToast": "API-ключ {{title}} успешно удален."}, "revoke": {"warning": "Вы собираетесь отозвать API-ключ {{title}}. Это действие нельзя отменить.", "successToast": "API-ключ {{title}} успешно отозван."}, "addSalesChannels": {"list": {"noRecordsMessage": "Сначала создайте канал продаж."}}, "removeSalesChannel": {"warning": "Вы собираетесь удалить канал продаж {{name}} из API-ключа. Это действие нельзя отменить.", "warningBatch_one": "Вы собираетесь удалить {{count}} канал продаж из API-ключа. Это действие нельзя отменить.", "warningBatch_other": "Вы собираетесь удалить {{count}} каналов продаж из API-ключа. Это действие нельзя отменить.", "successToast": "Канал продаж успешно удален из API-ключа.", "successToastBatch_one": "{{count}} канал продаж успешно удален из API-ключа.", "successToastBatch_other": "{{count}} каналов продаж успешно удалены из API-ключа."}, "actions": {"revoke": "Отозвать API-ключ", "copy": "Копировать API-ключ", "copySuccessToast": "API-ключ скопирован в буфер обмена."}, "table": {"lastUsedAtHeader": "Последнее использование", "createdAtHeader": "Создан"}, "fields": {"lastUsedAtLabel": "Последнее использование", "revokedByLabel": "Отозван", "revokedAtLabel": "Отозван", "createdByLabel": "Создан"}}, "returnReasons": {"domain": "Причины возврата", "subtitle": "Управляйте причинами возврата товаров.", "calloutHint": "Управляйте причинами для категоризации возвратов.", "editReason": "Редактировать причину возврата", "create": {"header": "Добавить причину возврата", "subtitle": "Укажите наиболее распространенные причины возврата.", "hint": "Создайте новую причину возврата для категоризации возвратов.", "successToast": "Причина возврата {{label}} успешно создана."}, "edit": {"header": "Редактировать причину возврата", "subtitle": "Отредактируйте значение причины возврата.", "successToast": "Причина возврата {{label}} успешно обновлена."}, "delete": {"confirmation": "Вы собираетесь удалить причину возврата {{label}}. Это действие нельзя отменить.", "successToast": "Причина возврата {{label}} успешно удалена."}, "fields": {"value": {"label": "Значение", "placeholder": "неверный_размер", "tooltip": "Значение должно быть уникальным идентификатором причины возврата."}, "label": {"label": "Название", "placeholder": "Неверный размер"}, "description": {"label": "Описание", "placeholder": "Клиент получил неверный размер"}}}, "login": {"forgotPassword": "Забыли пароль? - <0>Сбросить</0>", "title": "Добро пожаловать в Medusa", "hint": "Войдите, чтобы получить доступ к панели управления"}, "invite": {"title": "Добро пожаловать в Medusa", "hint": "Создайте свой аккаунт ниже", "backToLogin": "Вернуться к входу", "createAccount": "Создать аккаунт", "alreadyHaveAccount": "Уже есть аккаунт? - <0>Войти</0>", "emailTooltip": "Ваш email нельзя изменить. Если вы хотите использовать другой email, необходимо запросить новое приглашение.", "invalidInvite": "Приглашение недействительно или истекло.", "successTitle": "<PERSON>а<PERSON> аккаунт зарегистрирован", "successHint": "Начните работу с Medusa Admin прямо сейчас.", "successAction": "Запустить Medusa Admin", "invalidTokenTitle": "Ваш токен приглашения недействителен", "invalidTokenHint": "Попробуйте запросить новую ссылку приглашения.", "passwordMismatch": "Пароли не совпадают", "toast": {"accepted": "Приглашение успешно принято"}}, "resetPassword": {"title": "Сбросить пароль", "hint": "Введите ваш email ниже, и мы отправим вам инструкции по сбросу пароля.", "email": "Email", "sendResetInstructions": "Отправить инструкции по сбросу", "backToLogin": "<0>Вернуться к входу</0>", "newPasswordHint": "Выберите новый пароль ниже.", "invalidTokenTitle": "Ваш токен сброса недействителен", "invalidTokenHint": "Попробуйте запросить новую ссылку для сброса.", "expiredTokenTitle": "Срок действия вашего токена сброса истек", "goToResetPassword": "Перейти к сбросу пароля", "resetPassword": "Сбросить пароль", "newPassword": "Новый пароль", "repeatNewPassword": "Повторите новый пароль", "tokenExpiresIn": "Токен истекает через <0>{{time}}</0> минут", "successfulRequestTitle": "Письмо успешно отправлено", "successfulRequest": "Мы отправили вам email, который вы можете использовать для сброса пароля. Проверьте папку спам, если письмо не пришло через несколько минут.", "successfulResetTitle": "Пароль успешно сброшен", "successfulReset": "Пожалуйста, войдите на странице входа.", "passwordMismatch": "Пароли не совпадают", "invalidLinkTitle": "Ваша ссылка для сброса недействительна", "invalidLinkHint": "Попробуйте сбросить пароль еще раз."}, "workflowExecutions": {"domain": "Рабочие процессы", "subtitle": "Просматривайте и отслеживайте выполнение рабочих процессов в приложении Medusa.", "transactionIdLabel": "ID транзакции", "workflowIdLabel": "ID рабочего процесса", "progressLabel": "Прогресс", "stepsCompletedLabel_one": "{{completed}} из {{count}} шага", "stepsCompletedLabel_other": "{{completed}} из {{count}} шагов", "list": {"noRecordsMessage": "Рабочие процессы еще не выполнялись."}, "history": {"sectionTitle": "История", "runningState": "Выполняется...", "awaitingState": "Ожидание", "failedState": "Неудача", "skippedState": "Пропущено", "skippedFailureState": "Пропущено (неудача)", "definitionLabel": "Определение", "outputLabel": "Вывод", "compensateInputLabel": "Компенсационный ввод", "revertedLabel": "Отменено", "errorLabel": "Ошибка"}, "state": {"done": "Выполнено", "failed": "Неудача", "reverted": "Отменено", "invoking": "Вызывается", "compensating": "Компенсируется", "notStarted": "Не начато"}, "transaction": {"state": {"waitingToCompensate": "Ожидание компенсации"}}, "step": {"state": {"skipped": "Пропущено", "skippedFailure": "Пропущено (неудача)", "dormant": "Неактивно", "timeout": "Таймаут"}}}, "productTypes": {"domain": "Типы продуктов", "subtitle": "Организуйте ваши продукты по типам.", "create": {"header": "Создать тип продукта", "hint": "Создайте новый тип продукта для категоризации ваших продуктов.", "successToast": "Тип продукта {{value}} успешно создан."}, "edit": {"header": "Редактировать тип продукта", "successToast": "Тип продукта {{value}} успешно обновлен."}, "delete": {"confirmation": "Вы собираетесь удалить тип продукта {{value}}. Это действие нельзя отменить.", "successToast": "Тип продукта {{value}} успешно удален."}, "fields": {"value": "Значение"}}, "productTags": {"domain": "Теги продуктов", "create": {"header": "Создать тег продукта", "subtitle": "Создайте новый тег продукта для категоризации ваших продуктов.", "successToast": "Тег продукта {{value}} успешно создан."}, "edit": {"header": "Редактировать тег продукта", "subtitle": "Редактировать значение тега продукта.", "successToast": "Тег продукта {{value}} успешно обновлен."}, "delete": {"confirmation": "Вы собираетесь удалить тег продукта {{value}}. Это действие нельзя отменить.", "successToast": "Тег продукта {{value}} успешно удален."}, "fields": {"value": "Значение"}}, "notifications": {"domain": "Уведомления", "emptyState": {"title": "Нет уведомлений", "description": "У вас пока нет уведомлений, но когда они появятся, они будут отображаться здесь."}, "accessibility": {"description": "здесь будут перечислены уведомления о действиях Medusa."}}, "errors": {"serverError": "Ошибка сервера — попробуйте позже.", "invalidCredentials": "Неверный email или пароль"}, "statuses": {"scheduled": "Запланировано", "expired": "Истекло", "active": "Активно", "enabled": "Включено", "disabled": "Отключено", "inactive": "Неактивный", "draft": "Черновик"}, "labels": {"productVariant": "Вариант продукта", "prices": "Цены", "available": "Доступно", "inStock": "В наличии", "added": "Добавлено", "removed": "Удалено", "from": "От", "to": "До"}, "fields": {"amount": "Сумма", "refundAmount": "Сумма возврата", "name": "Название", "default": "По умолчанию", "lastName": "Фамилия", "firstName": "Имя", "title": "Заголовок", "customTitle": "Пользовательский заголовок", "manageInventory": "Управление запасами", "inventoryKit": "Имеет набор запасов", "inventoryItems": "Элементы запасов", "inventoryItem": "Элемен<PERSON> за<PERSON><PERSON><PERSON><PERSON>", "requiredQuantity": "Требуемое количество", "description": "Описание", "email": "Email", "password": "Пароль", "repeatPassword": "Повторите пароль", "confirmPassword": "Подтвердите пароль", "newPassword": "Новый пароль", "repeatNewPassword": "Повторите новый пароль", "categories": "Категории", "shippingMethod": "Способ доставки", "configurations": "Конфигурации", "conditions": "Условия", "category": "Категория", "collection": "Коллекция", "discountable": "Со скидкой", "handle": "Идентификатор", "subtitle": "Подзаголовок", "item": "<PERSON><PERSON><PERSON><PERSON>", "qty": "кол-во", "limit": "<PERSON>и<PERSON><PERSON><PERSON>", "tags": "Теги", "type": "Тип", "reason": "Причина", "none": "нет", "all": "все", "search": "Поиск", "percentage": "Процент", "sales_channels": "Каналы продаж", "customer_groups": "Группы клиентов", "product_tags": "Теги продуктов", "product_types": "Типы продуктов", "product_collections": "Коллекции продуктов", "status": "Статус", "code": "<PERSON>од", "value": "Значение", "disabled": "Отключено", "dynamic": "Динамический", "normal": "Обычный", "years": "<PERSON>е<PERSON>", "months": "Месяцев", "days": "<PERSON><PERSON><PERSON><PERSON>", "hours": "<PERSON><PERSON><PERSON><PERSON>", "minutes": "<PERSON>и<PERSON><PERSON><PERSON>", "totalRedemptions": "Всего погашений", "countries": "Страны", "paymentProviders": "Платежные провайдеры", "refundReason": "Причина возврата", "fulfillmentProviders": "Провайдеры доставки", "fulfillmentProvider": "Провайдер доставки", "providers": "Провайдеры", "availability": "Доступность", "inventory": "Запасы", "optional": "Необязательно", "note": "Примечание", "automaticTaxes": "Автоматические налоги", "taxInclusivePricing": "Цены с учетом налогов", "currency": "Валюта", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "address2": "Квартира, офи<PERSON> и т.д.", "city": "Город", "postalCode": "Почтовый индекс", "country": "Страна", "state": "<PERSON>т<PERSON><PERSON>", "province": "Область", "company": "Компания", "phone": "Телефон", "metadata": "Метаданные", "selectCountry": "Выберите страну", "products": "Продукты", "variants": "Варианты", "orders": "Заказы", "account": "Аккаунт", "total": "Итого по заказу", "paidTotal": "Всего получено", "totalExclTax": "Всего без налога", "subtotal": "Подытог", "shipping": "Доставка", "outboundShipping": "Исходящая доставка", "returnShipping": "Обратная доставка", "tax": "Налог", "created": "Создано", "key": "<PERSON><PERSON><PERSON><PERSON>", "customer": "Кли<PERSON><PERSON>т", "date": "Дата", "order": "Зак<PERSON>з", "fulfillment": "Фул<PERSON>ил<PERSON><PERSON>нт", "provider": "Провайдер", "payment": "Оплата", "items": "Товары", "salesChannel": "Канал продаж", "region": "Регион", "discount": "Скидка", "role": "Роль", "sent": "Отправлено", "salesChannels": "Каналы продаж", "product": "Продукт", "createdAt": "Создано", "updatedAt": "Обновлено", "revokedAt": "Отозвано", "true": "ИСТИНА", "false": "ЛОЖЬ", "giftCard": "Подарочная карта", "tag": "Тег", "dateIssued": "Дата выпуска", "issuedDate": "Дата выпуска", "expiryDate": "Срок действия", "price": "Цена", "priceTemplate": "Цена {{regionOrCurrency}}", "height": "Высота", "width": "Ши<PERSON><PERSON><PERSON>", "length": "Длина", "weight": "<PERSON>е<PERSON>", "midCode": "MID код", "hsCode": "HS код", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "Количество в наличии", "barcode": "Штрих-код", "countryOfOrigin": "Страна происхождения", "material": "Материал", "thumbnail": "Миниатюра", "sku": "SKU", "managedInventory": "Управляемые запасы", "allowBackorder": "Разрешить предзаказ", "inStock": "В наличии", "location": "Местоположение", "quantity": "Количество", "variant": "Вар<PERSON><PERSON><PERSON>т", "id": "ID", "parent": "Родитель", "minSubtotal": "Мин. подытог", "maxSubtotal": "Макс. подытог", "shippingProfile": "Профиль доставки", "summary": "Сводка", "details": "Детали", "label": "Метка", "rate": "Ставка", "requiresShipping": "Требует доставки", "unitPrice": "Цена за единицу", "startDate": "Дата начала", "endDate": "Дата окончания", "draft": "Черновик", "values": "Значения", "by": "От"}, "quotes": {"domain": "Котировки", "title": "Котировки", "subtitle": "Управление котировками и предложениями клиентов", "noQuotes": "Котировки не найдены", "noQuotesDescription": "В настоящее время котировок нет. Создайте одну из витрины.", "table": {"id": "ID Котировки", "customer": "Кли<PERSON><PERSON>т", "status": "Статус", "company": "Компания", "amount": "Сумма", "createdAt": "Создано", "updatedAt": "Обновлено", "actions": "Действия"}, "status": {"pending_merchant": "Ожидает продавца", "pending_customer": "Ожи<PERSON><PERSON><PERSON>т клиента", "merchant_rejected": "Отклонено продавцом", "customer_rejected": "Отклонено клиентом", "accepted": "Принято", "unknown": "Неизвестно"}, "actions": {"sendQuote": "Отправить котировку", "rejectQuote": "Отклонить котировку", "viewOrder": "Просмотреть заказ"}, "details": {"header": "Детали котировки", "quoteSummary": "Сводка котировки", "customer": "Кли<PERSON><PERSON>т", "company": "Компания", "items": "Товары", "total": "Итого", "subtotal": "Подытог", "shipping": "Доставка", "tax": "Налог", "discounts": "Скидки", "originalTotal": "Первоначальная сумма", "quoteTotal": "Сумма котировки", "messages": "Сообщения", "actions": "Действия", "sendMessage": "Отправить сообщение", "send": "Отправить", "pickQuoteItem": "Выбрать товар котировки", "selectQuoteItem": "Выберите товар котировки для комментария", "selectItem": "Выбрать товар", "manage": "Управлять", "phone": "Телефон", "spendingLimit": "<PERSON>и<PERSON><PERSON><PERSON> расходов", "name": "Имя", "manageQuote": "Управлять котировкой", "noItems": "В этой котировке нет товаров", "noMessages": "Нет сообщений для этой котировки"}, "items": {"title": "Продукт", "quantity": "Количество", "unitPrice": "Цена за единицу", "total": "Итого"}, "messages": {"admin": "Администратор", "customer": "Кли<PERSON><PERSON>т", "placeholder": "Введите ваше сообщение здесь..."}, "filters": {"status": "Фильтр по статусу"}, "confirmations": {"sendTitle": "Отправить котировку", "sendDescription": "Вы уверены, что хотите отправить эту котировку клиенту?", "rejectTitle": "Отклонить котировку", "rejectDescription": "Вы уверены, что хотите отклонить эту котировку?"}, "acceptance": {"message": "Котировка была принята"}, "toasts": {"sendSuccess": "Котировка успешно отправлена клиенту", "sendError": "Не удалось отправить котировку", "rejectSuccess": "Котировка клиента успешно отклонена", "rejectError": "Не удалось отклонить котировку", "messageSuccess": "Сообщение успешно отправлено клиенту", "messageError": "Не удалось отправить сообщение", "updateSuccess": "Котировка успешно обновлена"}, "manage": {"overridePriceHint": "Переопределить первоначальную цену для этого товара", "updatePrice": "Обновить цену"}}, "companies": {"domain": "Компании", "title": "Компании", "subtitle": "Управление деловыми отношениями", "noCompanies": "Компании не найдены", "noCompaniesDescription": "Создайте свою первую компанию для начала.", "notFound": "Компания не найдена", "table": {"name": "Название", "phone": "Телефон", "email": "Email", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "employees": "Сотрудники", "customerGroup": "Группа клиентов", "actions": "Действия"}, "fields": {"name": "Название компании", "email": "Email", "phone": "Телефон", "website": "Веб-сайт", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Город", "state": "Область", "zip": "Почтовый индекс", "zipCode": "Почтовый индекс", "country": "Страна", "currency": "Валюта", "logoUrl": "URL логотипа", "description": "Описание", "employees": "Сотрудники", "customerGroup": "Группа клиентов", "approvalSettings": "Настройки утверждения"}, "placeholders": {"name": "Введите название компании", "email": "Введите адрес электронной почты", "phone": "Введите номер телефона", "website": "Введите URL веб-сайта", "address": "Введите адрес", "city": "Введите город", "state": "Введите область", "zip": "Введите почтовый индекс", "logoUrl": "Введите URL логотипа", "description": "Введите описание компании", "selectCountry": "Выберите страну", "selectCurrency": "Выберите валюту"}, "validation": {"nameRequired": "Название компании обязательно", "emailRequired": "Email обязателен", "emailInvalid": "Неверный адрес электронной почты", "addressRequired": "Адрес обязателен", "cityRequired": "Город обязателен", "stateRequired": "Область обязательна", "zipRequired": "Почтовый индекс обязателен"}, "create": {"title": "Создать компанию", "description": "Создать новую компанию для управления деловыми отношениями.", "submit": "Создать компанию"}, "edit": {"title": "Редактировать компанию", "submit": "Обновить компанию"}, "details": {"actions": "Действия"}, "approvals": {"requiresAdminApproval": "Требует утверждения администратора", "requiresSalesManagerApproval": "Требует утверждения менеджера по продажам", "noApprovalRequired": "Утверждение не требуется"}, "deleteWarning": "Это навсегда удалит компанию и все связанные данные.", "approvalSettings": {"title": "Настройки утверждения", "requiresAdminApproval": "Требует утверждения администратора", "requiresSalesManagerApproval": "Требует утверждения менеджера по продажам", "requiresAdminApprovalDesc": "Заказы от этой компании требуют утверждения администратора перед обработкой", "requiresSalesManagerApprovalDesc": "Заказы от этой компании требуют утверждения менеджера по продажам перед обработкой", "updateSuccess": "Настройки утверждения успешно обновлены", "updateError": "Не удалось обновить настройки утверждения"}, "customerGroup": {"title": "Управление группой клиентов", "hint": "Назначьте эту компанию группе клиентов для применения групповых цен и разрешений.", "name": "Название группы клиентов", "groupName": "Группа клиентов", "actions": "Действия", "add": "Добавить", "remove": "Удалить", "description": "Управление группами клиентов для этой компании", "noGroups": "Нет доступных групп клиентов", "addSuccess": "Компания успешно добавлена в группу клиентов", "addError": "Не удалось добавить компанию в группу клиентов", "removeSuccess": "Компания успешно удалена из группы клиентов", "removeError": "Не удалось удалить компанию из группы клиентов"}, "actions": {"edit": "Редактировать компанию", "editDetails": "Редактировать детали", "manageCustomerGroup": "Управлять группой клиентов", "approvalSettings": "Настройки утверждения", "delete": "Удалить компанию", "confirmDelete": "Подтвердить удаление"}, "delete": {"title": "Удалить компанию", "description": "Вы уверены, что хотите удалить эту компанию? Это действие нельзя отменить."}, "employees": {"title": "Сотрудники", "noEmployees": "Сотрудники для этой компании не найдены", "name": "Имя", "email": "Email", "phone": "Телефон", "role": "Роль", "spendingLimit": "<PERSON>и<PERSON><PERSON><PERSON> расходов", "admin": "Администратор", "employee": "Сотрудник", "add": "Добавить сотрудника", "create": {"title": "Создать сотрудника", "success": "Сотрудник успешно создан", "error": "Не удалось создать сотрудника"}, "form": {"details": "Подробная информация", "permissions": "Разрешения", "firstName": "Имя", "lastName": "Фамилия", "email": "Email", "phone": "Телефон", "spendingLimit": "<PERSON>и<PERSON><PERSON><PERSON> расходов", "adminAccess": "Доступ администратора", "isAdmin": "Является администратором", "isAdminDesc": "Предоставить этому сотруднику права администратора", "isAdminTooltip": "Администраторы могут управлять настройками компании и другими сотрудниками", "firstNamePlaceholder": "Введите имя", "lastNamePlaceholder": "Введите фамилию", "emailPlaceholder": "Введите адрес электронной почты", "phonePlaceholder": "Введите номер телефона", "spendingLimitPlaceholder": "Введите лимит расходов", "save": "Сохранить", "saving": "Сохранение..."}, "delete": {"confirmation": "Вы уверены, что хотите удалить этого сотрудника?", "success": "Сотрудник успешно удален"}, "edit": {"title": "Редактировать сотрудника"}, "toasts": {"updateSuccess": "Сотрудник успешно обновлен", "updateError": "Не удалось обновить сотрудника"}}, "toasts": {"createSuccess": "Компания успешно создана", "createError": "Не удалось создать компанию", "updateSuccess": "Компания успешно обновлена", "updateError": "Не удалось обновить компанию", "deleteSuccess": "Компания успешно удалена", "deleteError": "Не удалось удалить компанию"}}, "approvals": {"domain": "Утверждения", "title": "Утверждения", "subtitle": "Управление рабочими процессами утверждения", "noApprovals": "Утверждения не найдены", "noApprovalsDescription": "В настоящее время нет утверждений для рассмотрения.", "table": {"id": "ID", "type": "Тип", "company": "Компания", "customer": "Кли<PERSON><PERSON>т", "amount": "Сумма", "status": "Статус", "createdAt": "Создано"}, "status": {"pending": "<PERSON>ж<PERSON><PERSON><PERSON><PERSON><PERSON>", "approved": "Утверждено", "rejected": "Отклонено", "expired": "Истекло", "unknown": "Неизвестно"}, "details": {"header": "Детали утверждения", "summary": "Сводка утверждения", "company": "Компания", "customer": "Кли<PERSON><PERSON>т", "order": "Зак<PERSON>з", "amount": "Сумма", "updatedAt": "Обновлено", "reason": "Причина", "actions": "Действия"}, "actions": {"approve": "Утвердить", "reject": "Отклонить", "confirmApprove": "Подтвердить утверждение", "confirmReject": "Подтвердить отклонение", "reasonPlaceholder": "Введите причину (необязательно)..."}, "filters": {"status": "Фильтр по статусу"}, "toasts": {"approveSuccess": "Успешно утверждено", "approveError": "Не удалось утвердить", "rejectSuccess": "Успешно отклонено", "rejectError": "Не удалось отклонить"}}, "dateTime": {"years_one": "Год", "years_other": "<PERSON>е<PERSON>", "months_one": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "months_other": "Месяцев", "weeks_one": "Неделя", "weeks_other": "Недель", "days_one": "День", "days_other": "<PERSON><PERSON><PERSON><PERSON>", "hours_one": "<PERSON><PERSON><PERSON>", "hours_other": "<PERSON><PERSON><PERSON><PERSON>", "minutes_one": "Минута", "minutes_other": "<PERSON>и<PERSON><PERSON><PERSON>", "seconds_one": "Секунда", "seconds_other": "Секунд"}}