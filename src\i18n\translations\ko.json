{"$schema": "./$schema.json", "general": {"ascending": "오름차순", "descending": "내림차순", "add": "추가", "start": "시작", "end": "종료", "open": "열기", "close": "닫기", "apply": "적용", "range": "범위", "search": "검색", "of": "중", "results": "결과", "pages": "페이지", "next": "다음", "prev": "이전", "is": "은(는)", "timeline": "타임라인", "success": "성공", "warning": "경고", "tip": "팁", "error": "오류", "select": "선택", "selected": "선택됨", "enabled": "활성화됨", "disabled": "비활성화됨", "expired": "만료됨", "active": "활성", "revoked": "취소됨", "new": "신규", "modified": "수정됨", "added": "추가됨", "removed": "제거됨", "admin": "관리자", "store": "스토어", "details": "상세 정보", "items_one": "{{count}}개 항목", "items_other": "{{count}}개 항목", "countSelected": "{{count}}개 선택됨", "countOfTotalSelected": "{{total}}개 중 {{count}}개 선택됨", "plusCount": "+ {{count}}개", "plusCountMore": "+ {{count}}개 더", "areYouSure": "확실합니까?", "areYouSureDescription": "{{entity}} {{title}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "noRecordsFound": "기록을 찾을 수 없음", "typeToConfirm": "확인을 위해 {val}을(를) 입력하세요:", "noResultsTitle": "결과 없음", "noResultsMessage": "필터 또는 검색어를 변경해 보세요", "noSearchResults": "검색 결과 없음", "noSearchResultsFor": "<0>'{{query}}'</0>에 대한 검색 결과가 없습니다", "noRecordsTitle": "기록 없음", "noRecordsMessage": "표시할 기록이 없습니다", "unsavedChangesTitle": "이 양식을 나가시겠습니까?", "unsavedChangesDescription": "저장되지 않은 변경 사항이 있으며, 이 양식을 나가면 변경 사항이 손실됩니다.", "includesTaxTooltip": "이 열의 가격은 세금이 포함된 금액입니다.", "excludesTaxTooltip": "이 열의 가격은 세금이 제외된 금액입니다.", "noMoreData": "더 이상 데이터가 없습니다", "actions": "작업"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}}개 키", "numberOfKeys_other": "{{count}}개 키", "drawer": {"header_one": "JSON <0>· {{count}}개 키</0>", "header_other": "JSON <0>· {{count}}개 키</0>", "description": "이 객체의 JSON 데이터를 확인합니다."}}, "metadata": {"header": "메타데이터", "numberOfKeys_one": "{{count}}개 키", "numberOfKeys_other": "{{count}}개 키", "edit": {"header": "메타데이터 편집", "description": "이 객체의 메타데이터를 편집합니다.", "successToast": "메타데이터가 성공적으로 업데이트되었습니다.", "actions": {"insertRowAbove": "위에 행 삽입", "insertRowBelow": "아래에 행 삽입", "deleteRow": "행 삭제"}, "labels": {"key": "키", "value": "값"}, "complexRow": {"label": "일부 행이 비활성화됨", "description": "이 객체에는 배열이나 객체와 같은 원시 타입이 아닌 메타데이터가 포함되어 있어 여기서 편집할 수 없습니다. 비활성화된 행을 편집하려면 API를 직접 사용하세요.", "tooltip": "이 행은 원시 타입이 아닌 데이터를 포함하고 있어 비활성화되었습니다."}}}, "validation": {"mustBeInt": "값은 정수여야 합니다.", "mustBePositive": "값은 양수여야 합니다."}, "actions": {"save": "저장", "saveAsDraft": "임시 저장", "copy": "복사", "copied": "복사됨", "duplicate": "복제", "publish": "게시", "create": "생성", "delete": "삭제", "remove": "제거", "revoke": "취소", "cancel": "취소", "forceConfirm": "강제 확인", "continueEdit": "편집 계속", "enable": "활성화", "disable": "비활성화", "undo": "실행 취소", "complete": "완료", "viewDetails": "상세 보기", "back": "뒤로", "close": "닫기", "showMore": "더 보기", "continue": "계속", "continueWithEmail": "이메일로 계속", "idCopiedToClipboard": "ID가 클립보드에 복사됨", "addReason": "사유 추가", "addNote": "메모 추가", "reset": "초기화", "confirm": "확인", "edit": "편집", "addItems": "항목 추가", "download": "다운로드", "clear": "지우기", "clearAll": "모두 지우기", "apply": "적용", "add": "추가", "select": "선택", "browse": "찾아보기", "logout": "로그아웃", "hide": "숨기기", "export": "내보내기", "import": "가져오기", "cannotUndo": "이 작업은 되돌릴 수 없습니다"}, "operators": {"in": "포함"}, "app": {"search": {"label": "검색", "title": "검색", "description": "주문, 제품, 고객 등을 포함한 전체 스토어를 검색합니다.", "allAreas": "모든 영역", "navigation": "탐색", "openResult": "결과 열기", "showMore": "더 보기", "placeholder": "이동하거나 무엇이든 찾아보세요...", "noResultsTitle": "결과를 찾을 수 없음", "noResultsMessage": "검색어와 일치하는 항목을 찾을 수 없습니다.", "emptySearchTitle": "검색어 입력", "emptySearchMessage": "키워드나 문구를 입력하여 탐색하세요.", "loadMore": "{{count}}개 더 불러오기", "groups": {"all": "모든 영역", "customer": "고객", "customerGroup": "고객 그룹", "product": "제품", "productVariant": "제품 변형", "inventory": "재고", "reservation": "예약", "category": "카테고리", "collection": "컬렉션", "order": "주문", "promotion": "프로모션", "campaign": "캠페인", "priceList": "가격 목록", "user": "사용자", "region": "지역", "taxRegion": "세금 지역", "returnReason": "반품 사유", "salesChannel": "판매 채널", "productType": "제품 유형", "productTag": "제품 태그", "location": "위치", "shippingProfile": "배송 프로필", "publishableApiKey": "공개 API 키", "secretApiKey": "비밀 API 키", "command": "명령", "navigation": "탐색"}}, "keyboardShortcuts": {"pageShortcut": "바로 이동", "settingShortcut": "설정", "commandShortcut": "명령", "then": "다음", "navigation": {"goToOrders": "주문", "goToProducts": "제품", "goToCollections": "컬렉션", "goToCategories": "카테고리", "goToCustomers": "고객", "goToCustomerGroups": "고객 그룹", "goToInventory": "재고", "goToReservations": "예약", "goToPriceLists": "가격 목록", "goToPromotions": "프로모션", "goToCampaigns": "캠페인"}, "settings": {"goToSettings": "설정", "goToStore": "스토어", "goToUsers": "사용자", "goToRegions": "지역", "goToTaxRegions": "세금 지역", "goToSalesChannels": "판매 채널", "goToProductTypes": "제품 유형", "goToLocations": "위치", "goToPublishableApiKeys": "공개 API 키", "goToSecretApiKeys": "비밀 API 키", "goToWorkflows": "워크플로우", "goToProfile": "프로필", "goToReturnReasons": "반품 사유"}}, "menus": {"user": {"documentation": "문서", "changelog": "변경 로그", "shortcuts": "단축키", "profileSettings": "프로필 설정", "theme": {"label": "테마", "dark": "어두운 테마", "light": "밝은 테마", "system": "시스템"}}, "store": {"label": "스토어", "storeSettings": "스토어 설정"}, "actions": {"logout": "로그아웃"}}, "nav": {"accessibility": {"title": "탐색", "description": "대시보드의 탐색 메뉴입니다."}, "common": {"extensions": "확장 기능"}, "main": {"store": "스토어", "storeSettings": "스토어 설정"}, "settings": {"header": "설정", "general": "일반", "developer": "개발자", "myAccount": "내 계정"}}}, "dataGrid": {"columns": {"view": "보기", "resetToDefault": "기본값으로 재설정", "disabled": "표시할 열 변경이 비활성화되었습니다."}, "shortcuts": {"label": "단축키", "commands": {"undo": "실행 취소", "redo": "다시 실행", "copy": "복사", "paste": "붙여넣기", "edit": "편집", "delete": "삭제", "clear": "지우기", "moveUp": "위로 이동", "moveDown": "아래로 이동", "moveLeft": "왼쪽으로 이동", "moveRight": "오른쪽으로 이동", "moveTop": "맨 위로 이동", "moveBottom": "맨 아래로 이동", "selectDown": "아래로 선택", "selectUp": "위로 선택", "selectColumnDown": "열 아래로 선택", "selectColumnUp": "열 위로 선택", "focusToolbar": "툴바 포커스", "focusCancel": "취소 포커스"}}, "errors": {"fixError": "오류 수정", "count_one": "{{count}}개 오류", "count_other": "{{count}}개 오류"}}, "filters": {"sortLabel": "정렬", "filterLabel": "필터", "searchLabel": "검색", "date": {"today": "오늘", "lastSevenDays": "최근 7일", "lastThirtyDays": "최근 30일", "lastNinetyDays": "최근 90일", "lastTwelveMonths": "최근 12개월", "custom": "사용자 지정", "from": "시작일", "to": "종료일", "starting": "시작", "ending": "종료"}, "compare": {"lessThan": "미만", "greaterThan": "초과", "exact": "정확히", "range": "범위", "lessThanLabel": "{{value}} 미만", "greaterThanLabel": "{{value}} 초과", "andLabel": "그리고"}, "sorting": {"alphabeticallyAsc": "A에서 Z순", "alphabeticallyDesc": "Z에서 A순", "dateAsc": "최신순", "dateDesc": "오래된순"}, "radio": {"yes": "예", "no": "아니오", "true": "참", "false": "거짓"}, "addFilter": "필터 추가"}, "errorBoundary": {"badRequestTitle": "400 - 잘못된 요청", "badRequestMessage": "잘못된 구문으로 인해 서버가 요청을 이해할 수 없습니다.", "notFoundTitle": "404 - 이 주소에 페이지가 없습니다", "notFoundMessage": "URL을 확인하고 다시 시도하거나 검색창을 사용하여 찾으려는 항목을 검색하세요.", "internalServerErrorTitle": "500 - 내부 서버 오류", "internalServerErrorMessage": "서버에서 예기치 않은 오류가 발생했습니다. 나중에 다시 시도해주세요.", "defaultTitle": "오류가 발생했습니다", "defaultMessage": "이 페이지를 렌더링하는 동안 예기치 않은 오류가 발생했습니다.", "noMatchMessage": "찾고 계신 페이지가 존재하지 않습니다.", "backToDashboard": "대시보드로 돌아가기"}, "addresses": {"title": "주소", "shippingAddress": {"header": "배송 주소", "editHeader": "배송 주소 편집", "editLabel": "배송 주소", "label": "배송 주소"}, "billingAddress": {"header": "청구 주소", "editHeader": "청구 주소 편집", "editLabel": "청구 주소", "label": "청구 주소", "sameAsShipping": "배송 주소와 동일"}, "contactHeading": "연락처", "locationHeading": "위치"}, "email": {"editHeader": "이메일 편집", "editLabel": "이메일", "label": "이메일"}, "transferOwnership": {"header": "소유권 이전", "label": "소유권 이전", "details": {"order": "주문 상세 정보", "draft": "임시 저장 상세 정보"}, "currentOwner": {"label": "현재 소유자", "hint": "주문의 현재 소유자입니다."}, "newOwner": {"label": "새 소유자", "hint": "주문을 이전할 새 소유자입니다."}, "validation": {"mustBeDifferent": "새 소유자는 현재 소유자와 달라야 합니다.", "required": "새 소유자는 필수 항목입니다."}}, "sales_channels": {"availableIn": "<1>{{y}}</1>개 중 <0>{{x}}</0>개 판매 채널에서 사용 가능"}, "products": {"domain": "제품", "list": {"noRecordsMessage": "판매를 시작하려면 첫 번째 제품을 생성하세요."}, "edit": {"header": "제품 편집", "description": "제품 상세 정보를 편집합니다.", "successToast": "제품 {{title}}이(가) 성공적으로 업데이트되었습니다."}, "create": {"title": "제품 생성", "description": "새 제품을 생성합니다.", "header": "일반", "tabs": {"details": "상세 정보", "organize": "분류", "variants": "변형", "inventory": "재고 키트"}, "errors": {"variants": "최소 하나의 변형을 선택해주세요.", "options": "최소 하나의 옵션을 생성해주세요.", "uniqueSku": "SKU는 고유해야 합니다."}, "inventory": {"heading": "재고 키트", "label": "변형의 재고 키트에 재고 항목을 추가합니다.", "itemPlaceholder": "재고 항목 선택", "quantityPlaceholder": "키트에 필요한 수량은 얼마입니까?"}, "variants": {"header": "변형", "subHeadingTitle": "예, 이것은 변형이 있는 제품입니다", "subHeadingDescription": "체크 해제되면 기본 변형을 자동으로 생성합니다", "optionTitle": {"placeholder": "사이즈"}, "optionValues": {"placeholder": "소, 중, 대"}, "productVariants": {"label": "제품 변형", "hint": "이 순위는 스토어프론트에서 변형의 순서에 영향을 미칩니다.", "alert": "변형을 생성하려면 옵션을 추가하세요.", "tip": "체크 해제된 변형은 생성되지 않습니다. 나중에 언제든지 변형을 생성하고 편집할 수 있지만 이 목록은 제품 옵션의 변형에 맞춰져 있습니다."}, "productOptions": {"label": "제품 옵션", "hint": "색상, 크기 등과 같은 제품 옵션을 정의합니다."}}, "successToast": "제품 {{title}}이(가) 성공적으로 생성되었습니다."}, "export": {"header": "제품 목록 내보내기", "description": "제품 목록을 CSV 파일로 내보냅니다.", "success": {"title": "내보내기를 처리 중입니다", "description": "데이터 내보내기는 몇 분 정도 소요될 수 있습니다. 완료되면 알려드리겠습니다."}, "filters": {"title": "필터", "description": "테이블 개요에서 필터를 적용하여 이 보기를 조정하세요"}, "columns": {"title": "열", "description": "특정 요구 사항에 맞게 내보낼 데이터를 사용자 지정하세요"}}, "import": {"header": "제품 목록 가져오기", "uploadLabel": "제품 가져오기", "uploadHint": "CSV 파일을 끌어다 놓거나 클릭하여 업로드하세요", "description": "미리 정의된 형식의 CSV 파일을 제공하여 제품을 가져옵니다", "template": {"title": "목록을 어떻게 구성해야 할지 모르시겠나요?", "description": "올바른 형식을 따르기 위해 아래 템플릿을 다운로드하세요."}, "upload": {"title": "CSV 파일 업로드", "description": "가져오기를 통해 제품을 추가하거나 업데이트할 수 있습니다. 기존 제품을 업데이트하려면 기존 핸들과 ID를 사용해야 하며, 기존 변형을 업데이트하려면 기존 ID를 사용해야 합니다. 제품을 가져오기 전에 확인을 요청할 것입니다.", "preprocessing": "전처리 중...", "productsToCreate": "생성될 제품", "productsToUpdate": "업데이트될 제품"}, "success": {"title": "가져오기를 처리 중입니다", "description": "데이터 가져오기는 시간이 걸릴 수 있습니다. 완료되면 알려드리겠습니다."}}, "deleteWarning": "제품 {{title}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "variants": {"header": "변형", "empty": {"heading": "변형 없음", "description": "표시할 변형이 없습니다."}, "filtered": {"heading": "결과 없음", "description": "현재 필터 조건과 일치하는 변형이 없습니다."}}, "attributes": "속성", "editAttributes": "속성 편집", "editOptions": "옵션 편집", "editPrices": "가격 편집", "media": {"label": "미디어", "editHint": "스토어프론트에서 제품을 선보이기 위해 미디어를 추가하세요.", "makeThumbnail": "썸네일로 설정", "uploadImagesLabel": "이미지 업로드", "uploadImagesHint": "이미지를 여기에 끌어다 놓거나 클릭하여 업로드하세요.", "invalidFileType": "'{{name}}'은(는) 지원되지 않는 파일 형식입니다. 지원되는 파일 형식: {{types}}.", "failedToUpload": "추가된 미디어를 업로드하지 못했습니다. 다시 시도해주세요.", "deleteWarning_one": "{{count}}개의 이미지를 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "deleteWarning_other": "{{count}}개의 이미지를 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "deleteWarningWithThumbnail_one": "썸네일을 포함한 {{count}}개의 이미지를 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "deleteWarningWithThumbnail_other": "썸네일을 포함한 {{count}}개의 이미지를 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "thumbnailTooltip": "썸네일", "galleryLabel": "갤러리", "downloadImageLabel": "현재 이미지 다운로드", "deleteImageLabel": "현재 이미지 삭제", "emptyState": {"header": "아직 미디어 없음", "description": "스토어프론트에서 제품을 선보이기 위해 미디어를 추가하세요.", "action": "미디어 추가"}, "successToast": "미디어가 성공적으로 업데이트되었습니다."}, "discountableHint": "체크 해제하면 이 제품에 할인이 적용되지 않습니다.", "noSalesChannels": "어떤 판매 채널에서도 사용할 수 없음", "variantCount_one": "{{count}}개 변형", "variantCount_other": "{{count}}개 변형", "deleteVariantWarning": "변형 {{title}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "productStatus": {"draft": "임시 저장", "published": "게시됨", "proposed": "제안됨", "rejected": "거부됨"}, "fields": {"title": {"label": "제목", "hint": "제품에 간결하고 명확한 제목을 부여하세요.<0/>검색 엔진에 권장되는 길이는 50-60자입니다."}, "subtitle": {"label": "부제목"}, "handle": {"label": "핸들", "tooltip": "핸들은 스토어프론트에서 제품을 참조하는 데 사용됩니다. 지정하지 않으면 제품 제목에서 핸들이 생성됩니다."}, "description": {"label": "설명", "hint": "제품에 간결하고 명확한 설명을 제공하세요.<0/>검색 엔진에 권장되는 길이는 120-160자입니다."}, "discountable": {"label": "할인 가능", "hint": "체크 해제하면 이 제품에 할인이 적용되지 않습니다"}, "shipping_profile": {"label": "배송 프로필", "hint": "제품을 배송 프로필에 연결합니다"}, "type": {"label": "유형"}, "collection": {"label": "컬렉션"}, "categories": {"label": "카테고리"}, "tags": {"label": "태그"}, "sales_channels": {"label": "판매 채널", "hint": "변경하지 않으면 이 제품은 기본 판매 채널에서만 사용할 수 있습니다."}, "countryOrigin": {"label": "원산지 국가"}, "material": {"label": "재질"}, "width": {"label": "너비"}, "length": {"label": "길이"}, "height": {"label": "높이"}, "weight": {"label": "무게"}, "options": {"label": "제품 옵션", "hint": "옵션은 제품의 색상, 크기 등을 정의하는 데 사용됩니다", "add": "옵션 추가", "optionTitle": "옵션 제목", "optionTitlePlaceholder": "색상", "variations": "변형(쉼표로 구분)", "variantionsPlaceholder": "빨강, 파랑, 초록"}, "variants": {"label": "제품 변형", "hint": "체크 해제된 변형은 생성되지 않습니다. 이 순위는 프론트엔드에서 변형이 정렬되는 방식에 영향을 줍니다."}, "mid_code": {"label": "중간 코드"}, "hs_code": {"label": "HS 코드"}}, "variant": {"edit": {"header": "변형 편집", "success": "제품 변형이 성공적으로 편집되었습니다"}, "create": {"header": "변형 상세 정보"}, "deleteWarning": "이 변형을 삭제하시겠습니까?", "pricesPagination": "{{total}}개 중 1 - {{current}}개 가격", "tableItemAvailable": "{{availableCount}}개 사용 가능", "tableItem_one": "{{locationCount}}개 위치에서 {{availableCount}}개 사용 가능", "tableItem_other": "{{locationCount}}개 위치에서 {{availableCount}}개 사용 가능", "inventory": {"notManaged": "관리되지 않음", "manageItems": "재고 항목 관리", "notManagedDesc": "이 변형에 대한 재고가 관리되지 않습니다. 변형의 재고를 추적하려면 '재고 관리'를 활성화하세요.", "manageKit": "재고 키트 관리", "navigateToItem": "재고 항목으로 이동", "actions": {"inventoryItems": "재고 항목으로 이동", "inventoryKit": "재고 항목 보기"}, "inventoryKit": "재고 키트", "inventoryKitHint": "이 변형은 여러 재고 항목으로 구성되어 있습니까?", "validation": {"itemId": "재고 항목을 선택하세요.", "quantity": "수량은 필수입니다. 양수를 입력하세요."}, "header": "재고 및 인벤토리", "editItemDetails": "항목 상세 정보 편집", "manageInventoryLabel": "재고 관리", "manageInventoryHint": "활성화하면 주문 및 반품이 생성될 때 재고 수량을 자동으로 변경합니다.", "allowBackordersLabel": "백오더 허용", "allowBackordersHint": "활성화하면 사용 가능한 수량이 없어도 고객이 변형을 구매할 수 있습니다.", "toast": {"levelsBatch": "재고 수준이 업데이트되었습니다.", "update": "재고 항목이 성공적으로 업데이트되었습니다.", "updateLevel": "재고 수준이 성공적으로 업데이트되었습니다.", "itemsManageSuccess": "재고 항목이 성공적으로 업데이트되었습니다."}}}, "options": {"header": "옵션", "edit": {"header": "옵션 편집", "successToast": "옵션 {{title}}이(가) 성공적으로 업데이트되었습니다."}, "create": {"header": "옵션 생성", "successToast": "옵션 {{title}}이(가) 성공적으로 생성되었습니다."}, "deleteWarning": "제품 옵션: {{title}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다."}, "organization": {"header": "분류", "edit": {"header": "분류 편집", "toasts": {"success": "{{title}}의 분류가 성공적으로 업데이트되었습니다."}}}, "stock": {"heading": "제품 재고 수준 및 위치 관리", "description": "제품의 모든 변형에 대한 재고 수준을 업데이트합니다.", "loading": "잠시만 기다려주세요, 이 작업은 시간이 걸릴 수 있습니다...", "tooltips": {"alreadyManaged": "이 재고 항목은 이미 {{title}} 아래에서 편집할 수 있습니다.", "alreadyManagedWithSku": "이 재고 항목은 이미 {{title}} ({{sku}}) 아래에서 편집할 수 있습니다."}}, "shippingProfile": {"header": "배송 구성", "edit": {"header": "배송 구성", "toasts": {"success": "{{title}}의 배송 프로필이 성공적으로 업데이트되었습니다."}}, "create": {"errors": {"required": "배송 프로필은 필수입니다"}}}, "toasts": {"delete": {"success": {"header": "제품 삭제됨", "description": "{{title}}이(가) 성공적으로 삭제되었습니다."}, "error": {"header": "제품 삭제 실패"}}}}, "collections": {"domain": "컬렉션", "subtitle": "제품을 컬렉션으로 구성합니다.", "createCollection": "컬렉션 생성", "createCollectionHint": "제품을 구성하기 위해 새 컬렉션을 생성합니다.", "createSuccess": "컬렉션이 성공적으로 생성되었습니다.", "editCollection": "컬렉션 편집", "handleTooltip": "핸들은 스토어프론트에서 컬렉션을 참조하는 데 사용됩니다. 지정하지 않으면 컬렉션 제목에서 핸들이 생성됩니다.", "deleteWarning": "컬렉션 {{title}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "removeSingleProductWarning": "컬렉션에서 제품 {{title}}을(를) 제거하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "removeProductsWarning_one": "컬렉션에서 {{count}}개의 제품을 제거하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "removeProductsWarning_other": "컬렉션에서 {{count}}개의 제품을 제거하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "products": {"list": {"noRecordsMessage": "컬렉션에 제품이 없습니다."}, "add": {"successToast_one": "제품이 컬렉션에 성공적으로 추가되었습니다.", "successToast_other": "제품이 컬렉션에 성공적으로 추가되었습니다."}, "remove": {"successToast_one": "제품이 컬렉션에서 성공적으로 제거되었습니다.", "successToast_other": "제품이 컬렉션에서 성공적으로 제거되었습니다."}}}, "categories": {"domain": "카테고리", "subtitle": "제품을 카테고리로 구성하고, 해당 카테고리의 순위와 계층 구조를 관리합니다.", "create": {"header": "카테고리 생성", "hint": "제품을 구성하기 위해 새 카테고리를 생성합니다.", "tabs": {"details": "상세 정보", "organize": "순위 구성"}, "successToast": "카테고리 {{name}}이(가) 성공적으로 생성되었습니다."}, "edit": {"header": "카테고리 편집", "description": "카테고리를 편집하여 세부 정보를 업데이트합니다.", "successToast": "카테고리가 성공적으로 업데이트되었습니다."}, "delete": {"confirmation": "카테고리 {{name}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "successToast": "카테고리 {{name}}이(가) 성공적으로 삭제되었습니다."}, "products": {"add": {"disabledTooltip": "이 제품은 이미 이 카테고리에 있습니다.", "successToast_one": "{{count}}개 제품이 카테고리에 추가되었습니다.", "successToast_other": "{{count}}개 제품이 카테고리에 추가되었습니다."}, "remove": {"confirmation_one": "카테고리에서 {{count}}개 제품을 제거하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "confirmation_other": "카테고리에서 {{count}}개 제품을 제거하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "successToast_one": "{{count}}개 제품이 카테고리에서 제거되었습니다.", "successToast_other": "{{count}}개 제품이 카테고리에서 제거되었습니다."}, "list": {"noRecordsMessage": "카테고리에 제품이 없습니다."}}, "organize": {"header": "구성", "action": "순위 편집"}, "fields": {"visibility": {"label": "가시성", "internal": "내부용", "public": "공개"}, "status": {"label": "상태", "active": "활성", "inactive": "비활성"}, "path": {"label": "경로", "tooltip": "카테고리의 전체 경로를 표시합니다."}, "children": {"label": "하위 항목"}, "new": {"label": "신규"}}}, "inventory": {"domain": "재고", "subtitle": "재고 항목 관리", "reserved": "예약됨", "available": "사용 가능", "locationLevels": "위치", "associatedVariants": "연결된 변형", "manageLocations": "위치 관리", "manageLocationQuantity": "위치 수량 관리", "deleteWarning": "재고 항목을 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "editItemDetails": "항목 상세 정보 편집", "create": {"title": "재고 항목 생성", "details": "상세 정보", "availability": "가용성", "locations": "위치", "attributes": "속성", "requiresShipping": "배송 필요", "requiresShippingHint": "재고 항목이 배송이 필요합니까?", "successToast": "재고 항목이 성공적으로 생성되었습니다."}, "reservation": {"header": "{{itemName}}의 예약", "editItemDetails": "예약 편집", "lineItemId": "라인 항목 ID", "orderID": "주문 ID", "description": "설명", "location": "위치", "inStockAtLocation": "이 위치의 재고", "availableAtLocation": "이 위치에서 사용 가능", "reservedAtLocation": "이 위치에서 예약됨", "reservedAmount": "예약 수량", "create": "예약 생성", "itemToReserve": "예약할 항목", "quantityPlaceholder": "얼마나 예약하시겠습니까?", "descriptionPlaceholder": "이것은 어떤 유형의 예약입니까?", "successToast": "예약이 성공적으로 생성되었습니다.", "updateSuccessToast": "예약이 성공적으로 업데이트되었습니다.", "deleteSuccessToast": "예약이 성공적으로 삭제되었습니다.", "errors": {"noAvaliableQuantity": "재고 위치에 사용 가능한 수량이 없습니다.", "quantityOutOfRange": "최소 수량은 1이고 최대 수량은 {{max}}입니다"}}, "adjustInventory": {"errors": {"stockedQuantity": "재고 수량을 {{quantity}}개의 예약된 수량보다 적게 업데이트할 수 없습니다."}}, "toast": {"updateLocations": "위치가 성공적으로 업데이트되었습니다.", "updateLevel": "재고 수준이 성공적으로 업데이트되었습니다.", "updateItem": "재고 항목이 성공적으로 업데이트되었습니다."}, "stock": {"title": "재고 수준 업데이트", "description": "선택한 재고 항목의 재고 수준을 업데이트합니다.", "action": "재고 수준 편집", "placeholder": "활성화되지 않음", "disablePrompt_one": "{{count}}개의 위치 수준을 비활성화하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "disablePrompt_other": "{{count}}개의 위치 수준을 비활성화하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "disabledToggleTooltip": "비활성화할 수 없음: 비활성화하기 전에 들어오는 수량 및/또는 예약된 수량을 지우세요.", "successToast": "재고 수준이 성공적으로 업데이트되었습니다."}}, "giftCards": {"domain": "기프트 카드", "editGiftCard": "기프트 카드 편집", "createGiftCard": "기프트 카드 생성", "createGiftCardHint": "스토어에서 결제 수단으로 사용할 수 있는 기프트 카드를 수동으로 생성합니다.", "selectRegionFirst": "먼저 지역을 선택하세요", "deleteGiftCardWarning": "기프트 카드 {{code}}를 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "balanceHigherThanValue": "잔액은 원래 금액보다 높을 수 없습니다.", "balanceLowerThanZero": "잔액은 음수가 될 수 없습니다.", "expiryDateHint": "국가마다 기프트 카드 만료일에 관한 법률이 다릅니다. 만료일을 설정하기 전에 현지 규정을 확인하세요.", "regionHint": "기프트 카드의 지역을 변경하면 통화도 변경되어 금전적 가치에 영향을 미칠 수 있습니다.", "enabledHint": "기프트 카드가 활성화되어 있는지 또는 비활성화되어 있는지 지정합니다.", "balance": "잔액", "currentBalance": "현재 잔액", "initialBalance": "초기 잔액", "personalMessage": "개인 메시지", "recipient": "수신자"}, "customers": {"domain": "고객", "list": {"noRecordsMessage": "고객이 이곳에 표시됩니다."}, "create": {"header": "고객 생성", "hint": "새 고객을 생성하고 세부 정보를 관리합니다.", "successToast": "고객 {{email}}이(가) 성공적으로 생성되었습니다."}, "groups": {"label": "고객 그룹", "remove": "고객을 \"{{name}}\" 고객 그룹에서 제거하시겠습니까?", "removeMany": "고객을 다음 고객 그룹에서 제거하시겠습니까: {{groups}}?", "alreadyAddedTooltip": "고객이 이미 이 고객 그룹에 있습니다.", "list": {"noRecordsMessage": "이 고객은 어떤 그룹에도 속하지 않습니다."}, "add": {"success": "고객이 다음에 추가되었습니다: {{groups}}.", "list": {"noRecordsMessage": "먼저 고객 그룹을 생성하세요."}}, "removed": {"success": "고객이 다음에서 제거되었습니다: {{groups}}.", "list": {"noRecordsMessage": "먼저 고객 그룹을 생성하세요."}}}, "edit": {"header": "고객 편집", "emailDisabledTooltip": "등록된 고객의 이메일 주소는 변경할 수 없습니다.", "successToast": "고객 {{email}}이(가) 성공적으로 업데이트되었습니다."}, "delete": {"title": "고객 삭제", "description": "고객 {{email}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "successToast": "고객 {{email}}이(가) 성공적으로 삭제되었습니다."}, "fields": {"guest": "게스트", "registered": "등록됨", "groups": "그룹"}, "registered": "등록됨", "guest": "게스트", "hasAccount": "계정 있음", "addresses": {"title": "주소", "fields": {"addressName": "주소명", "address1": "주소 1", "address2": "주소 2", "city": "도시", "province": "주/도", "postalCode": "우편번호", "country": "국가", "phone": "전화번호", "company": "회사", "countryCode": "국가 코드", "provinceCode": "주/도 코드"}, "create": {"header": "주소 생성", "hint": "고객을 위한 새 주소를 생성합니다.", "successToast": "주소가 성공적으로 생성되었습니다."}}}, "customerGroups": {"domain": "고객 그룹", "subtitle": "고객을 그룹으로 구성합니다. 그룹마다 다른 프로모션과 가격을 가질 수 있습니다.", "list": {"empty": {"heading": "고객 그룹 없음", "description": "표시할 고객 그룹이 없습니다."}, "filtered": {"heading": "결과 없음", "description": "현재 필터 조건과 일치하는 고객 그룹이 없습니다."}}, "create": {"header": "고객 그룹 생성", "hint": "고객을 세분화하기 위한 새 고객 그룹을 생성합니다.", "successToast": "고객 그룹 {{name}}이(가) 성공적으로 생성되었습니다."}, "edit": {"header": "고객 그룹 편집", "successToast": "고객 그룹 {{name}}이(가) 성공적으로 업데이트되었습니다."}, "delete": {"title": "고객 그룹 삭제", "description": "고객 그룹 {{name}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "successToast": "고객 그룹 {{name}}이(가) 성공적으로 삭제되었습니다."}, "customers": {"alreadyAddedTooltip": "고객이 이미 그룹에 추가되었습니다.", "add": {"successToast_one": "고객이 그룹에 성공적으로 추가되었습니다.", "successToast_other": "고객이 그룹에 성공적으로 추가되었습니다.", "list": {"noRecordsMessage": "먼저 고객을 생성하세요."}}, "remove": {"title_one": "고객 제거", "title_other": "고객 제거", "description_one": "고객 그룹에서 {{count}}명의 고객을 제거하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "description_other": "고객 그룹에서 {{count}}명의 고객을 제거하려고 합니다. 이 작업은 되돌릴 수 없습니다."}, "list": {"noRecordsMessage": "이 그룹에는 고객이 없습니다."}}}, "orders": {"domain": "주문", "claim": "클레임", "exchange": "교환", "return": "반품", "cancelWarning": "주문 {{id}}을(를) 취소하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "orderCanceled": "주문이 성공적으로 취소되었습니다", "onDateFromSalesChannel": "{{date}} {{salesChannel}}에서", "list": {"noRecordsMessage": "주문이 이곳에 표시됩니다."}, "status": {"not_paid": "미결제", "pending": "대기 중", "completed": "완료됨", "draft": "임시 저장", "archived": "보관됨", "canceled": "취소됨", "requires_action": "조치 필요"}, "summary": {"requestReturn": "반품 요청", "allocateItems": "항목 할당", "editOrder": "주문 편집", "editOrderContinue": "주문 편집 계속하기", "inventoryKit": "{{count}}개의 재고 항목으로 구성됨", "itemTotal": "상품 합계", "shippingTotal": "배송 합계", "discountTotal": "할인 합계", "taxTotalIncl": "세금 합계(포함)", "itemSubtotal": "상품 소계", "shippingSubtotal": "배송 소계", "discountSubtotal": "할인 소계", "taxTotal": "세금 합계"}, "transfer": {"title": "소유권 이전", "requestSuccess": "주문 이전 요청이 {{email}}에게 전송되었습니다.", "currentOwner": "현재 소유자", "newOwner": "새 소유자", "currentOwnerDescription": "이 주문과 현재 관련된 고객입니다.", "newOwnerDescription": "이 주문을 이전할 고객입니다."}, "payment": {"title": "결제", "isReadyToBeCaptured": "결제 <0/>가 캡처 준비되었습니다.", "totalPaidByCustomer": "고객 결제 총액", "capture": "결제 캡처", "capture_short": "캡처", "refund": "환불", "markAsPaid": "결제 완료로 표시", "statusLabel": "결제 상태", "statusTitle": "결제 상태", "status": {"notPaid": "미결제", "authorized": "승인됨", "partiallyAuthorized": "부분 승인됨", "awaiting": "대기 중", "captured": "캡처됨", "partiallyRefunded": "부분 환불됨", "partiallyCaptured": "부분 캡처됨", "refunded": "환불됨", "canceled": "취소됨", "requiresAction": "조치 필요"}, "capturePayment": "{{amount}}의 결제가 캡처됩니다.", "capturePaymentSuccess": "{{amount}}의 결제가 성공적으로 캡처되었습니다", "markAsPaidPayment": "{{amount}}의 결제가 결제 완료로 표시됩니다.", "markAsPaidPaymentSuccess": "{{amount}}의 결제가 성공적으로 결제 완료로 표시되었습니다", "createRefund": "환불 생성", "refundPaymentSuccess": "{{amount}} 금액의 환불이 성공적으로 처리되었습니다", "createRefundWrongQuantity": "수량은 1에서 {{number}} 사이의 숫자여야 합니다", "refundAmount": "{{ amount }} 환불", "paymentLink": "{{ amount }}에 대한 결제 링크 복사", "selectPaymentToRefund": "환불할 결제 선택"}, "edits": {"title": "주문 편집", "confirm": "편집 확인", "confirmText": "주문 편집을 확인하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "cancel": "편집 취소", "currentItems": "현재 항목", "currentItemsDescription": "항목 수량을 조정하거나 제거합니다.", "addItemsDescription": "주문에 새 항목을 추가할 수 있습니다.", "addItems": "항목 추가", "amountPaid": "결제 금액", "newTotal": "새 합계", "differenceDue": "차액", "create": "주문 편집", "currentTotal": "현재 합계", "noteHint": "편집에 대한 내부 메모 추가", "cancelSuccessToast": "주문 편집이 취소되었습니다", "createSuccessToast": "주문 편집 요청이 생성되었습니다", "activeChangeError": "주문에 이미 활성 주문 변경(반품, 클레임, 교환 등)이 있습니다. 주문을 편집하기 전에 변경을 완료하거나 취소하세요.", "panel": {"title": "주문 편집 요청됨", "titlePending": "주문 편집 대기 중"}, "toast": {"canceledSuccessfully": "주문 편집이 취소되었습니다", "confirmedSuccessfully": "주문 편집이 확인되었습니다"}, "validation": {"quantityLowerThanFulfillment": "수량을 이행된 수량보다 적거나 같게 설정할 수 없습니다"}}, "edit": {"email": {"title": "이메일 편집", "requestSuccess": "주문 이메일이 {{email}}(으)로 업데이트되었습니다."}, "shippingAddress": {"title": "배송 주소 편집", "requestSuccess": "주문 배송 주소가 업데이트되었습니다."}, "billingAddress": {"title": "청구 주소 편집", "requestSuccess": "주문 청구 주소가 업데이트되었습니다."}}, "returns": {"create": "반품 생성", "confirm": "반품 확인", "confirmText": "반품을 확인하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "inbound": "반입", "outbound": "반출", "sendNotification": "알림 전송", "sendNotificationHint": "고객에게 반품에 대해 알립니다.", "returnTotal": "반품 합계", "inboundTotal": "반입 합계", "estDifference": "예상 차액", "outstandingAmount": "미결제 금액", "reason": "사유", "reasonHint": "고객이 항목을 반품하려는 이유를 선택하세요.", "note": "메모", "noInventoryLevel": "재고 수준 없음", "noInventoryLevelDesc": "선택한 위치에는 선택한 항목에 대한 재고 수준이 없습니다. 반품은 요청할 수 있지만 선택한 위치에 대한 재고 수준이 생성될 때까지 수령할 수 없습니다.", "noteHint": "무언가를 지정하고 싶다면 자유롭게 입력할 수 있습니다.", "location": "위치", "locationHint": "항목을 반품할 위치를 선택하세요.", "inboundShipping": "반품 배송", "inboundShippingHint": "사용할 방법을 선택하세요.", "returnableQuantityLabel": "반품 가능 수량", "refundableAmountLabel": "환불 가능 금액", "returnRequestedInfo": "{{requestedItemsCount}}개 항목 반품 요청됨", "returnReceivedInfo": "{{requestedItemsCount}}개 항목 반품 수령됨", "itemReceived": "항목 수령됨", "returnRequested": "반품 요청됨", "damagedItemReceived": "손상된 항목 수령됨", "damagedItemsReturned": "{{quantity}}개 손상된 항목 반품됨", "activeChangeError": "이 주문에 진행 중인 활성 주문 변경이 있습니다. 먼저 변경을 완료하거나 취소하세요.", "cancel": {"title": "반품 취소", "description": "반품 요청을 취소하시겠습니까?"}, "placeholders": {"noReturnShippingOptions": {"title": "반품 배송 옵션을 찾을 수 없음", "hint": "위치에 대한 반품 배송 옵션이 생성되지 않았습니다. <LinkComponent>위치 및 배송</LinkComponent>에서 생성할 수 있습니다."}, "outboundShippingOptions": {"title": "반출 배송 옵션을 찾을 수 없음", "hint": "위치에 대한 반출 배송 옵션이 생성되지 않았습니다. <LinkComponent>위치 및 배송</LinkComponent>에서 생성할 수 있습니다."}}, "receive": {"action": "항목 수령", "receiveItems": "{{ returnType }} {{ id }}", "restockAll": "모든 항목 재입고", "itemsLabel": "수령된 항목", "title": "#{{returnId}}에 대한 항목 수령", "sendNotificationHint": "고객에게 수령된 반품에 대해 알립니다.", "inventoryWarning": "위의 입력에 따라 재고 수준이 자동으로 조정됩니다.", "writeOffInputLabel": "항목 중 몇 개가 손상되었습니까?", "toast": {"success": "반품이 성공적으로 수령되었습니다.", "errorLargeValue": "수량이 요청된 항목 수량보다 큽니다.", "errorNegativeValue": "수량은 음수가 될 수 없습니다.", "errorLargeDamagedValue": "손상된 항목 수량 + 손상되지 않은 수령 수량이 반품 항목의 총 수량을 초과합니다. 손상되지 않은 항목의 수량을 줄여주세요."}}, "toast": {"canceledSuccessfully": "반품이 성공적으로 취소되었습니다", "confirmedSuccessfully": "반품이 성공적으로 확인되었습니다"}, "panel": {"title": "반품 시작됨", "description": "완료해야 할 미처리 반품 요청이 있습니다"}}, "claims": {"create": "클레임 생성", "confirm": "클레임 확인", "confirmText": "클레임을 확인하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "manage": "클레임 관리", "outbound": "반출", "outboundItemAdded": "{{itemsCount}}개 클레임을 통해 추가됨", "outboundTotal": "반출 합계", "outboundShipping": "반출 배송", "outboundShippingHint": "사용할 방법을 선택하세요.", "refundAmount": "예상 차액", "activeChangeError": "이 주문에 활성 주문 변경이 있습니다. 이전 변경을 완료하거나 취소하세요.", "actions": {"cancelClaim": {"successToast": "클레임이 성공적으로 취소되었습니다."}}, "cancel": {"title": "클레임 취소", "description": "클레임을 취소하시겠습니까?"}, "tooltips": {"onlyReturnShippingOptions": "이 목록은 반품 배송 옵션만 포함합니다."}, "toast": {"canceledSuccessfully": "클레임이 성공적으로 취소되었습니다", "confirmedSuccessfully": "클레임이 성공적으로 확인되었습니다"}, "panel": {"title": "클레임 시작됨", "description": "완료해야 할 미처리 클레임 요청이 있습니다"}}, "exchanges": {"create": "교환 생성", "manage": "교환 관리", "confirm": "교환 확인", "confirmText": "교환을 확인하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "outbound": "반출", "outboundItemAdded": "{{itemsCount}}개 교환을 통해 추가됨", "outboundTotal": "반출 합계", "outboundShipping": "반출 배송", "outboundShippingHint": "사용할 방법을 선택하세요.", "refundAmount": "예상 차액", "activeChangeError": "이 주문에 활성 주문 변경이 있습니다. 이전 변경을 완료하거나 취소하세요.", "actions": {"cancelExchange": {"successToast": "교환이 성공적으로 취소되었습니다."}}, "cancel": {"title": "교환 취소", "description": "교환을 취소하시겠습니까?"}, "tooltips": {"onlyReturnShippingOptions": "이 목록은 반품 배송 옵션만 포함합니다."}, "toast": {"canceledSuccessfully": "교환이 성공적으로 취소되었습니다", "confirmedSuccessfully": "교환이 성공적으로 확인되었습니다"}, "panel": {"title": "교환 시작됨", "description": "완료해야 할 미처리 교환 요청이 있습니다"}}, "reservations": {"allocatedLabel": "할당됨", "notAllocatedLabel": "할당되지 않음"}, "allocateItems": {"action": "항목 할당", "title": "주문 항목 할당", "locationDescription": "할당할 위치를 선택하세요.", "itemsToAllocate": "할당할 항목", "itemsToAllocateDesc": "할당하려는 항목 수를 선택하세요", "search": "항목 검색", "consistsOf": "{{num}}개의 재고 항목으로 구성됨", "requires": "변형당 {{num}}개 필요", "toast": {"created": "항목이 성공적으로 할당되었습니다"}, "error": {"quantityNotAllocated": "할당되지 않은 항목이 있습니다."}}, "shipment": {"title": "이행을 배송 완료로 표시", "trackingNumber": "추적 번호", "addTracking": "추적 번호 추가", "sendNotification": "알림 전송", "sendNotificationHint": "이 배송에 대해 고객에게 알립니다.", "toastCreated": "배송이 성공적으로 생성되었습니다."}, "fulfillment": {"cancelWarning": "이행을 취소하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "markAsDeliveredWarning": "이행을 배송 완료로 표시하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "differentOptionSelected": "선택한 배송 옵션이 고객이 선택한 것과 다릅니다.", "disabledItemTooltip": "선택한 배송 옵션은 이 항목의 이행을 허용하지 않습니다", "unfulfilledItems": "미이행 항목", "statusLabel": "이행 상태", "statusTitle": "이행 상태", "fulfillItems": "항목 이행", "awaitingFulfillmentBadge": "이행 대기 중", "requiresShipping": "배송 필요", "number": "이행 #{{number}}", "itemsToFulfill": "이행할 항목", "create": "이행 생성", "available": "사용 가능", "inStock": "재고 있음", "markAsShipped": "배송 완료로 표시", "markAsPickedUp": "픽업 완료로 표시", "markAsDelivered": "배송 완료로 표시", "itemsToFulfillDesc": "이행할 항목과 수량을 선택하세요", "locationDescription": "항목을 이행할 위치를 선택하세요.", "sendNotificationHint": "생성된 이행에 대해 고객에게 알립니다.", "methodDescription": "고객이 선택한 것과 다른 배송 방법을 선택하세요", "error": {"wrongQuantity": "이행 가능한 항목은 하나뿐입니다", "wrongQuantity_other": "수량은 1에서 {{number}} 사이의 숫자여야 합니다", "noItems": "이행할 항목이 없습니다.", "noShippingOption": "배송 옵션은 필수입니다", "noLocation": "위치는 필수입니다"}, "status": {"notFulfilled": "미이행", "partiallyFulfilled": "부분 이행됨", "fulfilled": "이행됨", "partiallyShipped": "부분 배송됨", "shipped": "배송됨", "delivered": "배송 완료됨", "partiallyDelivered": "부분 배송 완료됨", "partiallyReturned": "부분 반품됨", "returned": "반품됨", "canceled": "취소됨", "requiresAction": "조치 필요"}, "toast": {"created": "이행이 성공적으로 생성되었습니다", "canceled": "이행이 성공적으로 취소되었습니다", "fulfillmentShipped": "이미 배송된 이행을 취소할 수 없습니다", "fulfillmentDelivered": "이행이 성공적으로 배송 완료로 표시되었습니다", "fulfillmentPickedUp": "이행이 성공적으로 픽업 완료로 표시되었습니다"}, "trackingLabel": "추적", "shippingFromLabel": "출발지", "itemsLabel": "항목"}, "refund": {"title": "환불 생성", "sendNotificationHint": "생성된 환불에 대해 고객에게 알립니다.", "systemPayment": "시스템 결제", "systemPaymentDesc": "결제 중 하나 이상이 시스템 결제입니다. 이러한 결제의 경우 캡처 및 환불은 Medusa에서 처리되지 않습니다.", "error": {"amountToLarge": "원래 주문 금액보다 더 많이 환불할 수 없습니다.", "amountNegative": "환불 금액은 양수여야 합니다.", "reasonRequired": "환불 사유를 선택하세요."}}, "customer": {"contactLabel": "연락처", "editEmail": "이메일 편집", "transferOwnership": "소유권 이전", "editBillingAddress": "청구 주소 편집", "editShippingAddress": "배송 주소 편집"}, "activity": {"header": "활동", "showMoreActivities_one": "{{count}}개 더 활동 보기", "showMoreActivities_other": "{{count}}개 더 활동 보기", "comment": {"label": "댓글", "placeholder": "댓글 남기기", "addButtonText": "댓글 추가", "deleteButtonText": "댓글 삭제"}, "from": "보낸 사람", "to": "받는 사람", "events": {"common": {"toReturn": "반품할", "toSend": "보낼"}, "placed": {"title": "주문 접수됨", "fromSalesChannel": "{{salesChannel}}에서"}, "canceled": {"title": "주문 취소됨"}, "payment": {"awaiting": "결제 대기 중", "captured": "결제 캡처됨", "canceled": "결제 취소됨", "refunded": "결제 환불됨"}, "fulfillment": {"created": "항목 이행됨", "canceled": "이행 취소됨", "shipped": "항목 배송됨", "delivered": "항목 배송 완료됨", "items_one": "{{count}}개 항목", "items_other": "{{count}}개 항목"}, "return": {"created": "반품 #{{returnId}} 요청됨", "canceled": "반품 #{{returnId}} 취소됨", "received": "반품 #{{returnId}} 수령됨", "items_one": "{{count}}개 항목 반품됨", "items_other": "{{count}}개 항목 반품됨"}, "note": {"comment": "댓글", "byLine": "{{author}} 작성"}, "claim": {"created": "클레임 #{{claimId}} 요청됨", "canceled": "클레임 #{{claimId}} 취소됨", "itemsInbound": "{{count}}개 항목 반품할", "itemsOutbound": "{{count}}개 항목 보낼"}, "exchange": {"created": "교환 #{{exchangeId}} 요청됨", "canceled": "교환 #{{exchangeId}} 취소됨", "itemsInbound": "{{count}}개 항목 반품할", "itemsOutbound": "{{count}}개 항목 보낼"}, "edit": {"requested": "주문 편집 #{{editId}} 요청됨", "confirmed": "주문 편집 #{{editId}} 확인됨"}, "transfer": {"requested": "주문 이전 #{{transferId}} 요청됨", "confirmed": "주문 이전 #{{transferId}} 확인됨", "declined": "주문 이전 #{{transferId}} 거부됨"}, "update_order": {"shipping_address": "배송 주소 업데이트됨", "billing_address": "청구 주소 업데이트됨", "email": "이메일 업데이트됨"}}}, "fields": {"displayId": "표시 ID", "refundableAmount": "환불 가능 금액", "returnableQuantity": "반품 가능 수량"}}, "draftOrders": {"domain": "임시 주문", "deleteWarning": "임시 주문 {{id}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "paymentLinkLabel": "결제 링크", "cartIdLabel": "장바구니 ID", "markAsPaid": {"label": "결제 완료로 표시", "warningTitle": "결제 완료로 표시", "warningDescription": "임시 주문을 결제 완료로 표시하려고 합니다. 이 작업은 되돌릴 수 없으며, 나중에 결제 수금이 불가능합니다."}, "status": {"open": "열림", "completed": "완료됨"}, "create": {"createDraftOrder": "임시 주문 생성", "createDraftOrderHint": "주문이 접수되기 전에 주문의 세부 사항을 관리하기 위한 새 임시 주문을 생성합니다.", "chooseRegionHint": "지역 선택", "existingItemsLabel": "기존 항목", "existingItemsHint": "임시 주문에 기존 제품을 추가합니다.", "customItemsLabel": "사용자 정의 항목", "customItemsHint": "임시 주문에 사용자 정의 항목을 추가합니다.", "addExistingItemsAction": "기존 항목 추가", "addCustomItemAction": "사용자 정의 항목 추가", "noCustomItemsAddedLabel": "아직 사용자 정의 항목이 추가되지 않았습니다", "noExistingItemsAddedLabel": "아직 기존 항목이 추가되지 않았습니다", "chooseRegionTooltip": "먼저 지역을 선택하세요", "useExistingCustomerLabel": "기존 고객 사용", "addShippingMethodsAction": "배송 방법 추가", "unitPriceOverrideLabel": "단가 재정의", "shippingOptionLabel": "배송 옵션", "shippingOptionHint": "임시 주문의 배송 옵션을 선택하세요.", "shippingPriceOverrideLabel": "배송 가격 재정의", "shippingPriceOverrideHint": "임시 주문의 배송 가격을 재정의합니다.", "sendNotificationLabel": "알림 전송", "sendNotificationHint": "임시 주문이 생성되면 고객에게 알림을 전송합니다."}, "validation": {"requiredEmailOrCustomer": "이메일 또는 고객은 필수입니다.", "requiredItems": "최소 하나의 항목이 필요합니다.", "invalidEmail": "이메일은 유효한 이메일 주소여야 합니다."}}, "stockLocations": {"domain": "위치 및 배송", "list": {"description": "스토어의 재고 위치 및 배송 옵션을 관리합니다."}, "create": {"header": "재고 위치 생성", "hint": "재고 위치는 제품이 보관되고 배송되는 물리적 장소입니다.", "successToast": "위치 {{name}}이(가) 성공적으로 생성되었습니다."}, "edit": {"header": "재고 위치 편집", "viewInventory": "재고 보기", "successToast": "위치 {{name}}이(가) 성공적으로 업데이트되었습니다."}, "delete": {"confirmation": "재고 위치 \"{{name}}\"을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다."}, "fulfillmentProviders": {"header": "이행 제공자", "shippingOptionsTooltip": "이 드롭다운은 이 위치에 대해 활성화된 제공자만 포함합니다. 드롭다운이 비활성화된 경우 위치에 제공자를 추가하세요.", "label": "연결된 이행 제공자", "connectedTo": "{{total}}개 중 {{count}}개의 이행 제공자에 연결됨", "noProviders": "이 재고 위치는 어떤 이행 제공자에도 연결되어 있지 않습니다.", "action": "제공자 연결", "successToast": "재고 위치에 대한 이행 제공자가 성공적으로 업데이트되었습니다."}, "fulfillmentSets": {"pickup": {"header": "픽업"}, "shipping": {"header": "배송"}, "disable": {"confirmation": "\"{{name}}\"을(를) 비활성화하시겠습니까? 이 작업은 모든 관련 서비스 영역 및 배송 옵션을 삭제하며 되돌릴 수 없습니다.", "pickup": "픽업이 성공적으로 비활성화되었습니다.", "shipping": "배송이 성공적으로 비활성화되었습니다."}, "enable": {"pickup": "픽업이 성공적으로 활성화되었습니다.", "shipping": "배송이 성공적으로 활성화되었습니다."}}, "sidebar": {"header": "배송 구성", "shippingProfiles": {"label": "배송 프로필", "description": "배송 요구 사항별로 제품을 그룹화"}}, "salesChannels": {"header": "판매 채널", "hint": "이 위치에 연결된 판매 채널을 관리합니다.", "label": "연결된 판매 채널", "connectedTo": "{{total}}개 중 {{count}}개의 판매 채널에 연결됨", "noChannels": "이 위치는 어떤 판매 채널에도 연결되어 있지 않습니다.", "action": "판매 채널 연결", "successToast": "판매 채널이 성공적으로 업데이트되었습니다."}, "pickupOptions": {"edit": {"header": "픽업 옵션 편집"}}, "shippingOptions": {"create": {"shipping": {"header": "{{zone}}에 대한 배송 옵션 생성", "hint": "이 위치에서 제품이 어떻게 배송되는지 정의하기 위한 새 배송 옵션을 생성합니다.", "label": "배송 옵션", "successToast": "배송 옵션 {{name}}이(가) 성공적으로 생성되었습니다."}, "pickup": {"header": "{{zone}}에 대한 픽업 옵션 생성", "hint": "이 위치에서 제품이 어떻게 픽업되는지 정의하기 위한 새 픽업 옵션을 생성합니다.", "label": "픽업 옵션", "successToast": "픽업 옵션 {{name}}이(가) 성공적으로 생성되었습니다."}, "returns": {"header": "{{zone}}에 대한 반품 옵션 생성", "hint": "이 위치로 제품이 어떻게 반품되는지 정의하기 위한 새 반품 옵션을 생성합니다.", "label": "반품 옵션", "successToast": "반품 옵션 {{name}}이(가) 성공적으로 생성되었습니다."}, "tabs": {"details": "상세 정보", "prices": "가격"}, "action": "옵션 생성"}, "delete": {"confirmation": "배송 옵션 \"{{name}}\"을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "successToast": "배송 옵션 {{name}}이(가) 성공적으로 삭제되었습니다."}, "edit": {"header": "배송 옵션 편집", "action": "옵션 편집", "successToast": "배송 옵션 {{name}}이(가) 성공적으로 업데이트되었습니다."}, "pricing": {"action": "가격 편집"}, "conditionalPrices": {"header": "{{name}}에 대한 조건부 가격", "description": "장바구니 항목 합계에 따라 이 배송 옵션에 대한 조건부 가격을 관리합니다.", "attributes": {"cartItemTotal": "장바구니 항목 합계"}, "summaries": {"range": "<0>{{attribute}}</0>이(가) <1>{{gte}}</1>에서 <2>{{lte}}</2> 사이인 경우", "greaterThan": "<0>{{attribute}}</0> ≥ <1>{{gte}}</1>인 경우", "lessThan": "<0>{{attribute}}</0> ≤ <1>{{lte}}</1>인 경우"}, "actions": {"addPrice": "가격 추가", "manageConditionalPrices": "조건부 가격 관리"}, "rules": {"amount": "배송 옵션 가격", "gte": "최소 장바구니 항목 합계", "lte": "최대 장바구니 항목 합계"}, "customRules": {"label": "사용자 정의 규칙", "tooltip": "이 조건부 가격은 대시보드에서 관리할 수 없는 규칙이 있습니다.", "eq": "장바구니 항목 합계가 다음과 같아야 함", "gt": "장바구니 항목 합계가 다음보다 커야 함", "lt": "장바구니 항목 합계가 다음보다 작아야 함"}, "errors": {"amountRequired": "배송 옵션 가격은 필수입니다", "minOrMaxRequired": "최소 또는 최대 장바구니 항목 합계 중 하나 이상은 제공되어야 합니다", "minGreaterThanMax": "최소 장바구니 항목 합계는 최대 장바구니 항목 합계보다 작거나 같아야 합니다", "duplicateAmount": "배송 옵션 가격은 각 조건에 대해 고유해야 합니다", "overlappingConditions": "조건은 모든 가격 규칙에서 고유해야 합니다"}}, "fields": {"count": {"shipping_one": "{{count}}개 배송 옵션", "shipping_other": "{{count}}개 배송 옵션", "pickup_one": "{{count}}개 픽업 옵션", "pickup_other": "{{count}}개 픽업 옵션", "returns_one": "{{count}}개 반품 옵션", "returns_other": "{{count}}개 반품 옵션"}, "priceType": {"label": "가격 유형", "options": {"fixed": {"label": "고정", "hint": "배송 옵션의 가격은 고정되어 있으며 주문 내용에 따라 변경되지 않습니다."}, "calculated": {"label": "계산됨", "hint": "배송 옵션의 가격은 결제 중에 이행 제공자에 의해 계산됩니다."}}}, "enableInStore": {"label": "스토어에서 활성화", "hint": "고객이 결제 중에 이 옵션을 사용할 수 있는지 여부입니다."}, "provider": "이행 제공자", "profile": "배송 프로필", "fulfillmentOption": "이행 옵션"}}, "serviceZones": {"create": {"headerPickup": "{{location}}에서 픽업을 위한 서비스 영역 생성", "headerShipping": "{{location}}에서 배송을 위한 서비스 영역 생성", "action": "서비스 영역 생성", "successToast": "서비스 영역 {{name}}이(가) 성공적으로 생성되었습니다."}, "edit": {"header": "서비스 영역 편집", "successToast": "서비스 영역 {{name}}이(가) 성공적으로 업데이트되었습니다."}, "delete": {"confirmation": "서비스 영역 \"{{name}}\"을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "successToast": "서비스 영역 {{name}}이(가) 성공적으로 삭제되었습니다."}, "manageAreas": {"header": "{{name}}에 대한 지역 관리", "action": "지역 관리", "label": "지역", "hint": "서비스 영역이 적용되는 지리적 지역을 선택하세요.", "successToast": "{{name}}에 대한 지역이 성공적으로 업데이트되었습니다."}, "fields": {"noRecords": "배송 옵션을 추가할 서비스 영역이 없습니다.", "tip": "서비스 영역은 지리적 영역 또는 지역의 모음입니다. 사용 가능한 배송 옵션을 정의된 위치 집합으로 제한하는 데 사용됩니다."}}}, "shippingProfile": {"domain": "배송 프로필", "subtitle": "유사한 배송 요구 사항을 가진 제품을 프로필로 그룹화합니다.", "create": {"header": "배송 프로필 생성", "hint": "유사한 배송 요구 사항을 가진 제품을 그룹화하기 위한 새 배송 프로필을 생성합니다.", "successToast": "배송 프로필 {{name}}이(가) 성공적으로 생성되었습니다."}, "delete": {"title": "배송 프로필 삭제", "description": "배송 프로필 {{name}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "successToast": "배송 프로필 {{name}}이(가) 성공적으로 삭제되었습니다."}, "tooltip": {"type": "배송 프로필 유형을 입력하세요. 예: 무거운, 대형, 화물 전용 등."}}, "taxRegions": {"domain": "세금 지역", "list": {"hint": "고객이 다른 국가 및 지역에서 쇼핑할 때 부과하는 세금을 관리합니다."}, "delete": {"confirmation": "세금 지역을 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "successToast": "세금 지역이 성공적으로, 삭제되었습니다."}, "create": {"header": "세금 지역 생성", "hint": "특정 국가에 대한 세율을 정의하기 위한 새 세금 지역을 생성합니다.", "errors": {"rateIsRequired": "기본 세율 생성 시 세율은 필수입니다.", "nameIsRequired": "기본 세율 생성 시 이름은 필수입니다."}, "successToast": "세금 지역이 성공적으로 생성되었습니다."}, "province": {"header": "도/주", "create": {"header": "도/주 세금 지역 생성", "hint": "특정 도/주에 대한 세율을 정의하기 위한 새 세금 지역을 생성합니다."}}, "state": {"header": "주", "create": {"header": "주 세금 지역 생성", "hint": "특정 주에 대한 세율을 정의하기 위한 새 세금 지역을 생성합니다."}}, "stateOrTerritory": {"header": "주 또는 준주", "create": {"header": "주/준주 세금 지역 생성", "hint": "특정 주/준주에 대한 세율을 정의하기 위한 새 세금 지역을 생성합니다."}}, "county": {"header": "군/구", "create": {"header": "군/구 세금 지역 생성", "hint": "특정 군/구에 대한 세율을 정의하기 위한 새 세금 지역을 생성합니다."}}, "region": {"header": "지역", "create": {"header": "지역 세금 지역 생성", "hint": "특정 지역에 대한 세율을 정의하기 위한 새 세금 지역을 생성합니다."}}, "department": {"header": "데파르트망", "create": {"header": "데파르트망 세금 지역 생성", "hint": "특정 데파르트망에 대한 세율을 정의하기 위한 새 세금 지역을 생성합니다."}}, "territory": {"header": "준주", "create": {"header": "준주 세금 지역 생성", "hint": "특정 준주에 대한 세율을 정의하기 위한 새 세금 지역을 생성합니다."}}, "prefecture": {"header": "도/현", "create": {"header": "도/현 세금 지역 생성", "hint": "특정 도/현에 대한 세율을 정의하기 위한 새 세금 지역을 생성합니다."}}, "district": {"header": "구역", "create": {"header": "구역 세금 지역 생성", "hint": "특정 구역에 대한 세율을 정의하기 위한 새 세금 지역을 생성합니다."}}, "governorate": {"header": "주/도", "create": {"header": "주/도 세금 지역 생성", "hint": "특정 주/도에 대한 세율을 정의하기 위한 새 세금 지역을 생성합니다."}}, "canton": {"header": "칸톤", "create": {"header": "칸톤 세금 지역 생성", "hint": "특정 칸톤에 대한 세율을 정의하기 위한 새 세금 지역을 생성합니다."}}, "emirate": {"header": "에미리트", "create": {"header": "에미리트 세금 지역 생성", "hint": "특정 에미리트에 대한 세율을 정의하기 위한 새 세금 지역을 생성합니다."}}, "sublevel": {"header": "하위 수준", "create": {"header": "하위 수준 세금 지역 생성", "hint": "특정 하위 수준에 대한 세율을 정의하기 위한 새 세금 지역을 생성합니다."}}, "taxOverrides": {"header": "재정의", "create": {"header": "재정의 생성", "hint": "선택한 조건에 대해 기본 세율을 재정의하는 세율을 생성합니다."}, "edit": {"header": "재정의 편집", "hint": "선택한 조건에 대해 기본 세율을 재정의하는 세율을 편집합니다."}}, "taxRates": {"create": {"header": "세율 생성", "hint": "지역에 대한 세율을 정의하기 위한 새 세율을 생성합니다.", "successToast": "세율이 성공적으로 생성되었습니다."}, "edit": {"header": "세율 편집", "hint": "지역에 대한 세율을 정의하기 위해 세율을 편집합니다.", "successToast": "세율이 성공적으로 업데이트되었습니다."}, "delete": {"confirmation": "세율 \"{{name}}\"을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "successToast": "세율이 성공적으로 삭제되었습니다."}}, "fields": {"isCombinable": {"label": "결합 가능", "hint": "이 세율이 세금 지역의 기본 세율과 결합될 수 있는지 여부입니다.", "true": "결합 가능", "false": "결합 불가능"}, "defaultTaxRate": {"label": "기본 세율", "tooltip": "이 지역의 기본 세율입니다. 예를 들어, 국가 또는 지역의 표준 부가가치세율입니다.", "action": "기본 세율 생성"}, "taxRate": "세율", "taxCode": "세금 코드", "targets": {"label": "대상", "hint": "이 세율이 적용될 대상을 선택하세요.", "options": {"product": "제품", "productCollection": "제품 컬렉션", "productTag": "제품 태그", "productType": "제품 유형", "customerGroup": "고객 그룹"}, "operators": {"in": "포함", "on": "대상", "and": "그리고"}, "placeholders": {"product": "제품 검색", "productCollection": "제품 컬렉션 검색", "productTag": "제품 태그 검색", "productType": "제품 유형 검색", "customerGroup": "고객 그룹 검색"}, "tags": {"product": "제품", "productCollection": "제품 컬렉션", "productTag": "제품 태그", "productType": "제품 유형", "customerGroup": "고객 그룹"}, "modal": {"header": "대상 추가"}, "values_one": "{{count}}개 값", "values_other": "{{count}}개 값", "numberOfTargets_one": "{{count}}개 대상", "numberOfTargets_other": "{{count}}개 대상", "additionalValues_one": "및 {{count}}개 추가 값", "additionalValues_other": "및 {{count}}개 추가 값", "action": "대상 추가"}, "sublevels": {"labels": {"province": "도/주", "state": "주", "region": "지역", "stateOrTerritory": "주/준주", "department": "데파르트망", "county": "군/구", "territory": "준주", "prefecture": "도/현", "district": "구역", "governorate": "주/도", "emirate": "에미리트", "canton": "칸톤", "sublevel": "하위 수준 코드"}, "placeholders": {"province": "도/주 선택", "state": "주 선택", "region": "지역 선택", "stateOrTerritory": "주/준주 선택", "department": "데파르트망 선택", "county": "군/구 선택", "territory": "준주 선택", "prefecture": "도/현 선택", "district": "구역 선택", "governorate": "주/도 선택", "emirate": "에미리트 선택", "canton": "칸톤 선택"}, "tooltips": {"sublevel": "하위 수준 세금 지역의 ISO 3166-2 코드를 입력하세요.", "notPartOfCountry": "{{province}}은(는) {{country}}의 일부가 아닌 것 같습니다. 이것이 맞는지 다시 확인하세요."}, "alert": {"header": "이 세금 지역에 대한 하위 수준 지역이 비활성화되어 있습니다", "description": "이 지역에 대한 하위 수준 지역은 기본적으로 비활성화되어 있습니다. 도/주, 주 또는 준주와 같은 하위 수준 지역을 생성하려면 활성화할 수 있습니다.", "action": "하위 수준 지역 활성화"}}, "noDefaultRate": {"label": "기본 세율 없음", "tooltip": "이 세금 지역에는 기본 세율이 없습니다. 국가의 부가가치세와 같은 표준 세율이 있다면 이 지역에 추가하세요."}}}, "promotions": {"domain": "프로모션", "sections": {"details": "프로모션 상세 정보"}, "tabs": {"template": "유형", "details": "상세 정보", "campaign": "캠페인"}, "fields": {"type": "유형", "value_type": "값 유형", "value": "값", "campaign": "캠페인", "method": "방법", "allocation": "할당", "addCondition": "조건 추가", "clearAll": "모두 지우기", "amount": {"tooltip": "금액 설정을 활성화하려면 통화 코드를 선택하세요"}, "conditions": {"rules": {"title": "누가 이 코드를 사용할 수 있나요?", "description": "어떤 고객이 프로모션 코드를 사용할 수 있나요? 변경하지 않으면 모든 고객이 프로모션 코드를 사용할 수 있습니다."}, "target-rules": {"title": "어떤 항목에 프로모션이 적용되나요?", "description": "프로모션은 다음 조건과 일치하는 항목에 적용됩니다."}, "buy-rules": {"title": "프로모션을 활성화하기 위해 장바구니에 무엇이 필요한가요?", "description": "이러한 조건이 일치하면 대상 항목에 프로모션을 활성화합니다."}}}, "tooltips": {"campaignType": "지출 예산을 설정하려면 프로모션에서 통화 코드를 선택해야 합니다."}, "errors": {"requiredField": "필수 필드", "promotionTabError": "계속하기 전에 프로모션 탭의 오류를 수정하세요"}, "toasts": {"promotionCreateSuccess": "프로모션 ({{code}})이(가) 성공적으로 생성되었습니다."}, "create": {}, "edit": {"title": "프로모션 상세 정보 편집", "rules": {"title": "사용 조건 편집"}, "target-rules": {"title": "항목 조건 편집"}, "buy-rules": {"title": "구매 규칙 편집"}}, "campaign": {"header": "캠페인", "edit": {"header": "캠페인 편집", "successToast": "프로모션의 캠페인이 성공적으로 업데이트되었습니다."}, "actions": {"goToCampaign": "캠페인으로 이동"}}, "campaign_currency": {"tooltip": "이것은 프로모션의 통화입니다. 상세 정보 탭에서 변경하세요."}, "form": {"required": "필수", "and": "그리고", "selectAttribute": "속성 선택", "campaign": {"existing": {"title": "기존 캠페인", "description": "기존 캠페인에 프로모션을 추가합니다.", "placeholder": {"title": "기존 캠페인 없음", "desc": "여러 프로모션을 추적하고 예산 제한을 설정하기 위해 하나를 생성할 수 있습니다."}}, "new": {"title": "새 캠페인", "description": "이 프로모션을 위한 새 캠페인을 생성합니다."}, "none": {"title": "캠페인 없음", "description": "프로모션을 캠페인과 연결하지 않고 진행합니다"}}, "status": {"label": "상태", "draft": {"title": "임시 저장", "description": "고객은 아직 코드를 사용할 수 없습니다"}, "active": {"title": "활성", "description": "고객이 코드를 사용할 수 있습니다"}, "inactive": {"title": "비활성", "description": "고객은 더 이상 코드를 사용할 수 없습니다"}}, "method": {"label": "방법", "code": {"title": "프로모션 코드", "description": "고객은 결제 시 이 코드를 입력해야 합니다"}, "automatic": {"title": "자동", "description": "고객은 결제 시 이 프로모션을 볼 수 있습니다"}}, "max_quantity": {"title": "최대 수량", "description": "이 프로모션이 적용되는 항목의 최대 수량입니다."}, "type": {"standard": {"title": "표준", "description": "표준 프로모션"}, "buyget": {"title": "구매 시 증정", "description": "X를 구매하면 Y를 받는 프로모션"}}, "allocation": {"each": {"title": "각각", "description": "각 항목에 값 적용"}, "across": {"title": "전체", "description": "항목 전체에 값 적용"}}, "code": {"title": "코드", "description": "고객이 결제 중에 입력할 코드입니다."}, "value": {"title": "프로모션 값"}, "value_type": {"fixed": {"title": "고정 금액", "description": "할인될 금액입니다. 예: 100"}, "percentage": {"title": "퍼센트", "description": "금액에서 할인할 퍼센트입니다. 예: 8%"}}}, "deleteWarning": "프로모션 {{code}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "createPromotionTitle": "프로모션 생성", "type": "프로모션 유형", "conditions": {"add": "조건 추가", "list": {"noRecordsMessage": "프로모션이 적용되는 항목을 제한하기 위한 조건을 추가하세요."}}}, "campaigns": {"domain": "캠페인", "details": "캠페인 상세 정보", "status": {"active": "활성", "expired": "만료됨", "scheduled": "예약됨"}, "delete": {"title": "확실합니까?", "description": "캠페인 '{{name}}'을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "successToast": "캠페인 '{{name}}'이(가) 성공적으로 생성되었습니다."}, "edit": {"header": "캠페인 편집", "description": "캠페인의 상세 정보를 편집합니다.", "successToast": "캠페인 '{{name}}'이(가) 성공적으로 업데이트되었습니다."}, "configuration": {"header": "구성", "edit": {"header": "캠페인 구성 편집", "description": "캠페인의 구성을 편집합니다.", "successToast": "캠페인 구성이 성공적으로 업데이트되었습니다."}}, "create": {"title": "캠페인 생성", "description": "프로모션 캠페인을 생성합니다.", "hint": "프로모션 캠페인을 생성합니다.", "header": "캠페인 생성", "successToast": "캠페인 '{{name}}'이(가) 성공적으로 생성되었습니다."}, "fields": {"name": "이름", "identifier": "식별자", "start_date": "시작일", "end_date": "종료일", "total_spend": "사용된 예산", "total_used": "사용된 예산", "budget_limit": "예산 한도", "campaign_id": {"hint": "프로모션과 동일한 통화 코드를 가진 캠페인만 이 목록에 표시됩니다."}}, "budget": {"create": {"hint": "캠페인의 예산을 생성합니다.", "header": "캠페인 예산"}, "details": "캠페인 예산", "fields": {"type": "유형", "currency": "통화", "limit": "한도", "used": "사용됨"}, "type": {"spend": {"title": "지출", "description": "모든 프로모션 사용의 총 할인 금액에 제한을 설정합니다."}, "usage": {"title": "사용량", "description": "프로모션이 사용될 수 있는 횟수에 제한을 설정합니다."}}, "edit": {"header": "캠페인 예산 편집"}}, "promotions": {"remove": {"title": "캠페인에서 프로모션 제거", "description": "캠페인에서 {{count}}개의 프로모션을 제거하려고 합니다. 이 작업은 되돌릴 수 없습니다."}, "alreadyAdded": "이 프로모션은 이미 캠페인에 추가되었습니다.", "alreadyAddedDiffCampaign": "이 프로모션은 이미 다른 캠페인({{name}})에 추가되었습니다.", "currencyMismatch": "프로모션과 캠페인의 통화가 일치하지 않습니다", "toast": {"success": "{{count}}개의 프로모션을 캠페인에 성공적으로 추가했습니다"}, "add": {"list": {"noRecordsMessage": "먼저 프로모션을 생성하세요."}}, "list": {"noRecordsMessage": "캠페인에 프로모션이 없습니다."}}, "deleteCampaignWarning": "캠페인 {{name}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "가격 목록", "subtitle": "특정 조건에 대한 할인 또는 가격 재정의를 생성합니다.", "delete": {"confirmation": "가격 목록 \"{{title}}\"을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "successToast": "가격 목록 \"{{title}}\"이(가) 성공적으로 삭제되었습니다."}, "create": {"header": "가격 목록 생성", "subheader": "제품 가격을 관리하기 위한 새 가격 목록을 생성합니다.", "tabs": {"details": "상세 정보", "products": "제품", "prices": "가격"}, "successToast": "가격 목록 {{title}}이(가) 성공적으로 생성되었습니다.", "products": {"list": {"noRecordsMessage": "먼저 제품을 생성하세요."}}}, "edit": {"header": "가격 목록 편집", "successToast": "가격 목록 {{title}}이(가) 성공적으로 업데이트되었습니다."}, "configuration": {"header": "구성", "edit": {"header": "가격 목록 구성 편집", "description": "가격 목록의 구성을 편집합니다.", "successToast": "가격 목록 구성이 성공적으로 업데이트되었습니다."}}, "products": {"header": "제품", "actions": {"addProducts": "제품 추가", "editPrices": "가격 편집"}, "delete": {"confirmation_one": "가격 목록에서 {{count}}개 제품의 가격을 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "confirmation_other": "가격 목록에서 {{count}}개 제품의 가격을 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "successToast_one": "{{count}}개 제품의 가격이 성공적으로 삭제되었습니다.", "successToast_other": "{{count}}개 제품의 가격이 성공적으로 삭제되었습니다."}, "add": {"successToast": "가격이 가격 목록에 성공적으로 추가되었습니다."}, "edit": {"successToast": "가격이 성공적으로 업데이트되었습니다."}}, "fields": {"priceOverrides": {"label": "가격 재정의", "header": "가격 재정의"}, "status": {"label": "상태", "options": {"active": "활성", "draft": "임시 저장", "expired": "만료됨", "scheduled": "예약됨"}}, "type": {"label": "유형", "hint": "생성하려는 가격 목록 유형을 선택하세요.", "options": {"sale": {"label": "할인", "description": "할인 가격은 제품의 일시적인 가격 변경입니다."}, "override": {"label": "재정의", "description": "재정의는 주로 고객별 가격을 생성하는 데 사용됩니다."}}}, "startsAt": {"label": "가격 목록에 시작일이 있습니까?", "hint": "미래에 활성화될 가격 목록을 예약합니다."}, "endsAt": {"label": "가격 목록에 만료일이 있습니까?", "hint": "미래에 비활성화될 가격 목록을 예약합니다."}, "customerAvailability": {"header": "고객 그룹 선택", "label": "고객 가용성", "hint": "가격 목록이 적용될 고객 그룹을 선택하세요.", "placeholder": "고객 그룹 검색", "attribute": "고객 그룹"}}}, "profile": {"domain": "프로필", "manageYourProfileDetails": "프로필 상세 정보를 관리합니다.", "fields": {"languageLabel": "언어", "usageInsightsLabel": "사용 인사이트"}, "edit": {"header": "프로필 편집", "languageHint": "관리자 대시보드에서 사용할 언어입니다. 이는 스토어의 언어를 변경하지 않습니다.", "languagePlaceholder": "언어 선택", "usageInsightsHint": "사용 인사이트를 공유하고 Medusa를 개선하는 데 도움을 주세요. 우리가 수집하는 내용과 그 사용 방법에 대한 자세한 내용은 <0>문서</0>에서 확인할 수 있습니다."}, "toast": {"edit": "프로필 변경 사항이 저장되었습니다"}}, "users": {"domain": "사용자", "editUser": "사용자 편집", "inviteUser": "사용자 초대", "inviteUserHint": "새 사용자를 스토어에 초대합니다.", "sendInvite": "초대장 보내기", "pendingInvites": "대기 중인 초대", "deleteInviteWarning": "{{email}}에 대한 초대를 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "resendInvite": "초대장 재전송", "copyInviteLink": "초대 링크 복사", "expiredOnDate": "{{date}}에 만료됨", "validFromUntil": "<0>{{from}}</0>부터 <1>{{until}}</1>까지 유효", "acceptedOnDate": "{{date}}에 수락됨", "inviteStatus": {"accepted": "수락됨", "pending": "대기 중", "expired": "만료됨"}, "roles": {"admin": "관리자", "developer": "개발자", "member": "멤버"}, "list": {"empty": {"heading": "사용자를 찾을 수 없음", "description": "사용자가 초대되면 여기에 표시됩니다."}, "filtered": {"heading": "결과 없음", "description": "현재 필터 조건과 일치하는 사용자가 없습니다."}}, "deleteUserWarning": "사용자 {{name}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "deleteUserSuccess": "사용자 {{name}}이(가) 성공적으로 삭제되었습니다", "invite": "초대"}, "store": {"domain": "스토어", "manageYourStoresDetails": "스토어의 상세 정보를 관리합니다", "editStore": "스토어 편집", "defaultCurrency": "기본 통화", "defaultRegion": "기본 지역", "defaultSalesChannel": "기본 판매 채널", "defaultLocation": "기본 위치", "swapLinkTemplate": "교환 링크 템플릿", "paymentLinkTemplate": "결제 링크 템플릿", "inviteLinkTemplate": "초대 링크 템플릿", "currencies": "통화", "addCurrencies": "통화 추가", "enableTaxInclusivePricing": "세금 포함 가격 활성화", "disableTaxInclusivePricing": "세금 포함 가격 비활성화", "removeCurrencyWarning_one": "스토어에서 {{count}}개의 통화를 제거하려고 합니다. 계속하기 전에 해당 통화를 사용하는 모든 가격을 제거했는지 확인하세요.", "removeCurrencyWarning_other": "스토어에서 {{count}}개의 통화를 제거하려고 합니다. 계속하기 전에 해당 통화를 사용하는 모든 가격을 제거했는지 확인하세요.", "currencyAlreadyAdded": "이 통화는 이미 스토어에 추가되었습니다.", "edit": {"header": "스토어 편집"}, "toast": {"update": "스토어가 성공적으로 업데이트되었습니다", "currenciesUpdated": "통화가 성공적으로 업데이트되었습니다", "currenciesRemoved": "스토어에서 통화가 성공적으로 제거되었습니다", "updatedTaxInclusivitySuccessfully": "세금 포함 가격이 성공적으로 업데이트되었습니다"}}, "regions": {"domain": "지역", "subtitle": "지역은 제품을 판매하는 영역입니다. 여러 국가를 포함할 수 있으며, 다른 세율, 제공자 및 통화를 가질 수 있습니다.", "createRegion": "지역 생성", "createRegionHint": "일련의 국가에 대한 세율 및 제공자를 관리합니다.", "addCountries": "국가 추가", "editRegion": "지역 편집", "countriesHint": "이 지역에 포함된 국가를 추가합니다.", "deleteRegionWarning": "지역 {{name}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "removeCountriesWarning_one": "지역에서 {{count}}개의 국가를 제거하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "removeCountriesWarning_other": "지역에서 {{count}}개의 국가를 제거하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "removeCountryWarning": "지역에서 국가 {{name}}을(를) 제거하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "automaticTaxesHint": "활성화되면 세금은 배송 주소에 따라 결제 시에만 계산됩니다.", "taxInclusiveHint": "활성화되면 지역의 가격에 세금이 포함됩니다.", "providersHint": "이 지역에서 사용할 수 있는 결제 제공자를 추가합니다.", "shippingOptions": "배송 옵션", "deleteShippingOptionWarning": "배송 옵션 {{name}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "return": "반품", "outbound": "반출", "priceType": "가격 유형", "flatRate": "정액제", "calculated": "계산됨", "list": {"noRecordsMessage": "판매 지역에 대한 지역을 생성하세요."}, "toast": {"delete": "지역이 성공적으로 삭제되었습니다", "edit": "지역 편집이 저장되었습니다", "create": "지역이 성공적으로 생성되었습니다", "countries": "지역 국가가 성공적으로 업데이트되었습니다"}, "shippingOption": {"createShippingOption": "배송 옵션 생성", "createShippingOptionHint": "지역에 대한 새 배송 옵션을 생성합니다.", "editShippingOption": "배송 옵션 편집", "fulfillmentMethod": "이행 방법", "type": {"outbound": "반출", "outboundHint": "고객에게 제품을 보내기 위한 배송 옵션을 생성하는 경우 이 옵션을 사용하세요.", "return": "반품", "returnHint": "고객이 제품을 반품하기 위한 배송 옵션을 생성하는 경우 이 옵션을 사용하세요."}, "priceType": {"label": "가격 유형", "flatRate": "정액제", "calculated": "계산됨"}, "availability": {"adminOnly": "관리자 전용", "adminOnlyHint": "활성화하면 배송 옵션은 관리자 대시보드에서만 사용 가능하며 스토어프론트에서는 사용할 수 없습니다."}, "taxInclusiveHint": "활성화하면 배송 옵션의 가격에 세금이 포함됩니다.", "requirements": {"label": "요구 사항", "hint": "배송 옵션에 대한 요구 사항을 지정합니다."}}}, "taxes": {"domain": "세금 지역", "domainDescription": "세금 지역 관리", "countries": {"taxCountriesHint": "세금 설정은 나열된 국가에 적용됩니다."}, "settings": {"editTaxSettings": "세금 설정 편집", "taxProviderLabel": "세금 제공자", "systemTaxProviderLabel": "시스템 세금 제공자", "calculateTaxesAutomaticallyLabel": "자동으로 세금 계산", "calculateTaxesAutomaticallyHint": "활성화하면 세율이 자동으로 계산되어 장바구니에 적용됩니다. 비활성화하면 결제 시 세금을 수동으로 계산해야 합니다. 수동 세금은 타사 세금 제공자와 함께 사용하는 것이 좋습니다.", "applyTaxesOnGiftCardsLabel": "기프트 카드에 세금 적용", "applyTaxesOnGiftCardsHint": "활성화하면 결제 시 기프트 카드에 세금이 적용됩니다. 일부 국가에서는 세금 규정상 구매 시 기프트 카드에 세금을 적용해야 합니다.", "defaultTaxRateLabel": "기본 세율", "defaultTaxCodeLabel": "기본 세금 코드"}, "defaultRate": {"sectionTitle": "기본 세율"}, "taxRate": {"sectionTitle": "세율", "createTaxRate": "세율 생성", "createTaxRateHint": "지역에 대한 새 세율을 생성합니다.", "deleteRateDescription": "세율 {{name}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "editTaxRate": "세율 편집", "editRateAction": "세율 편집", "editOverridesAction": "재정의 편집", "editOverridesTitle": "세율 재정의 편집", "editOverridesHint": "세율에 대한 재정의를 지정합니다.", "deleteTaxRateWarning": "세율 {{name}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "productOverridesLabel": "제품 재정의", "productOverridesHint": "세율에 대한 제품 재정의를 지정합니다.", "addProductOverridesAction": "제품 재정의 추가", "productTypeOverridesLabel": "제품 유형 재정의", "productTypeOverridesHint": "세율에 대한 제품 유형 재정의를 지정합니다.", "addProductTypeOverridesAction": "제품 유형 재정의 추가", "shippingOptionOverridesLabel": "배송 옵션 재정의", "shippingOptionOverridesHint": "세율에 대한 배송 옵션 재정의를 지정합니다.", "addShippingOptionOverridesAction": "배송 옵션 재정의 추가", "productOverridesHeader": "제품", "productTypeOverridesHeader": "제품 유형", "shippingOptionOverridesHeader": "배송 옵션"}}, "locations": {"domain": "위치", "editLocation": "위치 편집", "addSalesChannels": "판매 채널 추가", "noLocationsFound": "위치를 찾을 수 없음", "selectLocations": "항목을 보관하는 위치를 선택하세요.", "deleteLocationWarning": "위치 {{name}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "removeSalesChannelsWarning_one": "위치에서 {{count}}개의 판매 채널을 제거하려고 합니다.", "removeSalesChannelsWarning_other": "위치에서 {{count}}개의 판매 채널을 제거하려고 합니다.", "toast": {"create": "위치가 성공적으로 생성되었습니다", "update": "위치가 성공적으로 업데이트되었습니다", "removeChannel": "판매 채널이 성공적으로 제거되었습니다"}}, "reservations": {"domain": "예약", "subtitle": "재고 항목의 예약 수량을 관리합니다.", "deleteWarning": "예약을 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다."}, "salesChannels": {"domain": "판매 채널", "subtitle": "제품을 판매하는 온라인 및 오프라인 채널을 관리합니다.", "list": {"empty": {"heading": "판매 채널을 찾을 수 없음", "description": "판매 채널이 생성되면 여기에 표시됩니다."}, "filtered": {"heading": "결과 없음", "description": "현재 필터 조건과 일치하는 판매 채널이 없습니다."}}, "createSalesChannel": "판매 채널 생성", "createSalesChannelHint": "제품을 판매할 새 판매 채널을 생성합니다.", "enabledHint": "판매 채널의 활성화 여부를 지정합니다.", "removeProductsWarning_one": "{{sales_channel}}에서 {{count}}개의 제품을 제거하려고 합니다.", "removeProductsWarning_other": "{{sales_channel}}에서 {{count}}개의 제품을 제거하려고 합니다.", "addProducts": "제품 추가", "editSalesChannel": "판매 채널 편집", "productAlreadyAdded": "이 제품은 이미 판매 채널에 추가되었습니다.", "deleteSalesChannelWarning": "판매 채널 {{name}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "toast": {"create": "판매 채널이 성공적으로 생성되었습니다", "update": "판매 채널이 성공적으로 업데이트되었습니다", "delete": "판매 채널이 성공적으로 삭제되었습니다"}, "tooltip": {"cannotDeleteDefault": "기본 판매 채널은 삭제할 수 없습니다"}, "products": {"list": {"noRecordsMessage": "판매 채널에 제품이 없습니다."}, "add": {"list": {"noRecordsMessage": "먼저 제품을 생성하세요."}}}}, "apiKeyManagement": {"domain": {"publishable": "공개 API 키", "secret": "비밀 API 키"}, "subtitle": {"publishable": "스토어프론트에서 요청 범위를 특정 판매 채널로 제한하는 데 사용되는 API 키를 관리합니다.", "secret": "관리자 애플리케이션에서 관리자 사용자를 인증하는 데 사용되는 API 키를 관리합니다."}, "status": {"active": "활성", "revoked": "취소됨"}, "type": {"publishable": "공개", "secret": "비밀"}, "create": {"createPublishableHeader": "공개 API 키 생성", "createPublishableHint": "요청 범위를 특정 판매 채널로 제한하기 위한 새 공개 API 키를 생성합니다.", "createSecretHeader": "비밀 API 키 생성", "createSecretHint": "인증된 관리자 사용자로 Medusa API에 접근하기 위한 새 비밀 API 키를 생성합니다.", "secretKeyCreatedHeader": "비밀 키 생성됨", "secretKeyCreatedHint": "새 비밀 키가 생성되었습니다. 지금 복사하여 안전하게 보관하세요. 이 키는 한 번만 표시됩니다.", "copySecretTokenSuccess": "비밀 키가 클립보드에 복사되었습니다.", "copySecretTokenFailure": "비밀 키를 클립보드에 복사하지 못했습니다.", "successToast": "API 키가 성공적으로 생성되었습니다."}, "edit": {"header": "API 키 편집", "description": "API 키의 제목을 편집합니다.", "successToast": "API 키 {{title}}이(가) 성공적으로 업데이트되었습니다."}, "salesChannels": {"title": "판매 채널 추가", "description": "API 키가 제한되어야 하는 판매 채널을 추가합니다.", "successToast_one": "{{count}}개의 판매 채널이 API 키에 성공적으로 추가되었습니다.", "successToast_other": "{{count}}개의 판매 채널이 API 키에 성공적으로 추가되었습니다.", "alreadyAddedTooltip": "이 판매 채널은 이미 API 키에 추가되었습니다.", "list": {"noRecordsMessage": "공개 API 키의 범위에 판매 채널이 없습니다."}}, "delete": {"warning": "API 키 {{title}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "successToast": "API 키 {{title}}이(가) 성공적으로 삭제되었습니다."}, "revoke": {"warning": "API 키 {{title}}을(를) 취소하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "successToast": "API 키 {{title}}이(가) 성공적으로 취소되었습니다."}, "addSalesChannels": {"list": {"noRecordsMessage": "먼저 판매 채널을 생성하세요."}}, "removeSalesChannel": {"warning": "API 키에서 판매 채널 {{name}}을(를) 제거하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "warningBatch_one": "API 키에서 {{count}}개의 판매 채널을 제거하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "warningBatch_other": "API 키에서 {{count}}개의 판매 채널을 제거하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "successToast": "판매 채널이 API 키에서 성공적으로 제거되었습니다.", "successToastBatch_one": "{{count}}개의 판매 채널이 API 키에서 성공적으로 제거되었습니다.", "successToastBatch_other": "{{count}}개의 판매 채널이 API 키에서 성공적으로 제거되었습니다."}, "actions": {"revoke": "API 키 취소", "copy": "API 키 복사", "copySuccessToast": "API 키가 클립보드에 복사되었습니다."}, "table": {"lastUsedAtHeader": "마지막 사용 시간", "createdAtHeader": "취소 시간"}, "fields": {"lastUsedAtLabel": "마지막 사용 시간", "revokedByLabel": "취소자", "revokedAtLabel": "취소 시간", "createdByLabel": "생성자"}}, "returnReasons": {"domain": "반품 사유", "subtitle": "반품된 항목에 대한 사유를 관리합니다.", "calloutHint": "반품을 분류하기 위한 사유를 관리합니다.", "editReason": "반품 사유 편집", "create": {"header": "반품 사유 추가", "subtitle": "가장 일반적인 반품 사유를 지정합니다.", "hint": "반품을 분류하기 위한 새 반품 사유를 생성합니다.", "successToast": "반품 사유 {{label}}이(가) 성공적으로 생성되었습니다."}, "edit": {"header": "반품 사유 편집", "subtitle": "반품 사유의 값을 편집합니다.", "successToast": "반품 사유 {{label}}이(가) 성공적으로 업데이트되었습니다."}, "delete": {"confirmation": "반품 사유 \"{{label}}\"을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "successToast": "반품 사유 \"{{label}}\"이(가) 성공적으로 삭제되었습니다."}, "fields": {"value": {"label": "값", "placeholder": "wrong_size", "tooltip": "값은 반품 사유에 대한 고유 식별자여야 합니다."}, "label": {"label": "라벨", "placeholder": "잘못된 사이즈"}, "description": {"label": "설명", "placeholder": "고객이 잘못된 사이즈를 받았습니다"}}}, "login": {"forgotPassword": "비밀번호를 잊으셨나요? - <0>재설정</0>", "title": "Medusa에 오신 것을 환영합니다", "hint": "계정 영역에 접근하려면 로그인하세요"}, "invite": {"title": "Medusa에 오신 것을 환영합니다", "hint": "아래에서 계정을 생성하세요", "backToLogin": "로그인으로 돌아가기", "createAccount": "계정 생성", "alreadyHaveAccount": "이미 계정이 있으신가요? - <0>로그인</0>", "emailTooltip": "이메일은 변경할 수 없습니다. 다른 이메일을 사용하고 싶다면 새 초대장을 보내야 합니다.", "invalidInvite": "초대장이 유효하지 않거나 만료되었습니다.", "successTitle": "계정이 등록되었습니다", "successHint": "지금 바로 Medusa 관리자를 시작하세요.", "successAction": "Medusa 관리자 시작", "invalidTokenTitle": "초대 토큰이 유효하지 않습니다", "invalidTokenHint": "새 초대 링크를 요청해 보세요.", "passwordMismatch": "비밀번호가 일치하지 않습니다", "toast": {"accepted": "초대가 성공적으로 수락되었습니다"}}, "resetPassword": {"title": "비밀번호 재설정", "hint": "아래에 이메일을 입력하면 비밀번호 재설정 방법에 대한 안내를 보내드립니다.", "email": "이메일", "sendResetInstructions": "재설정 안내 보내기", "backToLogin": "<0>로그인으로 돌아가기</0>", "newPasswordHint": "아래에서 새 비밀번호를 선택하세요.", "invalidTokenTitle": "재설정 토큰이 유효하지 않습니다", "invalidTokenHint": "새 재설정 링크를 요청해 보세요.", "expiredTokenTitle": "재설정 토큰이 만료되었습니다", "goToResetPassword": "비밀번호 재설정으로 이동", "resetPassword": "비밀번호 재설정", "newPassword": "새 비밀번호", "repeatNewPassword": "새 비밀번호 확인", "tokenExpiresIn": "토큰이 <0>{{time}}</0>분 후에 만료됩니다", "successfulRequestTitle": "이메일을 성공적으로 보냈습니다", "successfulRequest": "비밀번호를 재설정할 수 있는 이메일을 보냈습니다. 몇 분 후에도 받지 못했다면 스팸 폴더를 확인해 보세요.", "successfulResetTitle": "비밀번호 재설정 성공", "successfulReset": "로그인 페이지에서 로그인하세요.", "passwordMismatch": "비밀번호가 일치하지 않습니다", "invalidLinkTitle": "재설정 링크가 유효하지 않습니다", "invalidLinkHint": "다시 비밀번호 재설정을 시도해 보세요."}, "workflowExecutions": {"domain": "워크플로우", "subtitle": "Medusa 애플리케이션의 워크플로우 실행을 보고 추적합니다.", "transactionIdLabel": "트랜잭션 ID", "workflowIdLabel": "워크플로우 ID", "progressLabel": "진행 상황", "stepsCompletedLabel_one": "{{count}}개 중 {{completed}}개 단계", "stepsCompletedLabel_other": "{{count}}개 중 {{completed}}개 단계", "list": {"noRecordsMessage": "아직 실행된 워크플로우가 없습니다."}, "history": {"sectionTitle": "기록", "runningState": "실행 중...", "awaitingState": "대기 중", "failedState": "실패", "skippedState": "건너뜀", "skippedFailureState": "건너뜀(실패)", "definitionLabel": "정의", "outputLabel": "출력", "compensateInputLabel": "보상 입력", "revertedLabel": "되돌림", "errorLabel": "오류"}, "state": {"done": "완료", "failed": "실패", "reverted": "되돌림", "invoking": "호출 중", "compensating": "보상 중", "notStarted": "시작되지 않음"}, "transaction": {"state": {"waitingToCompensate": "보상 대기 중"}}, "step": {"state": {"skipped": "건너뜀", "skippedFailure": "건너뜀(실패)", "dormant": "휴면", "timeout": "시간 초과"}}}, "productTypes": {"domain": "제품 유형", "subtitle": "제품을 유형별로 구성합니다.", "create": {"header": "제품 유형 생성", "hint": "제품을 분류하기 위한 새 제품 유형을 생성합니다.", "successToast": "제품 유형 {{value}}이(가) 성공적으로 생성되었습니다."}, "edit": {"header": "제품 유형 편집", "successToast": "제품 유형 {{value}}이(가) 성공적으로 업데이트되었습니다."}, "delete": {"confirmation": "제품 유형 \"{{value}}\"을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "successToast": "제품 유형 \"{{value}}\"이(가) 성공적으로 삭제되었습니다."}, "fields": {"value": "값"}}, "productTags": {"domain": "제품 태그", "create": {"header": "제품 태그 생성", "subtitle": "제품을 분류하기 위한 새 제품 태그를 생성합니다.", "successToast": "제품 태그 {{value}}이(가) 성공적으로 생성되었습니다."}, "edit": {"header": "제품 태그 편집", "subtitle": "제품 태그의 값을 편집합니다.", "successToast": "제품 태그 {{value}}이(가) 성공적으로 업데이트되었습니다."}, "delete": {"confirmation": "제품 태그 {{value}}을(를) 삭제하려고 합니다. 이 작업은 되돌릴 수 없습니다.", "successToast": "제품 태그 {{value}}이(가) 성공적으로 삭제되었습니다."}, "fields": {"value": "값"}}, "notifications": {"domain": "알림", "emptyState": {"title": "알림 없음", "description": "현재 알림이 없습니다. 알림이 생기면 여기에 표시됩니다."}, "accessibility": {"description": "Medusa 활동에 대한 알림이 여기에 나열됩니다."}}, "errors": {"serverError": "서버 오류 - 나중에 다시 시도하세요.", "invalidCredentials": "이메일 또는 비밀번호가 잘못되었습니다"}, "statuses": {"scheduled": "예약됨", "expired": "만료됨", "active": "활성", "inactive": "비활성", "draft": "임시 저장", "enabled": "활성화됨", "disabled": "비활성화됨"}, "labels": {"productVariant": "제품 변형", "prices": "가격", "available": "사용 가능", "inStock": "재고 있음", "added": "추가됨", "removed": "제거됨", "from": "시작", "to": "종료", "beaware": "주의", "loading": "로딩 중"}, "fields": {"amount": "금액", "refundAmount": "환불 금액", "name": "이름", "default": "기본", "lastName": "성", "firstName": "이름", "title": "제목", "customTitle": "사용자 지정 제목", "manageInventory": "재고 관리", "inventoryKit": "재고 키트 있음", "inventoryItems": "재고 항목", "inventoryItem": "재고 항목", "requiredQuantity": "필요 수량", "description": "설명", "email": "이메일", "password": "비밀번호", "repeatPassword": "비밀번호 확인", "confirmPassword": "비밀번호 확인", "newPassword": "새 비밀번호", "repeatNewPassword": "새 비밀번호 확인", "categories": "카테고리", "shippingMethod": "배송 방법", "configurations": "구성", "conditions": "조건", "category": "카테고리", "collection": "컬렉션", "discountable": "할인 가능", "handle": "핸들", "subtitle": "부제목", "by": "작성자", "item": "항목", "qty": "수량", "limit": "제한", "tags": "태그", "type": "유형", "reason": "사유", "none": "없음", "all": "전체", "search": "검색", "percentage": "퍼센트", "sales_channels": "판매 채널", "customer_groups": "고객 그룹", "product_tags": "제품 태그", "product_types": "제품 유형", "product_collections": "제품 컬렉션", "status": "상태", "code": "코드", "value": "값", "disabled": "비활성화됨", "dynamic": "동적", "normal": "일반", "years": "년", "months": "월", "days": "일", "hours": "시간", "minutes": "분", "totalRedemptions": "총 사용 횟수", "countries": "국가", "paymentProviders": "결제 제공자", "refundReason": "환불 사유", "fulfillmentProviders": "이행 제공자", "fulfillmentProvider": "이행 제공자", "providers": "제공자", "availability": "가용성", "inventory": "재고", "optional": "선택 사항", "note": "메모", "automaticTaxes": "자동 세금", "taxInclusivePricing": "세금 포함 가격", "currency": "통화", "address": "주소", "address2": "아파트, 호수 등", "city": "도시", "postalCode": "우편번호", "country": "국가", "state": "주", "province": "도/주", "company": "회사", "phone": "전화번호", "metadata": "메타데이터", "selectCountry": "국가 선택", "products": "제품", "variants": "변형", "orders": "주문", "account": "계정", "total": "주문 합계", "paidTotal": "캡처된 합계", "totalExclTax": "세금 제외 합계", "subtotal": "소계", "shipping": "배송", "outboundShipping": "반출 배송", "returnShipping": "반품 배송", "tax": "세금", "created": "생성됨", "key": "키", "customer": "고객", "date": "날짜", "order": "주문", "fulfillment": "이행", "provider": "제공자", "payment": "결제", "items": "항목", "salesChannel": "판매 채널", "region": "지역", "discount": "할인", "role": "역할", "sent": "전송됨", "salesChannels": "판매 채널", "product": "제품", "createdAt": "생성일", "updatedAt": "업데이트일", "revokedAt": "취소일", "true": "참", "false": "거짓", "giftCard": "기프트 카드", "tag": "태그", "dateIssued": "발행일", "issuedDate": "발행일", "expiryDate": "만료일", "price": "가격", "priceTemplate": "가격 {{regionOrCurrency}}", "height": "높이", "width": "너비", "length": "길이", "weight": "무게", "midCode": "MID 코드", "hsCode": "HS 코드", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "재고 수량", "barcode": "바코드", "countryOfOrigin": "원산지 국가", "material": "재질", "thumbnail": "썸네일", "sku": "SKU", "managedInventory": "관리된 재고", "allowBackorder": "백오더 허용", "inStock": "재고 있음", "location": "위치", "quantity": "수량", "variant": "변형", "id": "ID", "parent": "상위", "minSubtotal": "최소 소계", "maxSubtotal": "최대 소계", "shippingProfile": "배송 프로필", "summary": "요약", "details": "상세 정보", "label": "라벨", "rate": "비율", "requiresShipping": "배송 필요", "unitPrice": "단가", "startDate": "시작일", "endDate": "종료일", "draft": "임시 저장", "values": "값"}, "quotes": {"domain": "견적", "title": "견적", "subtitle": "고객 견적 및 제안 관리", "noQuotes": "견적을 찾을 수 없습니다", "noQuotesDescription": "현재 견적이 없습니다. 스토어프론트에서 생성하세요.", "table": {"id": "견적 ID", "customer": "고객", "status": "상태", "company": "회사", "amount": "금액", "createdAt": "생성일", "updatedAt": "업데이트일", "actions": "작업"}, "status": {"pending_merchant": "판매자 대기 중", "pending_customer": "고객 대기 중", "merchant_rejected": "판매자 거부", "customer_rejected": "고객 거부", "accepted": "승인됨", "unknown": "알 수 없음"}, "actions": {"sendQuote": "견적 보내기", "rejectQuote": "견적 거부", "viewOrder": "주문 보기"}, "details": {"header": "견적 상세", "quoteSummary": "견적 요약", "customer": "고객", "company": "회사", "items": "항목", "total": "합계", "subtotal": "소계", "shipping": "배송비", "tax": "세금", "discounts": "할인", "originalTotal": "원래 합계", "quoteTotal": "견적 합계", "messages": "메시지", "actions": "작업", "sendMessage": "메시지 보내기", "send": "보내기", "pickQuoteItem": "견적 항목 선택", "selectQuoteItem": "댓글을 달 견적 항목을 선택하세요", "selectItem": "항목 선택", "manage": "관리", "phone": "전화번호", "spendingLimit": "지출 한도", "name": "이름", "manageQuote": "견적 관리", "noItems": "이 견적에 항목이 없습니다", "noMessages": "이 견적에 메시지가 없습니다"}, "items": {"title": "제품", "quantity": "수량", "unitPrice": "단가", "total": "합계"}, "messages": {"admin": "관리자", "customer": "고객", "placeholder": "여기에 메시지를 입력하세요..."}, "filters": {"status": "상태별 필터"}, "confirmations": {"sendTitle": "견적 보내기", "sendDescription": "이 견적을 고객에게 보내시겠습니까?", "rejectTitle": "견적 거부", "rejectDescription": "이 견적을 거부하시겠습니까?"}, "acceptance": {"message": "견적이 승인되었습니다"}, "toasts": {"sendSuccess": "고객에게 견적을 성공적으로 보냈습니다", "sendError": "견적 보내기에 실패했습니다", "rejectSuccess": "고객의 견적을 성공적으로 거부했습니다", "rejectError": "견적 거부에 실패했습니다", "messageSuccess": "고객에게 메시지를 성공적으로 보냈습니다", "messageError": "메시지 보내기에 실패했습니다", "updateSuccess": "견적이 성공적으로 업데이트되었습니다"}, "manage": {"overridePriceHint": "이 항목의 원래 가격을 재정의합니다", "updatePrice": "가격 업데이트"}}, "companies": {"domain": "회사", "title": "회사", "subtitle": "비즈니스 관계 관리", "noCompanies": "회사를 찾을 수 없습니다", "noCompaniesDescription": "시작하려면 첫 번째 회사를 생성하세요.", "notFound": "회사를 찾을 수 없습니다", "table": {"name": "이름", "phone": "전화번호", "email": "이메일", "address": "주소", "employees": "직원", "customerGroup": "고객 그룹", "actions": "작업"}, "fields": {"name": "회사명", "email": "이메일", "phone": "전화번호", "website": "웹사이트", "address": "주소", "city": "도시", "state": "주/도", "zip": "우편번호", "zipCode": "우편번호", "country": "국가", "currency": "통화", "logoUrl": "로고 URL", "description": "설명", "employees": "직원", "customerGroup": "고객 그룹", "approvalSettings": "승인 설정"}, "placeholders": {"name": "회사명을 입력하세요", "email": "이메일 주소를 입력하세요", "phone": "전화번호를 입력하세요", "website": "웹사이트 URL을 입력하세요", "address": "주소를 입력하세요", "city": "도시를 입력하세요", "state": "주/도를 입력하세요", "zip": "우편번호를 입력하세요", "logoUrl": "로고 URL을 입력하세요", "description": "회사 설명을 입력하세요", "selectCountry": "국가 선택", "selectCurrency": "통화 선택"}, "validation": {"nameRequired": "회사명은 필수입니다", "emailRequired": "이메일은 필수입니다", "emailInvalid": "유효하지 않은 이메일 주소입니다", "addressRequired": "주소는 필수입니다", "cityRequired": "도시는 필수입니다", "stateRequired": "주/도는 필수입니다", "zipRequired": "우편번호는 필수입니다"}, "create": {"title": "회사 생성", "description": "비즈니스 관계를 관리하기 위한 새 회사를 생성합니다.", "submit": "회사 생성"}, "edit": {"title": "회사 편집", "submit": "회사 업데이트"}, "details": {"actions": "작업"}, "approvals": {"requiresAdminApproval": "관리자 승인 필요", "requiresSalesManagerApproval": "영업 관리자 승인 필요", "noApprovalRequired": "승인 불필요"}, "deleteWarning": "이렇게 하면 회사와 모든 관련 데이터가 영구적으로 삭제됩니다.", "approvalSettings": {"title": "승인 설정", "requiresAdminApproval": "관리자 승인 필요", "requiresSalesManagerApproval": "영업 관리자 승인 필요", "requiresAdminApprovalDesc": "이 회사의 주문은 처리 전에 관리자 승인이 필요합니다", "requiresSalesManagerApprovalDesc": "이 회사의 주문은 처리 전에 영업 관리자 승인이 필요합니다", "updateSuccess": "승인 설정이 성공적으로 업데이트되었습니다", "updateError": "승인 설정 업데이트에 실패했습니다"}, "customerGroup": {"title": "고객 그룹 관리", "hint": "이 회사를 고객 그룹에 할당하여 그룹별 가격 및 권한을 적용하세요.", "name": "고객 그룹명", "groupName": "고객 그룹", "actions": "작업", "add": "추가", "remove": "제거", "description": "이 회사의 고객 그룹 관리", "noGroups": "사용 가능한 고객 그룹이 없습니다", "addSuccess": "회사가 고객 그룹에 성공적으로 추가되었습니다", "addError": "회사를 고객 그룹에 추가하는데 실패했습니다", "removeSuccess": "회사가 고객 그룹에서 성공적으로 제거되었습니다", "removeError": "회사를 고객 그룹에서 제거하는데 실패했습니다"}, "actions": {"edit": "회사 편집", "editDetails": "세부 정보 편집", "manageCustomerGroup": "고객 그룹 관리", "approvalSettings": "승인 설정", "delete": "회사 삭제", "confirmDelete": "삭제 확인"}, "delete": {"title": "회사 삭제", "description": "이 회사를 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다."}, "employees": {"title": "직원", "noEmployees": "이 회사의 직원을 찾을 수 없습니다", "name": "이름", "email": "이메일", "phone": "전화번호", "role": "역할", "spendingLimit": "지출 한도", "admin": "관리자", "employee": "직원", "add": "직원 추가", "create": {"title": "직원 생성", "success": "직원이 성공적으로 생성되었습니다", "error": "직원 생성에 실패했습니다"}, "form": {"details": "상세 정보", "permissions": "권한", "firstName": "이름", "lastName": "성", "email": "이메일", "phone": "전화번호", "spendingLimit": "지출 한도", "adminAccess": "관리자 액세스", "isAdmin": "관리자임", "isAdminDesc": "이 직원에게 관리자 권한을 부여합니다", "isAdminTooltip": "관리자는 회사 설정과 다른 직원을 관리할 수 있습니다", "firstNamePlaceholder": "이름을 입력하세요", "lastNamePlaceholder": "성을 입력하세요", "emailPlaceholder": "이메일 주소를 입력하세요", "phonePlaceholder": "전화번호를 입력하세요", "spendingLimitPlaceholder": "지출 한도를 입력하세요", "save": "저장", "saving": "저장 중..."}, "delete": {"confirmation": "이 직원을 삭제하시겠습니까?", "success": "직원이 성공적으로 삭제되었습니다"}, "edit": {"title": "직원 편집"}, "toasts": {"updateSuccess": "직원이 성공적으로 업데이트되었습니다", "updateError": "직원 업데이트에 실패했습니다"}}, "toasts": {"createSuccess": "회사가 성공적으로 생성되었습니다", "createError": "회사 생성에 실패했습니다", "updateSuccess": "회사가 성공적으로 업데이트되었습니다", "updateError": "회사 업데이트에 실패했습니다", "deleteSuccess": "회사가 성공적으로 삭제되었습니다", "deleteError": "회사 삭제에 실패했습니다"}}, "approvals": {"domain": "승인", "title": "승인", "subtitle": "승인 워크플로 관리", "noApprovals": "승인을 찾을 수 없습니다", "noApprovalsDescription": "현재 검토할 승인이 없습니다.", "table": {"id": "ID", "type": "유형", "company": "회사", "customer": "고객", "amount": "금액", "status": "상태", "createdAt": "생성일"}, "status": {"pending": "대기 중", "approved": "승인됨", "rejected": "거부됨", "expired": "만료됨", "unknown": "알 수 없음"}, "details": {"header": "승인 세부 정보", "summary": "승인 요약", "company": "회사", "customer": "고객", "order": "주문", "amount": "금액", "updatedAt": "업데이트일", "reason": "사유", "actions": "작업"}, "actions": {"approve": "승인", "reject": "거부", "confirmApprove": "승인 확인", "confirmReject": "거부 확인", "reasonPlaceholder": "사유를 입력하세요 (선택사항)..."}, "filters": {"status": "상태별 필터"}, "toasts": {"approveSuccess": "성공적으로 승인되었습니다", "approveError": "승인에 실패했습니다", "rejectSuccess": "성공적으로 거부되었습니다", "rejectError": "거부에 실패했습니다"}}, "dateTime": {"years_one": "년", "years_other": "년", "months_one": "월", "months_other": "월", "weeks_one": "주", "weeks_other": "주", "days_one": "일", "days_other": "일", "hours_one": "시간", "hours_other": "시간", "minutes_one": "분", "minutes_other": "분", "seconds_one": "초", "seconds_other": "초"}}