{"$schema": "./$schema.json", "general": {"ascending": "Menaik", "descending": "<PERSON><PERSON><PERSON>", "add": "Tambah", "start": "<PERSON><PERSON>", "end": "<PERSON><PERSON><PERSON>", "open": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "apply": "Terapkan", "range": "Rentang", "search": "<PERSON><PERSON>", "of": "dari", "results": "hasil", "pages": "halaman", "next": "Berikutnya", "prev": "Sebelumnya", "is": "ad<PERSON>h", "timeline": "Linimasa", "success": "<PERSON><PERSON><PERSON><PERSON>", "warning": "Peringatan", "tip": "Tips", "error": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "selected": "<PERSON><PERSON><PERSON><PERSON>", "enabled": "Diaktifkan", "disabled": "Dinonaktifkan", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "active": "Aktif", "revoked": "Dicabut", "new": "<PERSON><PERSON>", "modified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "added": "Ditambahkan", "removed": "<PERSON><PERSON><PERSON>", "admin": "Admin", "store": "<PERSON><PERSON>", "details": "Detail", "items_one": "{{count}} item", "items_other": "{{count}} item", "countSelected": "{{count}} dipilih", "countOfTotalSelected": "{{count}} dari {{total}} dipilih", "plusCount": "+ {{count}}", "plusCountMore": "+ {{count}} lagi", "areYouSure": "Anda yakin?", "areYouSureDescription": "<PERSON>a akan menghapus {{entity}} {{title}}. Tindakan ini tidak dapat dibatalkan.", "noRecordsFound": "Tidak ada catatan ditemukan", "typeToConfirm": "Ketik {val} untuk konfirmasi:", "noResultsTitle": "Tidak ada hasil", "noResultsMessage": "Coba ubah filter atau kueri pencarian", "noSearchResults": "Tidak ada hasil pencarian", "noSearchResultsFor": "Tidak ada hasil pencarian untuk <0>'{{query}}'</0>", "noRecordsTitle": "Tidak ada catatan", "noRecordsMessage": "Tidak ada catatan untuk ditampilkan", "unsavedChangesTitle": "Anda yakin ingin meninggalkan formulir ini?", "unsavedChangesDescription": "Anda memiliki perubahan yang belum disimpan yang akan hilang jika Anda keluar dari formulir ini.", "includesTaxTooltip": "<PERSON>rga di kolom ini sudah termasuk pajak.", "excludesTaxTooltip": "<PERSON>rga di kolom ini belum termasuk pajak.", "noMoreData": "Tidak ada data lagi", "actions": "<PERSON><PERSON><PERSON>"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}} kunci", "numberOfKeys_other": "{{count}} kunci", "drawer": {"header_one": "JSON <0>· {{count}} kunci</0>", "header_other": "JSON <0>· {{count}} kunci</0>", "description": "Lihat data JSON untuk objek ini."}}, "metadata": {"header": "<PERSON><PERSON><PERSON>", "numberOfKeys_one": "{{count}} kunci", "numberOfKeys_other": "{{count}} kunci", "edit": {"header": "<PERSON>", "description": "Edit metadata untuk objek ini.", "successToast": "<PERSON><PERSON><PERSON> be<PERSON>.", "actions": {"insertRowAbove": "Sisipkan baris di atas", "insertRowBelow": "Sisipkan baris di bawah", "deleteRow": "Ha<PERSON> baris"}, "labels": {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON>"}, "complexRow": {"label": "Beberapa baris dinonakt<PERSON>", "description": "Objek ini berisi metadata non-primitif, seperti array atau objek, yang tidak dapat diedit di sini. Untuk mengedit baris yang dinonakt<PERSON>, gunakan API secara langsung.", "tooltip": "Baris ini dinonaktifkan karena berisi data non-primitif."}}}, "validation": {"mustBeInt": "<PERSON><PERSON> harus berupa bilangan bulat.", "mustBePositive": "<PERSON><PERSON> harus berupa bilangan positif."}, "actions": {"save": "Simpan", "saveAsDraft": "Simpan sebagai draf", "copy": "<PERSON><PERSON>", "copied": "<PERSON><PERSON><PERSON>", "duplicate": "Duplikat", "publish": "Publikasikan", "create": "Buat", "delete": "Hapus", "remove": "Hapus", "revoke": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "forceConfirm": "<PERSON><PERSON>", "continueEdit": "<PERSON><PERSON><PERSON><PERSON><PERSON> edit", "enable": "Aktifkan", "disable": "Nonaktifkan", "undo": "Urungkan", "complete": "Se<PERSON><PERSON>", "viewDetails": "Lihat detail", "back": "Kembali", "close": "<PERSON><PERSON><PERSON>", "showMore": "<PERSON><PERSON><PERSON><PERSON> lebih banyak", "continue": "Lanjutkan", "continueWithEmail": "Lan<PERSON><PERSON><PERSON> den<PERSON>", "idCopiedToClipboard": "ID tersalin ke papan klip", "addReason": "Tambah Alasan", "addNote": "Tambah Catatan", "reset": "<PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": "Edit", "addItems": "Tambah item", "download": "<PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON><PERSON>", "clearAll": "<PERSON><PERSON><PERSON><PERSON> semua", "apply": "Terapkan", "add": "Tambah", "select": "<PERSON><PERSON><PERSON>", "browse": "<PERSON><PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "hide": "Sembunyikan", "export": "Ekspor", "import": "Impor", "cannotUndo": "Tindakan ini tidak dapat dibatalkan"}, "operators": {"in": "<PERSON><PERSON>"}, "app": {"search": {"label": "<PERSON><PERSON>", "title": "<PERSON><PERSON>", "description": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, produk, pela<PERSON><PERSON>, dan la<PERSON>.", "allAreas": "Semua area", "navigation": "Na<PERSON><PERSON><PERSON>", "openResult": "<PERSON><PERSON> hasil", "showMore": "<PERSON><PERSON><PERSON><PERSON> lebih banyak", "placeholder": "Lompat ke atau temukan apa saja...", "noResultsTitle": "Tidak ada hasil di<PERSON>n", "noResultsMessage": "Coba ubah filter atau kueri pencarian", "emptySearchTitle": "Ketik untuk mencari", "emptySearchMessage": "<PERSON><PERSON><PERSON>n kata kunci atau frasa untuk menjelajahi.", "loadMore": "Muat {{count}} lagi", "groups": {"all": "Semua area", "customer": "Pelanggan", "customerGroup": "Grup Pelanggan", "product": "Produk", "productVariant": "<PERSON><PERSON>", "inventory": "Inventaris", "reservation": "Reservasi", "category": "<PERSON><PERSON><PERSON>", "collection": "<PERSON><PERSON><PERSON><PERSON>", "order": "<PERSON><PERSON><PERSON>", "promotion": "<PERSON><PERSON><PERSON>", "campaign": "Kampanye", "priceList": "<PERSON><PERSON><PERSON>", "user": "Pengguna", "region": "Wilayah", "taxRegion": "<PERSON><PERSON><PERSON>", "returnReason": "<PERSON><PERSON><PERSON>", "salesChannel": "<PERSON><PERSON><PERSON>", "productType": "<PERSON><PERSON>", "productTag": "Tag Produk", "location": "<PERSON><PERSON>", "shippingProfile": "<PERSON><PERSON>", "publishableApiKey": "Kunci API yang Dapat Dipublikasikan", "secretApiKey": "Kunci API Rahasia", "command": "<PERSON><PERSON><PERSON>", "navigation": "Na<PERSON><PERSON><PERSON>"}}, "keyboardShortcuts": {"pageShortcut": "Lompat ke", "settingShortcut": "<PERSON><PERSON><PERSON><PERSON>", "commandShortcut": "<PERSON><PERSON><PERSON>", "then": "lalu", "navigation": {"goToOrders": "<PERSON><PERSON><PERSON>", "goToProducts": "Produk", "goToCollections": "<PERSON><PERSON><PERSON><PERSON>", "goToCategories": "<PERSON><PERSON><PERSON>", "goToCustomers": "Pelanggan", "goToCustomerGroups": "Grup Pelanggan", "goToInventory": "Inventaris", "goToReservations": "Reservasi", "goToPriceLists": "<PERSON><PERSON><PERSON>", "goToPromotions": "<PERSON><PERSON><PERSON>", "goToCampaigns": "Kampanye"}, "settings": {"goToSettings": "<PERSON><PERSON><PERSON><PERSON>", "goToStore": "<PERSON><PERSON>", "goToUsers": "Pengguna", "goToRegions": "Wilayah", "goToTaxRegions": "<PERSON><PERSON><PERSON>", "goToSalesChannels": "<PERSON><PERSON><PERSON>", "goToProductTypes": "<PERSON><PERSON>", "goToLocations": "<PERSON><PERSON>", "goToPublishableApiKeys": "Kunci API yang Dapat Dipublikasikan", "goToSecretApiKeys": "Kunci API Rahasia", "goToWorkflows": "<PERSON><PERSON>", "goToProfile": "Profil", "goToReturnReasons": "Alasan pen<PERSON>"}}, "menus": {"user": {"documentation": "Dokumentasi", "changelog": "Catatan Perubahan", "shortcuts": "<PERSON><PERSON><PERSON>", "profileSettings": "Pengaturan profil", "theme": {"label": "<PERSON><PERSON>", "dark": "<PERSON><PERSON><PERSON>", "light": "Terang", "system": "Sistem"}}, "store": {"label": "<PERSON><PERSON>", "storeSettings": "<PERSON><PERSON><PERSON><PERSON> toko"}, "actions": {"logout": "<PERSON><PERSON><PERSON>"}}, "nav": {"accessibility": {"title": "Na<PERSON><PERSON><PERSON>", "description": "<PERSON>u navigasi untuk dasbor."}, "common": {"extensions": "<PERSON><PERSON><PERSON><PERSON>"}, "main": {"store": "<PERSON><PERSON>", "storeSettings": "<PERSON><PERSON><PERSON><PERSON> toko"}, "settings": {"header": "<PERSON><PERSON><PERSON><PERSON>", "general": "<PERSON><PERSON>", "developer": "Pengembang", "myAccount": "<PERSON><PERSON><PERSON>"}}}, "dataGrid": {"columns": {"view": "Lihat", "resetToDefault": "Atur ulang ke default", "disabled": "Mengubah kolom yang terlihat dinonaktifkan."}, "shortcuts": {"label": "<PERSON><PERSON><PERSON>", "commands": {"undo": "Urungkan", "redo": "<PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON>", "paste": "Tempel", "edit": "Edit", "delete": "Hapus", "clear": "<PERSON><PERSON><PERSON><PERSON>", "moveUp": "Pindahkan ke atas", "moveDown": "Pindahkan ke bawah", "moveLeft": "Pindahkan ke kiri", "moveRight": "Pindahkan ke kanan", "moveTop": "Pindahkan ke atas", "moveBottom": "Pindahkan ke bawah", "selectDown": "<PERSON><PERSON><PERSON> ke bawah", "selectUp": "<PERSON><PERSON>h ke atas", "selectColumnDown": "<PERSON><PERSON><PERSON> kolom ke bawah", "selectColumnUp": "<PERSON><PERSON><PERSON> kolom ke atas", "focusToolbar": "Fokus bilah alat", "focusCancel": "<PERSON><PERSON><PERSON> batal"}}, "errors": {"fixError": "Perbaiki kesalahan", "count_one": "{{count}} k<PERSON><PERSON>han", "count_other": "{{count}} k<PERSON><PERSON>han"}}, "filters": {"sortLabel": "Urut<PERSON>", "filterLabel": "Filter", "searchLabel": "<PERSON><PERSON>", "date": {"today": "<PERSON> ini", "lastSevenDays": "7 hari terakhir", "lastThirtyDays": "30 hari terakhir", "lastNinetyDays": "90 hari terakhir", "lastTwelveMonths": "12 bulan terakhir", "custom": "Kustom", "from": "<PERSON><PERSON>", "to": "Sampai", "starting": "<PERSON><PERSON>", "ending": "<PERSON><PERSON><PERSON>"}, "compare": {"lessThan": "<PERSON><PERSON> dari", "greaterThan": "<PERSON><PERSON><PERSON> dari", "exact": "<PERSON><PERSON>", "range": "Rentang", "lessThanLabel": "kurang dari {{value}}", "greaterThanLabel": "lebih dari {{value}}", "andLabel": "dan"}, "sorting": {"alphabeticallyAsc": "A ke Z", "alphabeticallyDesc": "Z ke A", "dateAsc": "Terbaru dulu", "dateDesc": "Terlama dulu"}, "radio": {"yes": "Ya", "no": "Tidak", "true": "<PERSON><PERSON>", "false": "<PERSON><PERSON>"}, "addFilter": "Tambah filter"}, "errorBoundary": {"badRequestTitle": "400 - <PERSON><PERSON><PERSON><PERSON> buruk", "badRequestMessage": "Permintaan tidak dapat dipahami oleh server karena sintaks yang salah bentuk.", "notFoundTitle": "404 - <PERSON><PERSON>k ada halaman di alamat ini", "notFoundMessage": "Periksa URL dan coba lagi, atau gunakan bilah pencarian untuk menemukan apa yang <PERSON>a cari.", "internalServerErrorTitle": "500 - Kesalahan server internal", "internalServerErrorMessage": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han tak terduga di server. <PERSON><PERSON>an coba lagi nanti.", "defaultTitle": "<PERSON><PERSON><PERSON><PERSON>", "defaultMessage": "<PERSON><PERSON><PERSON><PERSON> kesalahan tak terduga saat merender halaman ini.", "noMatchMessage": "<PERSON><PERSON> yang Anda cari tidak ada.", "backToDashboard": "<PERSON><PERSON><PERSON> ke dasbor"}, "addresses": {"title": "<PERSON><PERSON><PERSON>", "shippingAddress": {"header": "<PERSON><PERSON><PERSON>", "editHeader": "<PERSON> <PERSON><PERSON><PERSON>", "editLabel": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, "billingAddress": {"header": "<PERSON><PERSON><PERSON>", "editHeader": "<PERSON> <PERSON><PERSON><PERSON>", "editLabel": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>", "sameAsShipping": "<PERSON><PERSON> dengan alamat <PERSON>"}, "contactHeading": "Kontak", "locationHeading": "<PERSON><PERSON>"}, "email": {"editHeader": "<PERSON> Email", "editLabel": "Email", "label": "Email"}, "transferOwnership": {"header": "Transfer Kepemilikan", "label": "Transfer kepemilikan", "details": {"order": "<PERSON>ail pesanan", "draft": "Detail draf"}, "currentOwner": {"label": "Pemilik saat ini", "hint": "Pemilik pesanan saat ini."}, "newOwner": {"label": "Pemilik baru", "hint": "Pemilik baru untuk mentransfer pesanan."}, "validation": {"mustBeDifferent": "Pemilik baru harus berbeda dari pemilik saat ini.", "required": "Pemilik baru wajib diisi."}}, "sales_channels": {"availableIn": "Tersedia di <0>{{x}}</0> dari <1>{{y}}</1> saluran penjualan"}, "products": {"domain": "Produk", "list": {"noRecordsMessage": "Buat produk pertama Anda untuk mulai berju<PERSON>."}, "edit": {"header": "Edit Produk", "description": "Edit detail produk.", "successToast": "Produk {{title}} ber<PERSON><PERSON> diperbarui."}, "create": {"title": "Buat Produk", "description": "Buat produk baru.", "header": "<PERSON><PERSON>", "tabs": {"details": "Detail", "organize": "Atur", "variants": "<PERSON><PERSON>", "inventory": "<PERSON> inventaris"}, "errors": {"variants": "<PERSON><PERSON><PERSON> setida<PERSON>nya satu varian.", "options": "Buat setidaknya satu opsi.", "uniqueSku": "SKU harus unik."}, "inventory": {"heading": "<PERSON> inventaris", "label": "Tambah item inventaris ke kit inventaris varian.", "itemPlaceholder": "Pilih item inventaris", "quantityPlaceholder": "Berapa banyak dari ini yang dibutuhkan untuk kit?"}, "variants": {"header": "<PERSON><PERSON>", "subHeadingTitle": "Ya, ini adalah produk dengan varian", "subHeadingDescription": "<PERSON><PERSON> tidak dicenta<PERSON>, kami akan membuat varian default untuk Anda", "optionTitle": {"placeholder": "Ukuran"}, "optionValues": {"placeholder": "Kecil, Sedang, Besar"}, "productVariants": {"label": "Varian produk", "hint": "Peringkat ini akan memengaruhi urutan varian di etalase <PERSON>.", "alert": "Tambah opsi untuk membuat varian.", "tip": "Varian yang tidak dicentang tidak akan dibuat. <PERSON>a selalu dapat membuat dan mengedit varian set<PERSON>, tetapi daftar ini sesuai dengan variasi dalam opsi produk Anda."}, "productOptions": {"label": "Opsi produk", "hint": "Definisikan opsi untuk produk, misal: warna, ukuran, dll."}}, "successToast": "Produk {{title}} berhasil dibuat."}, "export": {"header": "Ekspor Daftar Produk", "description": "Ekspor daftar produk ke file CSV.", "success": {"title": "<PERSON><PERSON>g memproses ekspor Anda", "description": "Mengekspor data mungkin memakan waktu beberapa menit. <PERSON><PERSON> akan member<PERSON><PERSON>h se<PERSON>."}, "filters": {"title": "Filter", "description": "Terapkan filter di tabel ikhtisar untuk menyesuaikan tampilan ini"}, "columns": {"title": "<PERSON><PERSON><PERSON>", "description": "Sesuaikan data yang diekspor untuk memenuhi kebutuhan spesifik"}}, "import": {"header": "I<PERSON>r Daftar <PERSON>", "uploadLabel": "Impor Produk", "uploadHint": "Seret dan lepas file CSV di sini atau klik untuk mengunggah", "description": "Impor produk dengan menyediakan file CSV dalam format yang telah ditentukan", "template": {"title": "Tidak yakin cara mengatur daftar And<PERSON>?", "description": "Unduh template di bawah untuk memastikan Anda mengikuti format yang benar."}, "upload": {"title": "Unggah file CSV", "description": "Melalui impor Anda dapat menambah atau memperbarui produk. Untuk memperbarui produk yang sudah ada, <PERSON><PERSON> harus menggunakan handle dan ID yang sudah ada, untuk memperbarui varian yang sudah ada, Anda harus menggunakan ID yang sudah ada. Anda akan diminta konfirmasi sebelum kami mengimpor produk.", "preprocessing": "Memproses awal...", "productsToCreate": "Produk akan dibuat", "productsToUpdate": "<PERSON><PERSON>k akan <PERSON>"}, "success": {"title": "<PERSON><PERSON> sedang memproses impor Anda", "description": "Mengimpor data mungkin memakan waktu cukup lama. <PERSON><PERSON> akan member<PERSON><PERSON> se<PERSON>."}}, "deleteWarning": "<PERSON>a akan menghapus produk {{title}}. <PERSON>dakan ini tidak dapat di<PERSON>an.", "variants": {"header": "<PERSON><PERSON>", "empty": {"heading": "Tidak ada varian", "description": "Tidak ada varian untuk ditampilkan."}, "filtered": {"heading": "Tidak ada hasil", "description": "Tidak ada varian yang cocok dengan kriteria filter saat ini."}}, "attributes": "Atribut", "editAttributes": "Edit Atribut", "editOptions": "Edit Opsi", "editPrices": "Edit harga", "media": {"label": "Media", "editHint": "Tambah media ke produk untuk menampilkannya di etalase Anda.", "makeThumbnail": "<PERSON><PERSON><PERSON> thumbnail", "uploadImagesLabel": "Unggah gambar", "uploadImagesHint": "Seret dan lepas gambar di sini atau klik untuk mengunggah.", "invalidFileType": "'{{name}}' bukan jenis file yang didukung. Jenis file yang didukung adalah: {{types}}.", "failedToUpload": "Gagal mengunggah media yang ditambahkan. Silakan coba lagi.", "deleteWarning_one": "<PERSON>a akan mengh<PERSON> {{count}} gambar. Tindakan ini tidak dapat dibatalkan.", "deleteWarning_other": "<PERSON>a akan mengh<PERSON> {{count}} gambar. Tindakan ini tidak dapat dibatalkan.", "deleteWarningWithThumbnail_one": "<PERSON>a akan menghapus {{count}} gambar termasuk thumbnail. Tindakan ini tidak dapat dibatalkan.", "deleteWarningWithThumbnail_other": "<PERSON>a akan menghapus {{count}} gambar termasuk thumbnail. Tindakan ini tidak dapat dibatalkan.", "thumbnailTooltip": "<PERSON><PERSON><PERSON><PERSON>", "galleryLabel": "<PERSON><PERSON>", "downloadImageLabel": "Unduh gambar saat ini", "deleteImageLabel": "<PERSON>pus gambar saat ini", "emptyState": {"header": "Belum ada media", "description": "Tambah media ke produk untuk menampilkannya di etalase Anda.", "action": "Tambah media"}, "successToast": "Media berhasil diperbarui."}, "discountableHint": "<PERSON><PERSON> tidak dicentang, diskon tidak akan diterapkan pada produk ini.", "noSalesChannels": "Tidak tersedia di saluran penjualan mana pun", "variantCount_one": "{{count}} varian", "variantCount_other": "{{count}} varian", "deleteVariantWarning": "<PERSON>a akan mengh<PERSON>us varian {{title}}. Tindakan ini tidak dapat di<PERSON>an.", "productStatus": {"draft": "Draf", "published": "Dipublikasikan", "proposed": "Diusulkan", "rejected": "<PERSON><PERSON><PERSON>"}, "fields": {"title": {"label": "<PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON> judul produk <PERSON><PERSON> yang singkat dan jelas.<0/>50-60 karakter adalah panjang yang disarankan untuk mesin pencari.", "placeholder": "<PERSON>t musim dingin"}, "subtitle": {"label": "Subjudul", "placeholder": "<PERSON><PERSON> dan nyaman"}, "handle": {"label": "<PERSON><PERSON>", "tooltip": "Handle digunakan untuk mereferensikan produk di etalase Anda. <PERSON><PERSON> tidak ditentukan, handle akan dibuat dari judul produk.", "placeholder": "jaket-musim-dingin"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON><PERSON> produk <PERSON><PERSON> yang singkat dan jelas.<0/>120-160 karakter adalah panjang yang disarankan untuk mesin pencari.", "placeholder": "<PERSON><PERSON> hangat dan nyaman"}, "discountable": {"label": "Dapat Diskon", "hint": "<PERSON><PERSON> tidak dicenta<PERSON>, diskon tidak akan diterapkan pada produk ini"}, "shipping_profile": {"label": "<PERSON><PERSON>", "hint": "Hubungkan produk ke profil pengiriman"}, "type": {"label": "<PERSON><PERSON>"}, "collection": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "categories": {"label": "<PERSON><PERSON><PERSON>"}, "tags": {"label": "Tag"}, "sales_channels": {"label": "<PERSON><PERSON><PERSON>", "hint": "Produk ini hanya akan tersedia di saluran penjualan default jika dibiarkan apa adanya."}, "countryOrigin": {"label": "<PERSON><PERSON><PERSON> asal"}, "material": {"label": "<PERSON><PERSON>"}, "width": {"label": "<PERSON><PERSON>"}, "length": {"label": "Panjang"}, "height": {"label": "Tingg<PERSON>"}, "weight": {"label": "<PERSON><PERSON>"}, "options": {"label": "Opsi produk", "hint": "Opsi digunakan untuk men<PERSON>n warna, <PERSON><PERSON><PERSON>, dll. dari produk", "add": "Tambah opsi", "optionTitle": "<PERSON><PERSON><PERSON> opsi", "optionTitlePlaceholder": "<PERSON><PERSON>", "variations": "V<PERSON><PERSON> (dipisahkan koma)", "variantionsPlaceholder": "Merah, Biru, Hijau"}, "variants": {"label": "Varian produk", "hint": "Varian yang tidak dicentang tidak akan dibuat, peringkat ini akan memengaruhi peringkat varian di frontend And<PERSON>."}, "mid_code": {"label": "Kode MID"}, "hs_code": {"label": "Kode HS"}}, "variant": {"edit": {"header": "<PERSON>", "success": "Varian produk <PERSON><PERSON><PERSON><PERSON> diedit"}, "create": {"header": "Detail varian"}, "deleteWarning": "Anda yakin ingin menghapus varian ini?", "pricesPagination": "1 - {{current}} dari {{total}} harga", "tableItemAvailable": "{{availableCount}} tersedia", "tableItem_one": "{{availableCount}} tersedia di {{locationCount}} lokasi", "tableItem_other": "{{availableCount}} tersedia di {{locationCount}} lokasi", "inventory": {"notManaged": "Tidak dikelola", "manageItems": "Kelola item inventaris", "notManagedDesc": "Inventaris tidak dikelola untuk varian ini. Aktifkan 'Kelola Inventaris' untuk melacak inventaris varian.", "manageKit": "Kelola kit inventaris", "navigateToItem": "Pergi ke item inventaris", "actions": {"inventoryItems": "Pergi ke item inventaris", "inventoryKit": "Tampilkan item inventaris"}, "inventoryKit": "<PERSON>", "inventoryKitHint": "<PERSON><PERSON><PERSON><PERSON> varian ini terdiri dari beberapa item inventaris?", "validation": {"itemId": "Pilih item inventaris.", "quantity": "Kuantitas wajib diisi. Masukkan angka positif."}, "header": "Stok & Inventaris", "editItemDetails": "Edit detail item", "manageInventoryLabel": "<PERSON><PERSON><PERSON> inventaris", "manageInventoryHint": "<PERSON><PERSON>, kami akan mengubah kuantitas inventaris untuk Anda saat pesanan dan pengembalian dibuat.", "allowBackordersLabel": "Izinkan pesanan kembali", "allowBackordersHint": "<PERSON><PERSON>, pelanggan dapat membeli varian meskipun tidak ada kuantitas yang tersedia.", "toast": {"levelsBatch": "Level inventaris diperbarui.", "update": "Item inventaris berhasil diperbarui.", "updateLevel": "Level inventaris berhasil diperbarui.", "itemsManageSuccess": "Item inventaris berhasil diperbarui."}}}, "options": {"header": "Opsi", "edit": {"header": "Edit Opsi", "successToast": "Opsi {{title}} ber<PERSON><PERSON> diperbarui."}, "create": {"header": "Buat Opsi", "successToast": "Opsi {{title}} berhasil dibuat."}, "deleteWarning": "<PERSON><PERSON> akan menghapus opsi produk: {{title}}. <PERSON>dakan ini tidak dapat di<PERSON>an."}, "organization": {"header": "Atur", "edit": {"header": "Edit Organ<PERSON>", "toasts": {"success": "<PERSON><PERSON><PERSON><PERSON> me<PERSON> organisasi {{title}}."}}}, "stock": {"heading": "<PERSON><PERSON><PERSON> tingkat stok dan lokasi produk", "description": "<PERSON><PERSON><PERSON> tingkat inventaris stok untuk semua varian produk.", "loading": "<PERSON><PERSON><PERSON> se<PERSON>, ini mungkin memakan waktu...", "tooltips": {"alreadyManaged": "Item inventaris ini sudah dapat diedit di bawah {{title}}.", "alreadyManagedWithSku": "Item inventaris ini sudah dapat diedit di bawah {{title}} ({{sku}})."}}, "shippingProfile": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toasts": {"success": "<PERSON><PERSON><PERSON><PERSON> profil pen<PERSON>man untuk {{title}}."}}, "create": {"errors": {"required": "<PERSON>il <PERSON> wajib <PERSON>"}}}, "toasts": {"delete": {"success": {"header": "<PERSON><PERSON><PERSON>", "description": "{{title}} ber<PERSON><PERSON> di<PERSON><PERSON>."}, "error": {"header": "<PERSON><PERSON> produk"}}}}, "collections": {"domain": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Atur produk ke dalam koleksi.", "createCollection": "<PERSON><PERSON>t Koleksi", "createCollectionHint": "Buat koleksi baru untuk mengatur produk Anda.", "createSuccess": "Koleksi berhasil dibuat.", "editCollection": "<PERSON>", "handleTooltip": "Handle digunakan untuk mereferensikan koleksi di etalase Anda. Jika tidak ditentukan, handle akan dibuat dari judul koleksi.", "deleteWarning": "<PERSON><PERSON> akan mengh<PERSON> koleksi {{title}}. Tindakan ini tidak dapat di<PERSON>an.", "removeSingleProductWarning": "<PERSON><PERSON> akan menghapus produk {{title}} dari koleksi. Tindakan ini tidak dapat dibatalkan.", "removeProductsWarning_one": "<PERSON>a akan mengh<PERSON> {{count}} produk dari koleksi. Tindakan ini tidak dapat dibatalkan.", "removeProductsWarning_other": "<PERSON>a akan mengh<PERSON> {{count}} produk dari koleksi. Tindakan ini tidak dapat dibatalkan.", "products": {"list": {"noRecordsMessage": "Tidak ada produk dalam koleksi."}, "add": {"successToast_one": "Produk ber<PERSON>il ditambahkan ke koleksi.", "successToast_other": "Produk ber<PERSON>il ditambahkan ke koleksi."}, "remove": {"successToast_one": "<PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON> dari kole<PERSON>i.", "successToast_other": "<PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON> dari kole<PERSON>i."}}}, "categories": {"domain": "<PERSON><PERSON><PERSON>", "subtitle": "Atur produk ke dalam kategori, dan kelola peringkat dan hierarki kategori tersebut.", "create": {"header": "<PERSON><PERSON>t <PERSON>gori", "hint": "Buat kategori baru untuk mengatur produk Anda.", "tabs": {"details": "Detail", "organize": "<PERSON><PERSON>"}, "successToast": "Kate<PERSON><PERSON> {{name}} berhasil dibuat."}, "edit": {"header": "<PERSON>", "description": "Edit kate<PERSON>i untuk memperbarui <PERSON>.", "successToast": "<PERSON><PERSON><PERSON> be<PERSON>."}, "delete": {"confirmation": "<PERSON><PERSON> akan menghapus kategori {{name}}. Tindakan ini tidak dapat dibat<PERSON>an.", "successToast": "<PERSON><PERSON><PERSON> {{name}} ber<PERSON><PERSON> di<PERSON>."}, "products": {"add": {"disabledTooltip": "Produk sudah ada dalam kategori ini.", "successToast_one": "Menambahkan {{count}} produk ke kategori.", "successToast_other": "Menambahkan {{count}} produk ke kategori."}, "remove": {"confirmation_one": "<PERSON>a akan mengh<PERSON> {{count}} produk dari kategori. Tindakan ini tidak dapat dibatalkan.", "confirmation_other": "<PERSON>a akan mengh<PERSON> {{count}} produk dari kategori. Tindakan ini tidak dapat dibatalkan.", "successToast_one": "<PERSON>gh<PERSON><PERSON> {{count}} produk dari kate<PERSON>i.", "successToast_other": "<PERSON>gh<PERSON><PERSON> {{count}} produk dari kate<PERSON>i."}, "list": {"noRecordsMessage": "Tidak ada produk dalam kategori."}}, "organize": {"header": "Atur", "action": "Edit peringkat"}, "fields": {"visibility": {"label": "Visibilitas", "internal": "Internal", "public": "Publik"}, "status": {"label": "Status", "active": "Aktif", "inactive": "Tidak aktif"}, "path": {"label": "Path", "tooltip": "Tampilkan path lengkap kategori."}, "children": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "new": {"label": "<PERSON><PERSON>"}}}, "inventory": {"domain": "Inventaris", "subtitle": "Kelola item inventaris Anda", "reserved": "Dipesan", "available": "Tersedia", "locationLevels": "<PERSON><PERSON>", "associatedVariants": "Varian terkait", "manageLocations": "<PERSON><PERSON><PERSON> lo<PERSON>i", "manageLocationQuantity": "<PERSON><PERSON>la kuantitas lokasi", "deleteWarning": "Anda akan menghapus item inventaris. Tindakan ini tidak dapat dibatalkan.", "editItemDetails": "Edit detail item", "quantityAcrossLocations": "{{quantity}} di {{locations}} lokasi", "create": {"title": "Buat Item Inventaris", "details": "Detail", "availability": "<PERSON><PERSON><PERSON><PERSON>", "locations": "<PERSON><PERSON>", "attributes": "Atribut", "requiresShipping": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requiresShippingHint": "Apakah item inventaris membutuhkan pengiriman?", "successToast": "Item inventaris berhasil dibuat."}, "reservation": {"header": "Reservasi {{itemName}}", "editItemDetails": "Edit reservasi", "lineItemId": "ID Item Baris", "orderID": "<PERSON> Pesanan", "description": "<PERSON><PERSON><PERSON><PERSON>", "location": "<PERSON><PERSON>", "inStockAtLocation": "Stok tersedia di lokasi ini", "availableAtLocation": "Tersedia di lokasi ini", "reservedAtLocation": "Dipesan di lokasi ini", "reservedAmount": "<PERSON><PERSON><PERSON> reser<PERSON>", "create": "Buat reservasi", "itemToReserve": "<PERSON><PERSON> yang akan direservasi", "quantityPlaceholder": "<PERSON><PERSON>a banyak yang ingin Anda reservasi?", "descriptionPlaceholder": "<PERSON>is reservasi apa ini?", "successToast": "Reservasi berhasil dibuat.", "updateSuccessToast": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON>.", "deleteSuccessToast": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON><PERSON>.", "errors": {"noAvaliableQuantity": "Lokasi stok tidak memiliki kuantitas yang tersedia.", "quantityOutOfRange": "Kuantitas minimum adalah 1 dan kuantitas maksimum adalah {{max}}"}}, "adjustInventory": {"errors": {"stockedQuantity": "Kuantitas stok tidak dapat diperbarui menjadi kurang dari kuantitas yang dipesan yaitu {{quantity}}."}}, "toast": {"updateLocations": "<PERSON><PERSON> be<PERSON><PERSON>.", "updateLevel": "Tingkat inventaris berhasil diperbarui.", "updateItem": "Item inventaris berhasil diperbarui."}, "stock": {"title": "<PERSON><PERSON><PERSON> tingkat inventaris", "description": "<PERSON><PERSON><PERSON> tingkat inventaris stok untuk semua varian produk.", "action": "Edit tingkat stok", "placeholder": "Tidak diaktifkan", "disablePrompt_one": "<PERSON>a akan menonaktifkan {{count}} tingkat lokasi. Tindakan ini tidak dapat dibatalkan.", "disablePrompt_other": "<PERSON>a akan menonaktifkan {{count}} tingkat lokasi. Tindakan ini tidak dapat dibatalkan.", "disabledToggleTooltip": "Tidak dapat menonaktifkan: kosongkan kuantitas masuk dan/atau dipesan sebelum menonaktifkan.", "successToast": "Tingkat inventaris berhasil diperbarui."}}, "giftCards": {"domain": "<PERSON><PERSON><PERSON>", "editGiftCard": "<PERSON> <PERSON><PERSON><PERSON>", "createGiftCard": "Buat Kartu Hadiah", "createGiftCardHint": "Buat kartu hadiah secara manual yang dapat digunakan sebagai metode pembayaran di toko Anda.", "selectRegionFirst": "<PERSON><PERSON><PERSON> wilayah terlebih dahulu", "deleteGiftCardWarning": "<PERSON>a akan menghapus kartu hadiah {{code}}. Tindakan ini tidak dapat dibatalkan.", "balanceHigherThanValue": "Saldo tidak bisa lebih tinggi dari jumlah awal.", "balanceLowerThanZero": "Saldo tidak bisa negatif.", "expiryDateHint": "Negara memiliki hukum yang berbeda mengenai tanggal kedaluwarsa kartu hadiah. Pastikan untuk memeriksa peraturan setempat sebelum menetapkan tanggal kedaluwarsa.", "regionHint": "Mengubah wilayah kartu hadiah juga akan mengubah mata uangnya, berpotensi memengaruhi nilai moneternya.", "enabledHint": "Tentukan apakah kartu hadiah diaktifkan atau dinonaktifkan.", "balance": "<PERSON><PERSON>", "currentBalance": "Saldo saat ini", "initialBalance": "<PERSON><PERSON>", "personalMessage": "<PERSON><PERSON> pribadi", "recipient": "<PERSON><PERSON><PERSON>"}, "customers": {"domain": "Pelanggan", "list": {"noRecordsMessage": "Pelanggan Anda akan muncul di sini."}, "create": {"header": "<PERSON><PERSON><PERSON>ng<PERSON>", "hint": "<PERSON>uat pelanggan baru dan kelola <PERSON>.", "successToast": "Pelanggan {{email}} berhasil dibuat."}, "groups": {"label": "Grup pelanggan", "remove": "Anda yakin ingin menghapus pelanggan dari grup pelanggan \"{{name}}\"?", "removeMany": "Anda yakin ingin menghapus pelanggan dari grup pelanggan berikut: {{groups}}?", "alreadyAddedTooltip": "Pelanggan sudah ada di grup pelanggan ini.", "list": {"noRecordsMessage": "Pelanggan ini tidak termasuk dalam grup mana pun."}, "add": {"success": "Pelanggan ditambahkan ke: {{groups}}.", "list": {"noRecordsMessage": "Buat grup pelanggan terlebih dahulu."}}, "removed": {"success": "Pelanggan dihapus dari: {{groups}}.", "list": {"noRecordsMessage": "Buat grup pelanggan terlebih dahulu."}}}, "edit": {"header": "<PERSON>", "emailDisabledTooltip": "Alamat email tidak dapat diubah untuk pelanggan terdaftar.", "successToast": "Pelanggan {{email}} berhasil diperbarui."}, "delete": {"title": "<PERSON><PERSON>", "description": "<PERSON>a akan menghapus pelanggan {{email}}. Tindakan ini tidak dapat dibatalkan.", "successToast": "P<PERSON>nggan {{email}} berhasil dihapus."}, "fields": {"guest": "<PERSON><PERSON>", "registered": "Terdaftar", "groups": "Grup"}, "registered": "Terdaftar", "guest": "<PERSON><PERSON>", "hasAccount": "<PERSON><PERSON><PERSON> akun", "addresses": {"title": "<PERSON><PERSON><PERSON>", "fields": {"addressName": "<PERSON><PERSON>", "address1": "Alamat 1", "address2": "Alamat 2", "city": "Kota", "province": "<PERSON><PERSON><PERSON>", "postalCode": "<PERSON><PERSON> pos", "country": "Negara", "phone": "Telepon", "company": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "Kode negara", "provinceCode": "<PERSON><PERSON>"}, "create": {"header": "<PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON>t alamat baru untuk pelanggan.", "successToast": "<PERSON><PERSON><PERSON> be<PERSON> di<PERSON>at."}}}, "customerGroups": {"domain": "Grup Pelanggan", "subtitle": "Atur pelanggan ke dalam grup. Grup dapat memiliki promosi dan harga yang berbeda.", "list": {"empty": {"heading": "Tidak ada grup pelanggan", "description": "Tidak ada grup pelanggan untuk ditampilkan."}, "filtered": {"heading": "Tidak ada hasil", "description": "Tidak ada grup pelanggan yang cocok dengan kriteria filter saat ini."}}, "create": {"header": "Buat Grup Pelanggan", "hint": "Buat grup pelanggan baru untuk segmentasi pelanggan Anda.", "successToast": "Grup pelanggan {{name}} berhasil dibuat."}, "edit": {"header": "<PERSON>", "successToast": "Grup pelanggan {{name}} ber<PERSON><PERSON> diperbarui."}, "delete": {"title": "Hapus Grup Pelanggan", "description": "<PERSON><PERSON> akan menghapus grup pelanggan {{name}}. Tindakan ini tidak dapat dibatalkan.", "successToast": "Grup pelanggan {{name}} ber<PERSON><PERSON> di<PERSON>pus."}, "customers": {"alreadyAddedTooltip": "Pelanggan sudah ditambahkan ke grup.", "add": {"successToast_one": "Pelanggan berhasil ditambahkan ke grup.", "successToast_other": "Pelanggan berhasil ditambahkan ke grup.", "list": {"noRecordsMessage": "Buat pelanggan terlebih dahulu."}}, "remove": {"title_one": "<PERSON><PERSON> p<PERSON>", "title_other": "<PERSON><PERSON> p<PERSON>", "description_one": "<PERSON>a akan menghapus {{count}} pelanggan dari grup pelanggan. Tindakan ini tidak dapat dibatalkan.", "description_other": "<PERSON>a akan menghapus {{count}} pelanggan dari grup pelanggan. Tindakan ini tidak dapat dibatalkan."}, "list": {"noRecordsMessage": "Grup ini tidak memiliki pelanggan."}}}, "orders": {"domain": "<PERSON><PERSON><PERSON>", "claim": "<PERSON><PERSON><PERSON>", "exchange": "<PERSON><PERSON>", "return": "<PERSON><PERSON><PERSON><PERSON>", "cancelWarning": "<PERSON>a akan membatalkan pesanan {{id}}. Tindakan ini tidak dapat dibatalkan.", "orderCanceled": "<PERSON><PERSON><PERSON><PERSON>", "onDateFromSalesChannel": "{{date}} dari {{salesChannel}}", "list": {"noRecordsMessage": "<PERSON><PERSON><PERSON>a akan muncul di sini."}, "status": {"not_paid": "<PERSON><PERSON>", "pending": "Tertunda", "completed": "Se<PERSON><PERSON>", "draft": "Draf", "archived": "<PERSON><PERSON><PERSON><PERSON>", "canceled": "Di<PERSON><PERSON><PERSON>", "requires_action": "Memb<PERSON><PERSON><PERSON> tin<PERSON>an"}, "summary": {"requestReturn": "<PERSON><PERSON> pen<PERSON>", "allocateItems": "Alokasikan item", "editOrder": "<PERSON>", "editOrderContinue": "Lanjutkan edit pesanan", "inventoryKit": "<PERSON><PERSON>ri dari {{count}}x item inventaris", "itemTotal": "Total Item", "shippingTotal": "Total Pengiriman", "discountTotal": "Total Diskon", "taxTotalIncl": "Total Pajak (termasuk)", "itemSubtotal": "Subtotal Item", "shippingSubtotal": "Subtotal Pengiriman", "discountSubtotal": "Subtotal Diskon", "taxTotal": "Total Pajak"}, "transfer": {"title": "Transfer kepemilikan", "requestSuccess": "Permintaan transfer pesanan dikirim ke: {{email}}.", "currentOwner": "Pemilik saat ini", "newOwner": "Pemilik baru", "currentOwnerDescription": "Pelanggan yang saat ini terkait dengan pesanan ini.", "newOwnerDescription": "Pelanggan yang akan menerima transfer pesanan ini."}, "payment": {"title": "Pembayaran", "isReadyToBeCaptured": "Pembayaran <0/> siap untuk diambil.", "totalPaidByCustomer": "Total dibayar oleh pelanggan", "capture": "<PERSON><PERSON>", "capture_short": "Ambil", "refund": "Pengembalian dana", "markAsPaid": "Tandai sebagai dibayar", "statusLabel": "Status pembayaran", "statusTitle": "Status Pembayaran", "status": {"notPaid": "<PERSON><PERSON>", "authorized": "<PERSON><PERSON><PERSON><PERSON>", "partiallyAuthorized": "<PERSON><PERSON><PERSON>", "awaiting": "<PERSON><PERSON><PERSON>", "captured": "<PERSON><PERSON><PERSON>", "partiallyRefunded": "Sebagian dikembalikan", "partiallyCaptured": "Sebagian diambil", "refunded": "Di<PERSON><PERSON>lik<PERSON>", "canceled": "Di<PERSON><PERSON><PERSON>", "requiresAction": "Memb<PERSON><PERSON><PERSON> tin<PERSON>an"}, "capturePayment": "Pembayaran sebesar {{amount}} akan diambil.", "capturePaymentSuccess": "<PERSON>embayaran sebesar {{amount}} ber<PERSON><PERSON> diambil", "markAsPaidPayment": "Pembayaran sebesar {{amount}} akan ditandai sebagai dibayar.", "markAsPaidPaymentSuccess": "Pembayaran sebesar {{amount}} ber<PERSON>il ditandai sebagai dibayar", "createRefund": "<PERSON><PERSON><PERSON>", "refundPaymentSuccess": "<PERSON><PERSON><PERSON><PERSON> dana sebesar {{amount}} ber<PERSON>il", "createRefundWrongQuantity": "Kuantitas harus berupa angka antara 1 dan {{number}}", "refundAmount": "Pengembalian dana {{ amount }}", "paymentLink": "<PERSON>in tautan pembayaran untuk {{ amount }}", "selectPaymentToRefund": "<PERSON><PERSON>h pembayaran untuk dikembalikan"}, "edits": {"title": "<PERSON>", "confirm": "Konfirmasi Edit", "confirmText": "<PERSON><PERSON> akan <PERSON> <PERSON>. Tindakan ini tidak dapat di<PERSON>.", "cancel": "Batalkan Edit", "currentItems": "Item saat ini", "currentItemsDescription": "Sesuaikan kuantitas item atau hapus.", "addItemsDescription": "Anda dapat menambah item baru ke pesanan.", "addItems": "Tambah item", "amountPaid": "<PERSON><PERSON><PERSON>", "newTotal": "Total baru", "differenceDue": "<PERSON><PERSON><PERSON> te<PERSON>tang", "create": "<PERSON>", "currentTotal": "Total saat ini", "noteHint": "Tambah catatan internal untuk edit", "cancelSuccessToast": "<PERSON> p<PERSON><PERSON>", "createSuccessToast": "Permintaan edit pesanan dibuat", "activeChangeError": "Sudah ada perubahan pesanan yang aktif pada pesanan (pen<PERSON><PERSON><PERSON>, klaim, penuka<PERSON>, dll.). <PERSON><PERSON><PERSON><PERSON> atau batalkan perubahan sebelum mengedit pesanan.", "panel": {"title": "<PERSON> pesanan diminta", "titlePending": "<PERSON> pesanan tertunda"}, "toast": {"canceledSuccessfully": "<PERSON> p<PERSON><PERSON>", "confirmedSuccessfully": "<PERSON> p<PERSON><PERSON>"}, "validation": {"quantityLowerThanFulfillment": "Tidak dapat mengatur kuantitas menjadi kurang dari atau sama dengan kuantitas terpenuhi"}}, "edit": {"email": {"title": "Edit email", "requestSuccess": "Email pesanan diperbarui menjadi {{email}}."}, "shippingAddress": {"title": "<PERSON> <PERSON><PERSON><PERSON>", "requestSuccess": "<PERSON><PERSON><PERSON> pesanan <PERSON>."}, "billingAddress": {"title": "<PERSON> <PERSON><PERSON><PERSON>", "requestSuccess": "<PERSON><PERSON><PERSON> pesanan dip<PERSON>."}}, "returns": {"create": "<PERSON><PERSON><PERSON> Pen<PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmText": "<PERSON><PERSON> akan <PERSON>. Tindakan ini tidak dapat di<PERSON>an.", "inbound": "<PERSON><PERSON><PERSON>", "outbound": "<PERSON><PERSON><PERSON>", "sendNotification": "<PERSON><PERSON>", "sendNotificationHint": "<PERSON><PERSON><PERSON> pelanggan tentang pengembalian.", "returnTotal": "Total pengembalian", "inboundTotal": "Total masuk", "estDifference": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "outstandingAmount": "<PERSON><PERSON><PERSON>", "reason": "<PERSON><PERSON><PERSON>", "reasonHint": "Pilih alasan mengapa pelanggan ingin mengembalikan item.", "note": "Catatan", "noInventoryLevel": "Tidak ada level inventaris", "noInventoryLevelDesc": "Lokasi stok yang dipilih tidak memiliki level inventaris untuk item yang dipilih. Pengembalian dapat diminta tetapi tidak dapat diterima sampai level inventaris dibuat untuk lokasi yang dipilih.", "noteHint": "<PERSON>a dapat mengetik bebas jika ingin menentukan sesuatu.", "location": "<PERSON><PERSON>", "locationHint": "Pi<PERSON>h lokasi mana yang ingin Anda kembalikan item.", "inboundShipping": "Pengiri<PERSON> penge<PERSON>", "inboundShippingHint": "<PERSON><PERSON><PERSON> metode yang ingin <PERSON> gunakan.", "returnableQuantityLabel": "Kuantitas dapat dikembalikan", "refundableAmountLabel": "<PERSON><PERSON><PERSON>", "returnRequestedInfo": "{{requestedItemsCount}}x item diminta pengembalian", "returnReceivedInfo": "{{requestedItemsCount}}x item pengembalian diterima", "itemReceived": "<PERSON>em di<PERSON>ima", "returnRequested": "Pengemba<PERSON> diminta", "damagedItemReceived": "<PERSON>em rusak diterima", "damagedItemsReturned": "{{quantity}}x item rusak dikembalikan", "activeChangeError": "<PERSON> perubahan pesanan aktif yang sedang berlangsung pada pesanan ini. <PERSON><PERSON><PERSON><PERSON> atau batalkan perubahan terlebih dahulu.", "cancel": {"title": "Batalkan <PERSON>", "description": "Anda yakin ingin membatalkan permintaan pengembalian?"}, "placeholders": {"noReturnShippingOptions": {"title": "Tidak ada opsi pengiriman pengembalian di<PERSON>n", "hint": "Tidak ada opsi pengiriman pengembalian yang dibuat untuk lokasi ini. Anda dapat membuatnya di <LinkComponent>Lokasi & Pengiriman</LinkComponent>."}, "outboundShippingOptions": {"title": "Tidak ada opsi pengiriman keluar di<PERSON>n", "hint": "Tidak ada opsi pengiriman keluar yang dibuat untuk lokasi ini. Anda dapat membuatnya di <LinkComponent>Lokasi & Pengiriman</LinkComponent>."}}, "receive": {"action": "Terima item", "receiveItems": "{{ returnType }} {{ id }}", "restockAll": "Restok semua item", "itemsLabel": "<PERSON>em di<PERSON>ima", "title": "Terima item untuk #{{returnId}}", "sendNotificationHint": "<PERSON><PERSON><PERSON> pelanggan tentang pengembalian yang diterima.", "inventoryWarning": "Perhat<PERSON><PERSON> bahwa kami akan secara otomatis menyesuaikan tingkat inventaris berdasarkan input Anda di atas.", "writeOffInputLabel": "Berapa banyak item yang rusak?", "toast": {"success": "Pengembalian ber<PERSON>il diterima.", "errorLargeValue": "Kuantitas lebih besar dari kuantitas item yang diminta.", "errorNegativeValue": "Kuantitas tidak bisa negatif.", "errorLargeDamagedValue": "Kuantitas item rusak + kuantitas diterima tidak rusak melebihi total kuantitas item pada pengembalian. Kurangi kuantitas item tidak rusak."}}, "toast": {"canceledSuccessfully": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON>", "confirmedSuccessfully": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>"}, "panel": {"title": "<PERSON><PERSON><PERSON><PERSON> dimulai", "description": "<PERSON> permintaan pengembalian terbuka yang harus diselesaikan"}}, "claims": {"create": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmText": "<PERSON>a akan men<PERSON>. Tindakan ini tidak dapat dibatalkan.", "manage": "<PERSON><PERSON><PERSON>", "outbound": "<PERSON><PERSON><PERSON>", "outboundItemAdded": "{{itemsCount}}x ditambahkan melalui klaim", "outboundTotal": "Total keluar", "outboundShipping": "Pengiri<PERSON> keluar", "outboundShippingHint": "<PERSON><PERSON><PERSON> metode yang ingin <PERSON> gunakan.", "refundAmount": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "activeChangeError": "<PERSON> perubahan pesanan aktif pada pesanan ini. <PERSON><PERSON><PERSON><PERSON> atau buang perubahan sebelumnya.", "actions": {"cancelClaim": {"successToast": "<PERSON><PERSON><PERSON> <PERSON><PERSON>."}}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "description": "Anda yakin ingin membatalkan klaim?"}, "tooltips": {"onlyReturnShippingOptions": "Daftar ini hanya akan terdiri dari opsi pengiriman pengembalian."}, "toast": {"canceledSuccessfully": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "confirmedSuccessfully": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>"}, "panel": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON> permintaan klaim terbuka yang harus diselesaikan"}}, "exchanges": {"create": "<PERSON><PERSON><PERSON>", "manage": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmText": "<PERSON><PERSON> akan men<PERSON>. Tindakan ini tidak dapat di<PERSON>alkan.", "outbound": "<PERSON><PERSON><PERSON>", "outboundItemAdded": "{{itemsCount}}x ditambahkan melalui penukaran", "outboundTotal": "Total keluar", "outboundShipping": "Pengiri<PERSON> keluar", "outboundShippingHint": "<PERSON><PERSON><PERSON> metode yang ingin <PERSON> gunakan.", "refundAmount": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "activeChangeError": "<PERSON> perubahan pesanan aktif pada pesanan ini. <PERSON><PERSON><PERSON><PERSON> atau buang perubahan sebelumnya.", "actions": {"cancelExchange": {"successToast": "<PERSON><PERSON><PERSON>."}}, "cancel": {"title": "Batalk<PERSON>", "description": "Anda yakin ingin membatalkan penukaran?"}, "tooltips": {"onlyReturnShippingOptions": "Daftar ini hanya akan terdiri dari opsi pengiriman pengembalian."}, "toast": {"canceledSuccessfully": "<PERSON><PERSON><PERSON>", "confirmedSuccessfully": "<PERSON><PERSON><PERSON>"}, "panel": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON> permintaan penukaran terbuka yang harus diselesaikan"}}, "reservations": {"allocatedLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notAllocatedLabel": "Tidak dialokasikan"}, "allocateItems": {"action": "Alokasikan item", "title": "Alokasikan item pesanan", "locationDescription": "<PERSON><PERSON><PERSON> lokasi mana yang ingin <PERSON> al<PERSON>.", "itemsToAllocate": "<PERSON><PERSON> yang akan dial<PERSON>an", "itemsToAllocateDesc": "<PERSON><PERSON><PERSON> jumlah item yang ingin <PERSON>a al<PERSON>an", "search": "Cari item", "consistsOf": "<PERSON><PERSON>ri dari {{num}}x item inventaris", "requires": "Me<PERSON><PERSON>uhkan {{num}} per varian", "toast": {"created": "<PERSON><PERSON> be<PERSON><PERSON>"}, "error": {"quantityNotAllocated": "Ada item yang belum dialokasikan."}}, "shipment": {"title": "Tandai pengiriman terpenuhi", "trackingNumber": "Nomor pela<PERSON>", "addTracking": "Tambah nomor pelacakan", "sendNotification": "<PERSON><PERSON>", "sendNotificationHint": "<PERSON><PERSON><PERSON> pelanggan tentang pengiriman ini.", "toastCreated": "Pengiriman ber<PERSON>il dibuat."}, "fulfillment": {"cancelWarning": "<PERSON>a akan membatalkan pemenuhan. Tindakan ini tidak dapat dibatalkan.", "markAsDeliveredWarning": "<PERSON>a akan menandai pemenuhan sebagai terkirim. Tindakan ini tidak dapat dibatalkan.", "differentOptionSelected": "Opsi pengiriman yang dipilih berbeda dengan yang dipilih pelanggan.", "disabledItemTooltip": "Opsi pengiriman yang Anda pilih tidak mengizinkan pemenuhan item ini", "unfulfilledItems": "Item Bel<PERSON>i", "statusLabel": "Status pemenuhan", "statusTitle": "Status Pemenuhan", "fulfillItems": "Penuhi item", "awaitingFulfillmentBadge": "<PERSON><PERSON><PERSON> p<PERSON>an", "requiresShipping": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "number": "<PERSON><PERSON><PERSON><PERSON><PERSON> #{{number}}", "itemsToFulfill": "Item untuk dipenuhi", "create": "<PERSON><PERSON><PERSON>", "available": "Tersedia", "inStock": "Stok tersedia", "markAsShipped": "Tandai sebagai dikirim", "markAsPickedUp": "Tandai sebagai diambil", "markAsDelivered": "Tandai sebagai terkirim", "itemsToFulfillDesc": "Pilih item dan kuantitas untuk dipenuhi", "locationDescription": "Pilih lokasi mana yang ingin Anda penuhi item.", "sendNotificationHint": "<PERSON><PERSON><PERSON> pelanggan tentang pemenuhan yang dibuat.", "methodDescription": "<PERSON><PERSON><PERSON> metode pengiriman yang berbeda dari yang dipilih pelanggan", "error": {"wrongQuantity": "Hanya satu item yang tersedia untuk pemenuhan", "wrongQuantity_other": "Kuantitas harus berupa angka antara 1 dan {{number}}", "noItems": "Tidak ada item untuk dipenuhi.", "noShippingOption": "Opsi pengiriman wajib diisi", "noLocation": "<PERSON><PERSON> wajib diisi"}, "status": {"notFulfilled": "Belum terpen<PERSON>i", "partiallyFulfilled": "Sebagian terpenuhi", "fulfilled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "partiallyShipped": "Sebag<PERSON>", "shipped": "Di<PERSON><PERSON>", "delivered": "Terkirim", "partiallyDelivered": "Sebagian terkirim", "partiallyReturned": "Sebagian dikembalikan", "returned": "Di<PERSON><PERSON>lik<PERSON>", "canceled": "Di<PERSON><PERSON><PERSON>", "requiresAction": "Memb<PERSON><PERSON><PERSON> tin<PERSON>an"}, "toast": {"created": "<PERSON><PERSON><PERSON><PERSON><PERSON> ber<PERSON> dibuat", "canceled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fulfillmentShipped": "Tidak dapat membatalkan pemenuhan yang sudah dikirim", "fulfillmentDelivered": "<PERSON><PERSON><PERSON><PERSON><PERSON> ber<PERSON>il ditandai sebagai terkirim", "fulfillmentPickedUp": "<PERSON><PERSON><PERSON><PERSON><PERSON> ber<PERSON>il ditandai sebagai diambil"}, "trackingLabel": "Pelacakan", "shippingFromLabel": "<PERSON><PERSON><PERSON> da<PERSON>", "itemsLabel": "<PERSON><PERSON>"}, "refund": {"title": "<PERSON><PERSON><PERSON>", "sendNotificationHint": "<PERSON><PERSON><PERSON> pelanggan tentang pengembalian dana yang dibuat.", "systemPayment": "Pembayaran sistem", "systemPaymentDesc": "<PERSON>tu atau lebih pembayaran Anda adalah pembayaran sistem. <PERSON><PERSON>, pengambilan dan pengembalian dana tidak ditangani oleh Medusa untuk pembayaran tersebut.", "error": {"amountToLarge": "Tidak dapat mengembalikan dana lebih dari jumlah pesanan awal.", "amountNegative": "<PERSON><PERSON><PERSON> pen<PERSON>lian dana harus berupa angka positif.", "reasonRequired": "<PERSON><PERSON><PERSON> alasan pengembalian dana."}}, "customer": {"contactLabel": "Kontak", "editEmail": "Edit email", "transferOwnership": "Transfer kepemilikan", "editBillingAddress": "<PERSON> <PERSON><PERSON><PERSON>", "editShippingAddress": "<PERSON> <PERSON><PERSON><PERSON>"}, "activity": {"header": "Aktivitas", "showMoreActivities_one": "<PERSON><PERSON><PERSON><PERSON> {{count}} aktivitas lagi", "showMoreActivities_other": "<PERSON><PERSON><PERSON><PERSON> {{count}} aktivitas lagi", "comment": {"label": "Komentar", "placeholder": "Tinggalkan komentar", "addButtonText": "Tambah komentar", "deleteButtonText": "<PERSON><PERSON> komentar"}, "from": "<PERSON><PERSON>", "to": "<PERSON><PERSON><PERSON>", "events": {"common": {"toReturn": "Untuk dikembalikan", "toSend": "Untuk dikirim"}, "placed": {"title": "<PERSON><PERSON><PERSON>", "fromSalesChannel": "dari {{salesChannel}}"}, "canceled": {"title": "<PERSON><PERSON><PERSON>"}, "payment": {"awaiting": "<PERSON><PERSON><PERSON> p<PERSON>", "captured": "Pembayaran diambil", "canceled": "Pembayaran dibatalkan", "refunded": "Pembayaran dikembalikan"}, "fulfillment": {"created": "<PERSON><PERSON>", "canceled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shipped": "<PERSON><PERSON>", "delivered": "<PERSON><PERSON> terk<PERSON>m", "items_one": "{{count}} item", "items_other": "{{count}} item"}, "return": {"created": "Pengembalian #{{returnId}} diminta", "canceled": "Pengembalian #{{returnId}} dibat<PERSON>an", "received": "Pengembalian #{{returnId}} diterima", "items_one": "{{count}} item dikembalikan", "items_other": "{{count}} item dikembalikan"}, "note": {"comment": "Komentar", "byLine": "oleh {{author}}"}, "claim": {"created": "Klaim #{{claimId}} diminta", "canceled": "Klaim #{{claimId}} di<PERSON><PERSON><PERSON>", "itemsInbound": "{{count}} item untuk dikembalikan", "itemsOutbound": "{{count}} item untuk dikirim"}, "exchange": {"created": "Penukaran #{{exchangeId}} diminta", "canceled": "Penukaran #{{exchangeId}} dibat<PERSON><PERSON>", "itemsInbound": "{{count}} item untuk dikembalikan", "itemsOutbound": "{{count}} item untuk dikirim"}, "edit": {"requested": "Edit pesanan #{{editId}} diminta", "confirmed": "Edit pesanan #{{editId}} dikon<PERSON><PERSON><PERSON>"}, "transfer": {"requested": "Transfer pesanan #{{transferId}} diminta", "confirmed": "Transfer pesanan #{{transferId}} dikonfirmasi", "declined": "Transfer pesanan #{{transferId}} ditolak"}, "update_order": {"shipping_address": "<PERSON><PERSON><PERSON>", "billing_address": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON>"}}}, "fields": {"displayId": "ID Tampilan", "refundableAmount": "<PERSON><PERSON><PERSON>", "returnableQuantity": "Kuantitas dapat dikembalikan"}}, "draftOrders": {"domain": "<PERSON><PERSON>", "deleteWarning": "<PERSON><PERSON> akan mengh<PERSON>us draf p<PERSON>an {{id}}. Tindakan ini tidak dapat di<PERSON>an.", "paymentLinkLabel": "<PERSON><PERSON>", "cartIdLabel": "ID Keranjang", "markAsPaid": {"label": "Tandai sebagai dibayar", "warningTitle": "Tandai sebagai Dibayar", "warningDescription": "<PERSON>a akan menandai draf pesanan sebagai dibayar. <PERSON>dakan ini tidak dapat di<PERSON>, dan menagih pembayaran tidak akan mungkin dilakukan nanti."}, "status": {"open": "<PERSON><PERSON>", "completed": "Se<PERSON><PERSON>"}, "create": {"createDraftOrder": "<PERSON><PERSON><PERSON> Draf <PERSON>", "createDraftOrderHint": "Buat draf pesanan baru untuk mengelola detail pesanan sebelum ditempatkan.", "chooseRegionHint": "<PERSON><PERSON><PERSON> wilayah", "existingItemsLabel": "<PERSON>em yang sudah ada", "existingItemsHint": "Tambah produk yang sudah ada ke draf pesanan.", "customItemsLabel": "<PERSON><PERSON> kustom", "customItemsHint": "Tambah item kustom ke draf pesanan.", "addExistingItemsAction": "Tambah item yang sudah ada", "addCustomItemAction": "Tambah item kustom", "noCustomItemsAddedLabel": "Belum ada item kustom ditambahkan", "noExistingItemsAddedLabel": "Belum ada item yang sudah ada ditambahkan", "chooseRegionTooltip": "<PERSON><PERSON><PERSON> wilayah terlebih dahulu", "useExistingCustomerLabel": "Gunakan pelanggan yang sudah ada", "addShippingMethodsAction": "Tambah metode pengiriman", "unitPriceOverrideLabel": "<PERSON><PERSON> harga satuan", "shippingOptionLabel": "<PERSON><PERSON>", "shippingOptionHint": "<PERSON><PERSON><PERSON> opsi pengiriman untuk draf pesanan.", "shippingPriceOverrideLabel": "<PERSON><PERSON> harga pen<PERSON>", "shippingPriceOverrideHint": "<PERSON><PERSON> harga pengiriman untuk draf p<PERSON>an.", "sendNotificationLabel": "<PERSON><PERSON>", "sendNotificationHint": "<PERSON><PERSON> ke pelanggan saat draf pesanan dibuat."}, "validation": {"requiredEmailOrCustomer": "Email atau pelanggan wajib diisi.", "requiredItems": "Setidaknya satu item wajib diisi.", "invalidEmail": "Email harus berupa alamat email yang valid."}}, "stockLocations": {"domain": "Lokasi & Pengiriman", "list": {"description": "<PERSON><PERSON>la lokasi stok dan opsi pengiriman toko <PERSON>."}, "create": {"header": "Buat Lokasi Stok", "hint": "Lokasi stok adalah situs fisik tempat produk disimpan dan dikirim.", "successToast": "Lokasi {{name}} berhasil dibuat."}, "edit": {"header": "<PERSON>", "viewInventory": "<PERSON><PERSON> inventaris", "successToast": "<PERSON><PERSON> {{name}} ber<PERSON><PERSON> diperbar<PERSON>."}, "delete": {"confirmation": "<PERSON>a akan menghapus lokasi stok \"{{name}}\". Tindakan ini tidak dapat dibatalkan."}, "fulfillmentProviders": {"header": "<PERSON><PERSON><PERSON>", "shippingOptionsTooltip": "Dropdown ini hanya akan berisi penyedia yang diaktifkan untuk lokasi ini. Tambahkan mereka ke lokasi jika dropdown dinonaktifkan.", "label": "<PERSON><PERSON><PERSON> pemen<PERSON>an ter<PERSON>", "connectedTo": "Te<PERSON>hu<PERSON>ng ke {{count}} dari {{total}} pen<PERSON><PERSON> pemen<PERSON>an", "noProviders": "Lokasi Stok ini tidak terhubung ke penyedia pemenuhan mana pun.", "action": "Hubungkan Penyedia", "successToast": "Penyedia pemenuhan untuk lokasi stok berhasil diperbarui."}, "fulfillmentSets": {"pickup": {"header": "Ambil"}, "shipping": {"header": "Pen<PERSON><PERSON>"}, "disable": {"confirmation": "Anda yakin ingin menonakt<PERSON> \"{{name}}\"? Ini akan menghapus semua zona layanan dan opsi pengiriman terkait, dan tidak dapat di<PERSON>an.", "pickup": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON><PERSON>.", "shipping": "Pengiri<PERSON> be<PERSON><PERSON><PERSON>."}, "enable": {"pickup": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON>.", "shipping": "Pengiri<PERSON> be<PERSON><PERSON><PERSON>."}}, "sidebar": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shippingProfiles": {"label": "<PERSON><PERSON>", "description": "Kelompokkan produk berdasarkan persyaratan pengiriman"}}, "salesChannels": {"header": "<PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON> saluran penjualan yang terhubung ke lokasi ini.", "label": "<PERSON><PERSON>n pen<PERSON>alan terhubung", "connectedTo": "Terhu<PERSON>ng ke {{count}} dari {{total}} saluran penjualan", "noChannels": "Lokasi tidak terhubung ke saluran penjualan mana pun.", "action": "Hubungkan saluran penjualan", "successToast": "<PERSON><PERSON>n pen<PERSON> ber<PERSON><PERSON> diper<PERSON>."}, "pickupOptions": {"edit": {"header": "<PERSON> <PERSON><PERSON>"}}, "shippingOptions": {"create": {"shipping": {"header": "Buat Opsi Pengiriman untuk {{zone}}", "hint": "Buat opsi pengiriman baru untuk menentukan cara produk dikirim dari lokasi ini.", "label": "<PERSON><PERSON>", "successToast": "Opsi pengiriman {{name}} berhasil dibuat."}, "pickup": {"header": "Buat Opsi Pengambilan untuk {{zone}}", "hint": "Buat opsi pengambilan baru untuk menentukan cara produk diambil dari lokasi ini.", "label": "<PERSON><PERSON> pen<PERSON>", "successToast": "Opsi pengambilan {{name}} berhasil dibuat."}, "returns": {"header": "Buat Opsi Pengembalian untuk {{zone}}", "hint": "Buat opsi pengembalian baru untuk menentukan cara produk dikembalikan ke lokasi ini.", "label": "<PERSON><PERSON> pen<PERSON>lian", "successToast": "Opsi pengembalian {{name}} berhasil dibuat."}, "tabs": {"details": "Detail", "prices": "<PERSON><PERSON>"}, "action": "Buat opsi"}, "delete": {"confirmation": "<PERSON><PERSON> akan menghapus opsi pengiriman \"{{name}}\". Tindakan ini tidak dapat dibatalkan.", "successToast": "<PERSON><PERSON> pengiriman {{name}} ber<PERSON><PERSON> di<PERSON>."}, "edit": {"header": "<PERSON> <PERSON><PERSON>", "action": "Edit opsi", "successToast": "<PERSON><PERSON> pengiriman {{name}} ber<PERSON><PERSON> diperbarui."}, "pricing": {"action": "Edit harga"}, "conditionalPrices": {"header": "<PERSON><PERSON> untuk {{name}}", "description": "<PERSON><PERSON>la harga bersyarat untuk opsi pengiriman ini berdasarkan total item keranjang.", "attributes": {"cartItemTotal": "Total item keranjang"}, "summaries": {"range": "Jika <0>{{attribute}}</0> antara <1>{{gte}}</1> dan <2>{{lte}}</2>", "greaterThan": "Jika <0>{{attribute}}</0> ≥ <1>{{gte}}</1>", "lessThan": "Jika <0>{{attribute}}</0> ≤ <1>{{lte}}</1>"}, "actions": {"addPrice": "Tambah harga", "manageConditionalPrices": "<PERSON><PERSON><PERSON> harga be<PERSON>"}, "rules": {"amount": "<PERSON><PERSON> opsi pen<PERSON>", "gte": "Total item keranjang minimum", "lte": "Total item keranjang maksimum"}, "customRules": {"label": "Aturan kustom", "tooltip": "<PERSON>rga bersyarat ini memiliki aturan yang tidak dapat dikelola di dasbor.", "eq": "Total item keranjang harus sama dengan", "gt": "Total item keranjang harus lebih besar dari", "lt": "Total item keranjang harus kurang dari"}, "errors": {"amountRequired": "<PERSON><PERSON> opsi pengiriman wajib diisi", "minOrMaxRequired": "Setidaknya salah satu dari total item keranjang minimum atau maksimum harus disediakan", "minGreaterThanMax": "Total item keranjang minimum harus kurang dari atau sama dengan total item keranjang maksimum", "duplicateAmount": "Harga opsi pengiriman harus unik untuk setiap kondisi", "overlappingConditions": "<PERSON><PERSON><PERSON> harus unik di semua aturan harga"}}, "fields": {"count": {"shipping_one": "{{count}} opsi pen<PERSON>man", "shipping_other": "{{count}} opsi pen<PERSON>man", "pickup_one": "{{count}} opsi pengambilan", "pickup_other": "{{count}} opsi pengambilan", "returns_one": "{{count}} opsi pengembalian", "returns_other": "{{count}} opsi pengembalian"}, "priceType": {"label": "<PERSON><PERSON> ha<PERSON>", "options": {"fixed": {"label": "Tetap", "hint": "<PERSON>rga opsi pengiriman ditetapkan dan tidak berubah berdasarkan isi pesanan."}, "calculated": {"label": "<PERSON><PERSON><PERSON>", "hint": "<PERSON>rga opsi pengiriman dihitung oleh penyedia pemenuhan selama checkout."}}}, "enableInStore": {"label": "Aktifkan di toko", "hint": "Apakah pelanggan dapat menggunakan opsi ini saat checkout."}, "provider": "<PERSON><PERSON><PERSON> p<PERSON>", "profile": "<PERSON><PERSON>", "fulfillmentOption": "<PERSON><PERSON> pem<PERSON>an"}}, "serviceZones": {"create": {"headerPickup": "Buat Zona Layanan untuk Pengambilan dari {{location}}", "headerShipping": "Buat Zona Layanan untuk Pengiriman dari {{location}}", "action": "Buat zona layanan", "successToast": "Zona layanan {{name}} berhasil dibuat."}, "edit": {"header": "<PERSON> <PERSON>", "successToast": "<PERSON><PERSON> layanan {{name}} ber<PERSON><PERSON> diperbarui."}, "delete": {"confirmation": "<PERSON>a akan menghapus zona layanan \"{{name}}\". Tindakan ini tidak dapat dibatalkan.", "successToast": "<PERSON><PERSON> layanan {{name}} ber<PERSON><PERSON> di<PERSON>."}, "manageAreas": {"header": "Kelola Area untuk {{name}}", "action": "Kelola area", "label": "Area", "hint": "Pilih area geografis yang dicakup oleh zona layanan.", "successToast": "Area untuk {{name}} berhasil diperbarui."}, "fields": {"noRecords": "Tidak ada zona layanan untuk ditambahkan opsi pengiriman.", "tip": "Zona layanan adalah kumpulan zona atau area geografis. Ini digunakan untuk membatasi opsi pengiriman yang tersedia untuk sekumpulan lokasi yang ditentukan."}}}, "shippingProfile": {"domain": "<PERSON><PERSON>", "subtitle": "Kelompokkan produk dengan persyaratan pengiriman yang serupa ke dalam profil.", "create": {"header": "Buat <PERSON><PERSON>", "hint": "Buat profil pengiriman baru untuk mengelompokkan produk dengan persyaratan pengiriman yang serupa.", "successToast": "Profil pen<PERSON>man {{name}} berhasil dibuat."}, "delete": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> akan mengh<PERSON>us profil pen<PERSON>man {{name}}. Tindakan ini tidak dapat dibatalkan.", "successToast": "<PERSON>il pen<PERSON> {{name}} ber<PERSON><PERSON> di<PERSON>."}, "tooltip": {"type": "<PERSON><PERSON><PERSON><PERSON> jenis profil <PERSON>, conto<PERSON>: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, dll."}}, "taxRegions": {"domain": "<PERSON><PERSON><PERSON>", "list": {"hint": "<PERSON><PERSON><PERSON> apa yang Anda tagih kepada pelanggan Anda saat mereka berbelanja dari berbagai negara dan wilayah."}, "delete": {"confirmation": "<PERSON>a akan menghapus wilayah pajak. Tindakan ini tidak dapat dibatalkan.", "successToast": "<PERSON><PERSON><PERSON> pajak ber<PERSON><PERSON>."}, "create": {"header": "Buat Wilayah Pajak", "hint": "Buat wilayah pajak baru untuk menentukan tarif pajak untuk negara tertentu.", "errors": {"missingProvider": "Penyedia wajib diisi saat membuat wilayah pajak.", "missingCountry": "Negara wajib diisi saat membuat wilayah pajak."}, "successToast": "Wilayah pajak berhasil dibuat."}, "edit": {"header": "<PERSON> <PERSON><PERSON><PERSON>", "hint": "Edit detail wilayah pajak.", "successToast": "<PERSON>ila<PERSON> pajak ber<PERSON><PERSON> dip<PERSON>."}, "province": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "Buat Wilayah Pa<PERSON>", "hint": "Buat wilayah pajak baru untuk menentukan tarif pajak untuk provinsi tertentu."}}, "provider": {"header": "<PERSON><PERSON><PERSON>"}, "state": {"header": "Negara Bagian", "create": {"header": "Buat Wilayah Pajak Negara Bagian", "hint": "Buat wilayah pajak baru untuk menentukan tarif pajak untuk negara bagian tertentu."}}, "stateOrTerritory": {"header": "Negara Bagian atau Teritori", "create": {"header": "Buat Wilayah Pajak Negara Bagian/Teritori", "hint": "Buat wilayah pajak baru untuk menentukan tarif pajak untuk negara bagian/teritori tertentu."}}, "county": {"header": "County", "create": {"header": "Buat Wilayah Pajak County", "hint": "Buat wilayah pajak baru untuk menentukan tarif pajak untuk county tertentu."}}, "region": {"header": "Wilayah", "create": {"header": "Buat Wilayah Pajak", "hint": "Buat wilayah pajak baru untuk menentukan tarif pajak untuk wilayah tertentu."}}, "department": {"header": "Departemen", "create": {"header": "Buat Wilayah Pajak Departemen", "hint": "Buat wilayah pajak baru untuk menentukan tarif pajak untuk departemen tertentu."}}, "territory": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "Buat Wilayah Pajak Teritori", "hint": "Buat wilayah pajak baru untuk menentukan tarif pajak untuk teritori tertentu."}}, "prefecture": {"header": "Prefektur", "create": {"header": "Buat Wilayah Pajak Prefektur", "hint": "Buat wilayah pajak baru untuk menentukan tarif pajak untuk prefektur tertentu."}}, "district": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "Buat Wilayah Pajak Distrik", "hint": "Buat wilayah pajak baru untuk menentukan tarif pajak untuk distrik tertentu."}}, "governorate": {"header": "Governorat", "create": {"header": "Buat Wilayah Pajak Governorat", "hint": "Buat wilayah pajak baru untuk menentukan tarif pajak untuk governorat tertentu."}}, "canton": {"header": "<PERSON><PERSON>", "create": {"header": "Buat Wilayah Pajak Kanton", "hint": "Buat wilayah pajak baru untuk menentukan tarif pajak untuk kanton tertentu."}}, "emirate": {"header": "Emirat", "create": {"header": "Buat Wilayah Pajak Emirat", "hint": "Buat wilayah pajak baru untuk menentukan tarif pajak untuk emirat tertentu."}}, "sublevel": {"header": "Sublevel", "create": {"header": "Buat Wilayah Pajak Sublevel", "hint": "Buat wilayah pajak baru untuk menentukan tarif pajak untuk sublevel tertentu."}}, "taxOverrides": {"header": "Pengabaian", "create": {"header": "Buat Pengabaian", "hint": "Buat tarif pajak yang mengabaikan tarif pajak default untuk kondisi yang dipilih."}, "edit": {"header": "<PERSON>", "hint": "Edit tarif pajak yang mengabaikan tarif pajak default untuk kondisi yang dipilih."}}, "taxRates": {"create": {"header": "<PERSON><PERSON><PERSON>", "hint": "Buat tarif pajak baru untuk menentukan tarif pajak untuk wilayah.", "successToast": "<PERSON><PERSON><PERSON> pajak ber<PERSON>il dibuat."}, "edit": {"header": "<PERSON> <PERSON><PERSON><PERSON>", "hint": "Edit tarif pajak untuk menentukan tarif pajak untuk wilayah.", "successToast": "<PERSON><PERSON><PERSON> pajak ber<PERSON><PERSON>."}, "delete": {"confirmation": "<PERSON><PERSON> akan menghapus tarif pajak \"{{name}}\". Tindakan ini tidak dapat dibatalkan.", "successToast": "<PERSON><PERSON><PERSON> p<PERSON> be<PERSON><PERSON><PERSON>."}}, "fields": {"isCombinable": {"label": "Dapat Digabung", "hint": "<PERSON><PERSON><PERSON><PERSON> tarif pajak ini dapat digabungkan dengan tarif default dari wilayah pajak.", "true": "Dapat Digabung", "false": "Tidak Dapat Digabung"}, "defaultTaxRate": {"label": "<PERSON><PERSON><PERSON> p<PERSON> default", "tooltip": "Tarif pajak default untuk wilayah ini. Contohnya adalah tarif PPN standar untuk negara atau wilayah.", "action": "Buat tarif pajak default"}, "taxRate": "<PERSON><PERSON><PERSON>", "taxCode": "<PERSON><PERSON> pajak", "taxProvider": "<PERSON><PERSON><PERSON> pajak", "targets": {"label": "Target", "hint": "Pilih target yang akan diterapkan tarif pajak ini.", "options": {"product": "Produk", "productCollection": "<PERSON><PERSON><PERSON><PERSON> produk", "productTag": "Tag produk", "productType": "<PERSON><PERSON> produk", "customerGroup": "Grup pelanggan"}, "operators": {"in": "dalam", "on": "pada", "and": "dan"}, "placeholders": {"product": "<PERSON><PERSON> produk", "productCollection": "<PERSON>i koleksi produk", "productTag": "Cari tag produk", "productType": "<PERSON><PERSON> jenis produk", "customerGroup": "Cari grup pelanggan"}, "tags": {"product": "Produk", "productCollection": "<PERSON><PERSON><PERSON><PERSON> produk", "productTag": "Tag produk", "productType": "<PERSON><PERSON> produk", "customerGroup": "Grup pelanggan"}, "modal": {"header": "Tambah target"}, "values_one": "{{count}} nilai", "values_other": "{{count}} nilai", "numberOfTargets_one": "{{count}} target", "numberOfTargets_other": "{{count}} target", "additionalValues_one": "dan {{count}} nilai la<PERSON>ya", "additionalValues_other": "dan {{count}} nilai la<PERSON>ya", "action": "Tambah target"}, "sublevels": {"labels": {"province": "<PERSON><PERSON><PERSON>", "state": "Negara Bagian", "region": "Wilayah", "stateOrTerritory": "Negara Bagian/Teritori", "department": "Departemen", "county": "County", "territory": "<PERSON><PERSON><PERSON>", "prefecture": "Prefektur", "district": "<PERSON><PERSON><PERSON>", "governorate": "Governorat", "emirate": "Emirat", "canton": "<PERSON><PERSON>", "sublevel": "Kode sublevel"}, "placeholders": {"province": "<PERSON><PERSON><PERSON>", "state": "<PERSON><PERSON><PERSON> negara bagian", "region": "<PERSON><PERSON><PERSON> wilayah", "stateOrTerritory": "<PERSON><PERSON><PERSON> negara bagian/teritori", "department": "<PERSON><PERSON><PERSON> depart<PERSON>en", "county": "Pilih county", "territory": "<PERSON><PERSON><PERSON> teritori", "prefecture": "<PERSON><PERSON><PERSON> prefektur", "district": "<PERSON><PERSON><PERSON> distrik", "governorate": "Pilih governorat", "emirate": "Pilih emirat", "canton": "<PERSON><PERSON><PERSON>"}, "tooltips": {"sublevel": "Masukkan kode ISO 3166-2 untuk wilayah pajak sublevel.", "notPartOfCountry": "{{province}} tampaknya bukan bagian dari {{country}}. <PERSON>hon periksa kembali apakah ini benar."}, "alert": {"header": "Wilayah sublevel dinonaktifkan untuk wilayah pajak ini", "description": "Wilayah sublevel dinonaktifkan untuk wilayah ini secara default. Anda dapat mengaktifkannya untuk membuat wilayah sublevel seperti provinsi, negara bagian, atau teritori.", "action": "Aktifkan wilayah sublevel"}}, "noDefaultRate": {"label": "Tidak ada tarif default", "tooltip": "Wilayah pajak ini tidak memiliki tarif pajak default. <PERSON><PERSON> ada tarif standar, seperti PPN negara, tambahkan ke wilayah ini."}}}, "promotions": {"domain": "<PERSON><PERSON><PERSON>", "sections": {"details": "Detail Promosi"}, "tabs": {"template": "<PERSON><PERSON>", "details": "Detail", "campaign": "Kampanye"}, "fields": {"type": "<PERSON><PERSON>", "value_type": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "campaign": "Kampanye", "method": "Metode", "allocation": "Alokasi", "addCondition": "Tambah kondisi", "clearAll": "<PERSON><PERSON><PERSON><PERSON> semua", "amount": {"tooltip": "<PERSON><PERSON>h kode mata uang untuk mengaktifkan pengaturan jumlah"}, "conditions": {"rules": {"title": "Siapa yang dapat menggunakan kode ini?", "description": "Pelanggan mana yang diizinkan menggunakan kode promosi? Kode promosi dapat digunakan oleh semua pelanggan jika dibiarkan apa adanya."}, "target-rules": {"title": "Item apa yang akan dikenakan promosi?", "description": "Promosi akan diterapkan pada item yang sesuai dengan kondisi berikut."}, "buy-rules": {"title": "Apa yang harus ada di keranjang untuk membuka promosi?", "description": "<PERSON><PERSON> kondisi ini cocok, kami mengaktifkan promosi pada item target."}}}, "tooltips": {"campaignType": "Kode mata uang harus dipilih dalam promosi untuk menetapkan anggaran belanja."}, "errors": {"requiredField": "<PERSON><PERSON><PERSON> waji<PERSON>", "promotionTabError": "Perbaiki kesalahan di Tab Promosi sebelum melanjutkan"}, "toasts": {"promotionCreateSuccess": "Promosi ({{code}}) berhasil dibuat."}, "create": {}, "edit": {"title": "Edit Detail <PERSON>", "rules": {"title": "<PERSON> kond<PERSON> pen<PERSON>"}, "target-rules": {"title": "Edit kondisi item"}, "buy-rules": {"title": "Edit aturan beli"}}, "campaign": {"header": "Kampanye", "edit": {"header": "<PERSON>", "successToast": "<PERSON><PERSON><PERSON><PERSON> memperbarui kampanye promosi."}, "actions": {"goToCampaign": "<PERSON>gi ke kampanye"}}, "campaign_currency": {"tooltip": "Ini adalah mata uang promosi. Ubah dari tab Detail."}, "form": {"required": "<PERSON><PERSON><PERSON>", "and": "DAN", "selectAttribute": "<PERSON><PERSON><PERSON>", "campaign": {"existing": {"title": "<PERSON><PERSON><PERSON><PERSON> yang <PERSON>", "description": "Tambah promosi ke kampanye yang sudah ada.", "placeholder": {"title": "Kampanye tidak ada", "desc": "Anda dapat membuat satu untuk melacak beberapa promosi dan menetapkan batas anggaran."}}, "new": {"title": "Kampanye Baru", "description": "Buat kampanye baru untuk promosi ini."}, "none": {"title": "Tan<PERSON> Kampanye", "description": "Lanjutkan tanpa mengaitkan promosi dengan kampanye"}}, "status": {"label": "Status", "draft": {"title": "Draf", "description": "Pelanggan belum dapat menggunakan kode"}, "active": {"title": "Aktif", "description": "Pelanggan akan dapat menggunakan kode"}, "inactive": {"title": "Tidak Aktif", "description": "Pelanggan tidak lagi dapat menggunakan kode"}}, "method": {"label": "Metode", "code": {"title": "Ko<PERSON> promosi", "description": "Pelanggan harus memasukkan kode ini saat checkout"}, "automatic": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Pelanggan akan melihat promosi ini saat checkout"}}, "max_quantity": {"title": "Kuantitas Maksimum", "description": "Kuantitas maksimum item yang berlaku untuk promosi ini."}, "type": {"standard": {"title": "<PERSON>ar", "description": "<PERSON><PERSON><PERSON> standar"}, "buyget": {"title": "<PERSON><PERSON>", "description": "Promosi Beli X dapat Y"}}, "allocation": {"each": {"title": "Set<PERSON><PERSON>", "description": "Terapkan nilai pada setiap item"}, "across": {"title": "Di <PERSON>", "description": "Terapkan nilai di seluruh item"}}, "code": {"title": "<PERSON><PERSON>", "description": "Kode yang akan dimasukkan pelanggan Anda saat checkout."}, "value": {"title": "<PERSON><PERSON>"}, "value_type": {"fixed": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> yang akan didiskon. contoh: 100"}, "percentage": {"title": "Persentase", "description": "Persentase diskon dari jumlah. contoh: 8%"}}}, "deleteWarning": "<PERSON>a akan menghapus promosi {{code}}. Tindakan ini tidak dapat di<PERSON>an.", "createPromotionTitle": "Buat Promosi", "type": "<PERSON><PERSON>", "conditions": {"add": "Tambah kondisi", "list": {"noRecordsMessage": "Tambah kondisi untuk membatasi item apa yang berlaku untuk promosi."}}}, "campaigns": {"domain": "Kampanye", "details": "Detail kampanye", "status": {"active": "Aktif", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scheduled": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "delete": {"title": "Anda yakin?", "description": "<PERSON>a akan menghapus kampanye '{{name}}'. Tindakan ini tidak dapat di<PERSON>an.", "successToast": "<PERSON><PERSON><PERSON><PERSON> '{{name}}' ber<PERSON>il dibuat."}, "edit": {"header": "<PERSON>", "description": "Edit detail kampany<PERSON>.", "successToast": "<PERSON><PERSON><PERSON><PERSON> '{{name}}' ber<PERSON><PERSON> diperbarui."}, "configuration": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": {"header": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON> kon<PERSON><PERSON><PERSON><PERSON>.", "successToast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kamp<PERSON>e ber<PERSON>."}}, "create": {"title": "Buat Kampanye", "description": "Buat kampanye promosi.", "hint": "Buat kampanye promosi.", "header": "Buat Kampanye", "successToast": "<PERSON><PERSON><PERSON><PERSON> '{{name}}' ber<PERSON>il dibuat."}, "fields": {"name": "<PERSON><PERSON>", "identifier": "Identifier", "start_date": "<PERSON><PERSON> mulai", "end_date": "<PERSON><PERSON> akhir", "total_spend": "Anggaran terpakai", "total_used": "<PERSON><PERSON><PERSON>", "budget_limit": "<PERSON><PERSON> an<PERSON>", "campaign_id": {"hint": "<PERSON><PERSON> kampanye dengan kode mata uang yang sama dengan promosi yang ditampilkan dalam daftar ini."}}, "budget": {"create": {"hint": "B<PERSON>t anggaran untuk kampanye.", "header": "<PERSON><PERSON><PERSON>"}, "details": "Anggara<PERSON> kampanye", "fields": {"type": "<PERSON><PERSON>", "currency": "<PERSON>", "limit": "Batas", "used": "<PERSON><PERSON><PERSON><PERSON>"}, "type": {"spend": {"title": "Bel<PERSON><PERSON>", "description": "Tetapkan batas pada total jumlah diskon dari semua penggunaan promosi."}, "usage": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Tetapkan batas berapa kali promosi dapat digunakan."}}, "edit": {"header": "<PERSON> <PERSON><PERSON>"}}, "promotions": {"remove": {"title": "Hapus promosi dari kampanye", "description": "<PERSON>a akan mengh<PERSON> {{count}} promosi dari kampanye. Tindakan ini tidak dapat dibatalkan."}, "alreadyAdded": "Promosi ini sudah ditambahkan ke kampanye.", "alreadyAddedDiffCampaign": "Promosi ini sudah ditambahkan ke kampanye lain ({{name}}).", "currencyMismatch": "<PERSON> uang promosi dan kampanye tidak cocok", "toast": {"success": "<PERSON><PERSON><PERSON><PERSON> {{count}} promosi ke kampanye"}, "add": {"list": {"noRecordsMessage": "Buat promosi terle<PERSON>h da<PERSON>."}}, "list": {"noRecordsMessage": "Tidak ada promosi dalam kampanye."}}, "deleteCampaignWarning": "<PERSON><PERSON> akan menghap<PERSON> kampanye {{name}}. Tindakan ini tidak dapat dibat<PERSON>an.", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON>uat harga penjualan atau timpa harga untuk kondisi tertentu.", "delete": {"confirmation": "<PERSON><PERSON> akan menghapus daftar harga \"{{title}}\". Tindakan ini tidak dapat di<PERSON>an.", "successToast": "Daftar harga \"{{title}}\" ber<PERSON><PERSON> dihapus."}, "create": {"header": "<PERSON><PERSON><PERSON>", "subheader": "Buat daftar harga baru untuk mengelola harga produk Anda.", "tabs": {"details": "Detail", "products": "Produk", "prices": "<PERSON><PERSON>"}, "successToast": "<PERSON><PERSON>ar harga {{title}} berhasil dibuat.", "products": {"list": {"noRecordsMessage": "Buat produk terlebih da<PERSON>u."}}}, "edit": {"header": "<PERSON> <PERSON><PERSON><PERSON>", "successToast": "<PERSON><PERSON>ar harga {{title}} ber<PERSON><PERSON> diperbarui."}, "configuration": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": {"header": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON> kon<PERSON><PERSON><PERSON>i daftar harga.", "successToast": "<PERSON>n<PERSON><PERSON><PERSON><PERSON> daftar harga ber<PERSON><PERSON>."}}, "products": {"header": "Produk", "actions": {"addProducts": "Tambah produk", "editPrices": "Edit harga"}, "delete": {"confirmation_one": "<PERSON>a akan menghapus harga untuk {{count}} produk dalam daftar harga. Tindakan ini tidak dapat dibatalkan.", "confirmation_other": "<PERSON>a akan menghapus harga untuk {{count}} produk dalam daftar harga. Tindakan ini tidak dapat dibatalkan.", "successToast_one": "<PERSON><PERSON><PERSON><PERSON><PERSON>us harga untuk {{count}} produk.", "successToast_other": "<PERSON><PERSON><PERSON><PERSON><PERSON>us harga untuk {{count}} produk."}, "add": {"successToast": "<PERSON>rga ber<PERSON>il ditambahkan ke daftar harga."}, "edit": {"successToast": "<PERSON><PERSON> be<PERSON><PERSON><PERSON>."}}, "fields": {"priceOverrides": {"label": "<PERSON><PERSON> harga", "header": "<PERSON><PERSON>"}, "status": {"label": "Status", "options": {"active": "Aktif", "draft": "Draf", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scheduled": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "type": {"label": "<PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON> jenis daftar harga yang ingin <PERSON>a buat.", "options": {"sale": {"label": "Obral", "description": "Harga obral adalah per<PERSON>han harga sementara untuk produk."}, "override": {"label": "<PERSON><PERSON>", "description": "<PERSON><PERSON>a digunakan untuk membuat harga khusus pelanggan."}}}, "startsAt": {"label": "Daftar harga memiliki tanggal mulai?", "hint": "<PERSON><PERSON><PERSON><PERSON> daftar harga untuk aktif di masa mendatang."}, "endsAt": {"label": "Daftar harga memiliki tanggal kedalu<PERSON>sa?", "hint": "<PERSON><PERSON><PERSON><PERSON> daftar harga untuk dinonaktifkan di masa mendatang."}, "customerAvailability": {"header": "Pilih grup pelanggan", "label": "<PERSON><PERSON><PERSON><PERSON> pela<PERSON>", "hint": "Pilih grup pelanggan mana yang harus diterapkan daftar harga.", "placeholder": "Cari grup pelanggan", "attribute": "Grup pelanggan"}}}, "profile": {"domain": "Profil", "manageYourProfileDetails": "Ke<PERSON>la detail profil <PERSON><PERSON>.", "fields": {"languageLabel": "Bahasa", "usageInsightsLabel": "<PERSON><PERSON><PERSON>"}, "edit": {"header": "Edit Profil", "languageHint": "Bahasa yang ingin Anda gunakan di dasbor admin. Ini tidak mengubah bahasa toko <PERSON>a.", "languagePlaceholder": "<PERSON><PERSON><PERSON> bahasa", "usageInsightsHint": "Bagikan wawasan penggunaan dan bantu kami meningkatkan Medusa. Anda dapat membaca lebih lanjut tentang apa yang kami kumpulkan dan bagaimana kami menggunakannya di <0>dokumentasi</0> kami."}, "toast": {"edit": "<PERSON><PERSON><PERSON> profil ters<PERSON>pan"}}, "users": {"domain": "Pengguna", "editUser": "<PERSON>", "inviteUser": "Undang Pengguna", "inviteUserHint": "Undang pengguna baru ke toko <PERSON>a.", "sendInvite": "<PERSON><PERSON>", "pendingInvites": "Undangan Tertunda", "deleteInviteWarning": "<PERSON>a akan menghapus undangan untuk {{email}}. Tindakan ini tidak dapat dibatalkan.", "resendInvite": "<PERSON><PERSON>", "copyInviteLink": "<PERSON><PERSON> tautan undangan", "expiredOnDate": "<PERSON><PERSON><PERSON><PERSON><PERSON> pada {{date}}", "validFromUntil": "Berlaku dari <0>{{from}}</0> - <1>{{until}}</1>", "acceptedOnDate": "Di<PERSON><PERSON> pada {{date}}", "inviteStatus": {"accepted": "Diterima", "pending": "Tertunda", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "roles": {"admin": "Admin", "developer": "Pengembang", "member": "Anggota"}, "list": {"empty": {"heading": "Tidak ada pengguna ditemukan", "description": "<PERSON><PERSON><PERSON> pen<PERSON>una di<PERSON>ng, mereka akan muncul di sini."}, "filtered": {"heading": "Tidak ada hasil", "description": "Tidak ada pengguna yang cocok dengan kriteria filter saat ini."}}, "deleteUserWarning": "<PERSON>a akan menghapus pengguna {{name}}. Tindakan ini tidak dapat dibatalkan.", "deleteUserSuccess": "Pengguna {{name}} ber<PERSON><PERSON> dihapus", "invite": "Undang"}, "store": {"domain": "<PERSON><PERSON>", "manageYourStoresDetails": "Kelola detail to<PERSON>", "editStore": "Edit toko", "defaultCurrency": "<PERSON> uang default", "defaultRegion": "Wilayah default", "defaultSalesChannel": "Saluran penjualan default", "defaultLocation": "Lokasi default", "swapLinkTemplate": "Template tautan swap", "paymentLinkTemplate": "Template <PERSON><PERSON>", "inviteLinkTemplate": "Template <PERSON><PERSON>n", "currencies": "<PERSON>", "addCurrencies": "Tam<PERSON> mata uang", "enableTaxInclusivePricing": "<PERSON>kt<PERSON><PERSON> harga term<PERSON> pajak", "disableTaxInclusivePricing": "Nonaktif<PERSON> harga term<PERSON>uk pajak", "removeCurrencyWarning_one": "Anda akan menghapus {{count}} mata uang dari toko <PERSON>a. Pastikan Anda telah menghapus semua harga menggunakan mata uang tersebut sebelum melanjutkan.", "removeCurrencyWarning_other": "Anda akan menghapus {{count}} mata uang dari toko <PERSON>a. Pastikan Anda telah menghapus semua harga menggunakan mata uang tersebut sebelum melanjutkan.", "currencyAlreadyAdded": "Mata uang sudah ditambahkan ke toko <PERSON>a.", "edit": {"header": "<PERSON>"}, "toast": {"update": "<PERSON><PERSON>", "currenciesUpdated": "<PERSON> uang ber<PERSON><PERSON> diper<PERSON>", "currenciesRemoved": "<PERSON> uang ber<PERSON>il di<PERSON>pus dari toko", "updatedTaxInclusivitySuccessfully": "<PERSON><PERSON> term<PERSON><PERSON> pajak ber<PERSON><PERSON>"}}, "regions": {"domain": "Wilayah", "subtitle": "Wilayah adalah area tempat Anda menjual produk. Ini dapat mencakup beberapa negara, dan memiliki tarif pajak, pen<PERSON><PERSON>, dan mata uang yang berbeda.", "createRegion": "Buat Wilayah", "createRegionHint": "<PERSON><PERSON><PERSON> tarif pajak dan penyedia untuk sekumpulan negara.", "addCountries": "Tambah negara", "editRegion": "<PERSON>", "countriesHint": "Tambah negara yang termasuk dalam wilayah ini.", "deleteRegionWarning": "<PERSON>a akan menghapus wilayah {{name}}. Tindakan ini tidak dapat dibatalkan.", "removeCountriesWarning_one": "<PERSON>a akan menghapus {{count}} negara dari wilayah ini. Tindakan ini tidak dapat dibatalkan.", "removeCountriesWarning_other": "<PERSON>a akan menghapus {{count}} negara dari wilayah ini. Tindakan ini tidak dapat dibatalkan.", "removeCountryWarning": "<PERSON>a akan menghapus negara {{name}} dari wilayah ini. Tindakan ini tidak dapat dibatalkan.", "automaticTaxesHint": "<PERSON><PERSON>, pajak hanya akan dihitung saat checkout berda<PERSON><PERSON> alamat pen<PERSON>.", "taxInclusiveHint": "<PERSON><PERSON>, harga di wilayah ini sudah termasuk pajak.", "providersHint": "Tambah penyedia pembayaran mana yang tersedia di wilayah ini.", "shippingOptions": "<PERSON><PERSON>", "deleteShippingOptionWarning": "<PERSON><PERSON> akan menghapus opsi pengiriman {{name}}. Tindakan ini tidak dapat dibatalkan.", "return": "<PERSON><PERSON><PERSON><PERSON>", "outbound": "<PERSON><PERSON><PERSON>", "priceType": "<PERSON><PERSON>", "flatRate": "<PERSON><PERSON><PERSON>", "calculated": "<PERSON><PERSON><PERSON>", "list": {"noRecordsMessage": "Buat wilayah untuk area tempat Anda berjualan."}, "toast": {"delete": "<PERSON><PERSON><PERSON> be<PERSON>", "edit": "Edit wilayah tersimpan", "create": "Wilayah berhasil dibuat", "countries": "Negara wilayah ber<PERSON>il <PERSON>er<PERSON>"}, "shippingOption": {"createShippingOption": "Buat Opsi Pengiriman", "createShippingOptionHint": "Buat opsi pengiriman baru untuk wilayah.", "editShippingOption": "<PERSON> <PERSON><PERSON>", "fulfillmentMethod": "<PERSON><PERSON>", "type": {"outbound": "<PERSON><PERSON><PERSON>", "outboundHint": "<PERSON>akan ini jika Anda membuat opsi pengiriman untuk mengirim produk ke pelanggan.", "return": "<PERSON><PERSON><PERSON><PERSON>", "returnHint": "<PERSON>akan ini jika Anda membuat opsi pengiriman agar pelanggan dapat mengembalikan produk kepada Anda."}, "priceType": {"label": "<PERSON><PERSON>", "flatRate": "<PERSON><PERSON><PERSON> tetap", "calculated": "<PERSON><PERSON><PERSON>"}, "availability": {"adminOnly": "<PERSON><PERSON> admin", "adminOnlyHint": "<PERSON><PERSON>, opsi pengiriman hanya akan tersedia di dasbor admin, dan tidak di etalase."}, "taxInclusiveHint": "<PERSON><PERSON>, harga opsi pen<PERSON>man akan term<PERSON>uk pajak.", "requirements": {"label": "Persyara<PERSON>", "hint": "Tentukan persyaratan untuk opsi pengiriman."}}}, "taxes": {"domain": "<PERSON><PERSON><PERSON>", "domainDescription": "<PERSON><PERSON><PERSON> wilayah pajak Anda", "countries": {"taxCountriesHint": "Pengaturan pajak berlaku untuk negara yang terdaftar."}, "settings": {"editTaxSettings": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "taxProviderLabel": "<PERSON><PERSON><PERSON> pajak", "systemTaxProviderLabel": "Penyedia <PERSON> Sistem", "calculateTaxesAutomaticallyLabel": "Hitung pajak secara otomatis", "calculateTaxesAutomaticallyHint": "<PERSON><PERSON> di<PERSON>, tarif pajak akan dihitung secara otomatis dan diterapkan pada keranjang. <PERSON><PERSON> dinonakt<PERSON><PERSON>, pajak harus dihitung secara manual saat checkout. Pajak manual disarankan untuk digunakan dengan penyedia pajak pihak ketiga.", "applyTaxesOnGiftCardsLabel": "Terapkan pajak pada kartu hadiah", "applyTaxesOnGiftCardsHint": "<PERSON><PERSON>, pajak akan diterapkan pada kartu hadiah saat checkout. <PERSON> <PERSON><PERSON> negara, peraturan pajak mewajibkan penerapan pajak pada kartu hadiah saat pembelian.", "defaultTaxRateLabel": "<PERSON><PERSON><PERSON> p<PERSON> default", "defaultTaxCodeLabel": "Kode pajak default"}, "defaultRate": {"sectionTitle": "<PERSON><PERSON><PERSON>"}, "taxRate": {"sectionTitle": "<PERSON><PERSON><PERSON>", "createTaxRate": "<PERSON><PERSON><PERSON>", "createTaxRateHint": "Buat tarif pajak baru untuk wilayah.", "deleteRateDescription": "<PERSON><PERSON> akan menghapus tarif pajak {{name}}. Tindakan ini tidak dapat dibatalkan.", "editRateAction": "Edit tarif", "editOverridesAction": "<PERSON>", "editOverridesTitle": "<PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON>", "editOverridesHint": "Tentukan pengabaian untuk tarif pajak.", "deleteTaxRateWarning": "<PERSON><PERSON> akan menghapus tarif pajak {{name}}. Tindakan ini tidak dapat dibatalkan.", "productOverridesLabel": "Pengabaian produk", "productOverridesHint": "Tentukan pengabaian produk untuk tarif pajak.", "addProductOverridesAction": "Tambah pengabaian produk", "productTypeOverridesLabel": "Pengabaian jenis produk", "productTypeOverridesHint": "Tentukan pengabaian jenis produk untuk tarif pajak.", "addProductTypeOverridesAction": "Tambah pengabaian jenis produk", "shippingOptionOverridesLabel": "Pengabaian opsi pengiriman", "shippingOptionOverridesHint": "Tentukan pengabaian opsi pengiriman untuk tarif pajak.", "addShippingOptionOverridesAction": "Tambah pengabaian opsi pengiriman", "productOverridesHeader": "Produk", "productTypeOverridesHeader": "<PERSON><PERSON>", "shippingOptionOverridesHeader": "<PERSON><PERSON>"}}, "locations": {"domain": "<PERSON><PERSON>", "editLocation": "<PERSON> lokasi", "addSalesChannels": "Tambah saluran penjualan", "noLocationsFound": "Tidak ada lokasi di<PERSON>ukan", "selectLocations": "Pilih lokasi yang menyimpan item.", "deleteLocationWarning": "<PERSON>a akan menghapus lokasi {{name}}. Tindakan ini tidak dapat dibatalkan.", "removeSalesChannelsWarning_one": "<PERSON>a akan menghapus {{count}} saluran penjualan dari lokasi.", "removeSalesChannelsWarning_other": "<PERSON>a akan menghapus {{count}} saluran penjualan dari lokasi.", "toast": {"create": "Lokasi berhasil dibuat", "update": "<PERSON><PERSON> <PERSON><PERSON>", "removeChannel": "<PERSON><PERSON><PERSON> pen<PERSON> ber<PERSON><PERSON>"}}, "reservations": {"domain": "Reservasi", "subtitle": "Kelola kuantitas item inventaris yang dipesan.", "deleteWarning": "<PERSON>a akan menghapus reservasi. Tindakan ini tidak dapat dibatalkan."}, "salesChannels": {"domain": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON>la saluran online dan offline tempat Anda menjual produk.", "list": {"empty": {"heading": "Tidak ada saluran penjualan ditemukan", "description": "<PERSON><PERSON><PERSON> saluran pen<PERSON>alan dibuat, akan muncul di sini."}, "filtered": {"heading": "Tidak ada hasil", "description": "Tidak ada saluran penjualan yang cocok dengan kriteria filter saat ini."}}, "createSalesChannel": "Buat Sal<PERSON>", "createSalesChannelHint": "Buat saluran penjualan baru untuk menjual produk Anda.", "enabledHint": "Tentukan apakah saluran penjualan diaktifkan.", "removeProductsWarning_one": "<PERSON><PERSON> akan mengh<PERSON> {{count}} produk dari {{sales_channel}}.", "removeProductsWarning_other": "<PERSON><PERSON> akan mengh<PERSON> {{count}} produk dari {{sales_channel}}.", "addProducts": "Tambah Produk", "editSalesChannel": "<PERSON> sa<PERSON>", "productAlreadyAdded": "Produk sudah ditambahkan ke saluran penjualan.", "deleteSalesChannelWarning": "<PERSON><PERSON> akan menghapus saluran pen<PERSON> {{name}}. Tindakan ini tidak dapat dibatalkan.", "toast": {"create": "<PERSON><PERSON>n pen<PERSON> berhasil dibuat", "update": "<PERSON><PERSON><PERSON> pen<PERSON> ber<PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON> pen<PERSON> ber<PERSON><PERSON>"}, "tooltip": {"cannotDeleteDefault": "Tidak dapat menghapus saluran penjualan default"}, "products": {"list": {"noRecordsMessage": "Tidak ada produk di saluran penjualan."}, "add": {"list": {"noRecordsMessage": "Buat produk terlebih da<PERSON>u."}}}}, "apiKeyManagement": {"domain": {"publishable": "Kunci API yang Dapat Dipublikasikan", "secret": "Kunci API Rahasia"}, "subtitle": {"publishable": "Kelola kunci API yang digunakan di etalase untuk membatasi cakupan permintaan ke saluran penjualan tertentu.", "secret": "Ke<PERSON>la kunci API yang digunakan untuk mengautentikasi pengguna admin di aplikasi admin."}, "status": {"active": "Aktif", "revoked": "Dicabut"}, "type": {"publishable": "Dapat Dipublikasikan", "secret": "<PERSON><PERSON><PERSON>"}, "create": {"createPublishableHeader": "Buat Kunci API yang Dapat Dipublikasikan", "createPublishableHint": "Buat kunci API yang dapat dipublikasikan baru untuk membatasi cakupan permintaan ke saluran penjualan tertentu.", "createSecretHeader": "Buat Kunci API Rahasia", "createSecretHint": "Buat kunci API rahasia baru untuk mengakses Medusa API sebagai pengguna admin terautentikasi.", "secretKeyCreatedHeader": "<PERSON><PERSON><PERSON>", "secretKeyCreatedHint": "<PERSON><PERSON>i rahasia baru Anda telah dibuat. <PERSON>in dan simpan dengan aman sekarang. Ini adalah satu-satunya saat akan ditampilkan.", "copySecretTokenSuccess": "<PERSON><PERSON><PERSON> rahasia ber<PERSON>il disalin ke papan klip.", "copySecretTokenFailure": "<PERSON><PERSON> menyalin kunci rahasia ke papan klip.", "successToast": "Kunci API berhasil dibuat."}, "edit": {"header": "Edit Kunci API", "description": "Edit judul kunci API.", "successToast": "Kunci API {{title}} berhasil diperbarui."}, "salesChannels": {"title": "Tambah Saluran <PERSON>", "description": "Tambah saluran penjualan yang harus dibatasi oleh kunci API.", "successToast_one": "{{count}} saluran penjualan berhasil ditambahkan ke kunci API.", "successToast_other": "{{count}} saluran penjualan berhasil ditambahkan ke kunci API.", "alreadyAddedTooltip": "Saluran penjualan sudah ditambahkan ke kunci API.", "list": {"noRecordsMessage": "Tidak ada saluran penjualan dalam cakupan kunci API yang dapat dipublikasikan."}}, "delete": {"warning": "<PERSON><PERSON> akan menghapus kunci API {{title}}. Tindakan ini tidak dapat dibatalkan.", "successToast": "Kunci API {{title}} berhasil dihapus."}, "revoke": {"warning": "Anda akan mencabut kunci API {{title}}. Tindakan ini tidak dapat dibatalkan.", "successToast": "Kunci API {{title}} berhasil dicabut."}, "addSalesChannels": {"list": {"noRecordsMessage": "Buat saluran penjualan terlebih dahulu."}}, "removeSalesChannel": {"warning": "<PERSON><PERSON> akan menghapus saluran penjualan {{name}} dari kunci API. Tindakan ini tidak dapat dibatalkan.", "warningBatch_one": "<PERSON>a akan menghapus {{count}} saluran penjualan dari kunci API. Tindakan ini tidak dapat dibatalkan.", "warningBatch_other": "<PERSON>a akan menghapus {{count}} saluran penjualan dari kunci API. Tindakan ini tidak dapat dibatalkan.", "successToast": "<PERSON><PERSON>n pen<PERSON> berhasil dihapus dari kunci API.", "successToastBatch_one": "{{count}} saluran penjualan berhasil dihapus dari kunci API.", "successToastBatch_other": "{{count}} saluran penjualan berhasil dihapus dari kunci API."}, "actions": {"revoke": "<PERSON><PERSON><PERSON> kunci <PERSON>", "copy": "<PERSON><PERSON> kunci <PERSON>", "copySuccessToast": "Kunci API berhasil disalin ke papan klip."}, "table": {"lastUsedAtHeader": "<PERSON><PERSON><PERSON>", "createdAtHeader": "Dicabut Pada"}, "fields": {"lastUsedAtLabel": "<PERSON><PERSON><PERSON>kan pada", "revokedByLabel": "Dicabut oleh", "revokedAtLabel": "Dicabut pada", "createdByLabel": "Dibuat oleh"}}, "returnReasons": {"domain": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON>la alasan untuk item yang dikembalikan.", "calloutHint": "<PERSON><PERSON><PERSON> alasan untuk mengkategorikan pengembalian.", "editReason": "<PERSON> <PERSON><PERSON>", "create": {"header": "Tambah Alasan <PERSON>", "subtitle": "Tentukan alasan paling umum untuk pengembalian.", "hint": "Buat alasan pengembalian baru untuk mengkategorikan pengembalian.", "successToast": "Alasan pengembalian {{label}} berhasil dibuat."}, "edit": {"header": "<PERSON> <PERSON><PERSON>", "subtitle": "Edit nilai al<PERSON>n pen<PERSON>.", "successToast": "Alasan pengembalian {{label}} ber<PERSON><PERSON> diperbarui."}, "delete": {"confirmation": "<PERSON><PERSON> akan menghapus alasan pengembalian \"{{label}}\". Tindakan ini tidak dapat dibatalkan.", "successToast": "<PERSON><PERSON><PERSON> pengembalian \"{{label}}\" ber<PERSON>il dihapus."}, "fields": {"value": {"label": "<PERSON><PERSON>", "placeholder": "salah_ukuran", "tooltip": "<PERSON><PERSON> harus menjadi pengenal unik untuk alasan pengembalian."}, "label": {"label": "Label", "placeholder": "Ukuran salah"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "Pelanggan menerima ukuran yang salah"}}}, "login": {"forgotPassword": "Lupa kata sandi? - <0><PERSON><PERSON></0>", "title": "Selamat datang di Medusa", "hint": "Masuk untuk mengakses area akun"}, "invite": {"title": "Selamat datang di Medusa", "hint": "<PERSON><PERSON>t akun <PERSON> di bawah", "backToLogin": "<PERSON><PERSON><PERSON> ke login", "createAccount": "<PERSON><PERSON><PERSON> akun", "alreadyHaveAccount": "Sudah punya akun? - <0><PERSON><PERSON><PERSON></0>", "emailTooltip": "Alamat email Anda tidak dapat diubah. Jika Anda ingin menggunakan email lain, undangan baru harus dikirim.", "invalidInvite": "Undangan tidak valid atau sudah kedalu<PERSON>sa.", "successTitle": "<PERSON><PERSON><PERSON>a telah terdaftar", "successHint": "<PERSON><PERSON> se<PERSON> juga.", "successAction": "<PERSON><PERSON>", "invalidTokenTitle": "Token undangan Anda tidak valid", "invalidTokenHint": "Coba minta tautan undangan baru.", "passwordMismatch": "Kata sandi tidak cocok", "toast": {"accepted": "Undangan ber<PERSON>il diterima"}}, "resetPassword": {"title": "<PERSON><PERSON> ulang kata sandi", "hint": "Masukkan email <PERSON><PERSON>, dan kami akan mengirimkan instruksi tentang cara mengatur ulang kata sandi Anda.", "email": "Email", "sendResetInstructions": "<PERSON><PERSON> instruksi setel ulang", "backToLogin": "<0><PERSON><PERSON><PERSON> ke login</0>", "newPasswordHint": "<PERSON><PERSON>h kata sandi baru di bawah.", "invalidTokenTitle": "Token setel ulang Anda tidak valid", "invalidTokenHint": "<PERSON><PERSON> setel ulang kata sandi Anda lagi.", "expiredTokenTitle": "Token setel ulang Anda telah kedalu<PERSON>sa", "goToResetPassword": "<PERSON><PERSON> ke <PERSON>el Ulang Kata Sandi", "resetPassword": "<PERSON><PERSON> ulang kata sandi", "newPassword": "Kata sandi baru", "repeatNewPassword": "Ulangi kata sandi baru", "tokenExpiresIn": "Token kedalu<PERSON>sa dalam <0>{{time}}</0> menit", "successfulRequestTitle": "<PERSON><PERSON><PERSON><PERSON> email kepada Anda", "successfulRequest": "<PERSON><PERSON> telah men<PERSON>kan email yang dapat Anda gunakan untuk mengatur ulang kata sandi Anda. Periksa folder spam Anda jika Anda belum menerimanya setelah beberapa menit.", "successfulResetTitle": "<PERSON><PERSON> ulang kata sandi ber<PERSON>il", "successfulReset": "<PERSON><PERSON><PERSON> masuk di halaman login.", "passwordMismatch": "Kata sandi tidak cocok", "invalidLinkTitle": "<PERSON><PERSON> setel ulang <PERSON>a tidak valid", "invalidLinkHint": "<PERSON><PERSON> setel ulang kata sandi Anda lagi."}, "workflowExecutions": {"domain": "<PERSON><PERSON>", "subtitle": "<PERSON>hat dan pantau eksekusi alur kerja di aplikasi Medusa Anda.", "transactionIdLabel": "ID Transaksi", "workflowIdLabel": "ID Alur Kerja", "progressLabel": "<PERSON><PERSON><PERSON><PERSON>", "stepsCompletedLabel_one": "{{completed}} dari {{count}} langkah", "stepsCompletedLabel_other": "{{completed}} dari {{count}} langkah", "list": {"noRecordsMessage": "Belum ada alur kerja yang die<PERSON>."}, "history": {"sectionTitle": "Riwayat", "runningState": "<PERSON><PERSON><PERSON><PERSON>...", "awaitingState": "<PERSON><PERSON><PERSON>", "failedState": "Gaga<PERSON>", "skippedState": "Di<PERSON><PERSON>", "skippedFailureState": "<PERSON><PERSON><PERSON> (Gagal)", "definitionLabel": "Definisi", "outputLabel": "Output", "compensateInputLabel": "Input kompensasi", "revertedLabel": "Di<PERSON><PERSON>lik<PERSON>", "errorLabel": "<PERSON><PERSON><PERSON>"}, "state": {"done": "Se<PERSON><PERSON>", "failed": "Gaga<PERSON>", "reverted": "Di<PERSON><PERSON>lik<PERSON>", "invoking": "Memanggil", "compensating": "Mengkompensasi", "notStarted": "Belum dimulai"}, "transaction": {"state": {"waitingToCompensate": "<PERSON>ung<PERSON> kompensasi"}}, "step": {"state": {"skipped": "Di<PERSON><PERSON>", "skippedFailure": "<PERSON><PERSON><PERSON> (Gagal)", "dormant": "<PERSON><PERSON>", "timeout": "<PERSON><PERSON><PERSON> habis"}}}, "productTypes": {"domain": "<PERSON><PERSON>", "subtitle": "Atur produk <PERSON>a ke dalam jenis.", "create": {"header": "Buat Jenis <PERSON>", "hint": "<PERSON><PERSON>t jenis produk baru untuk mengkategorikan produk And<PERSON>.", "successToast": "<PERSON><PERSON> produk {{value}} berhasil dibuat."}, "edit": {"header": "<PERSON> <PERSON><PERSON>", "successToast": "<PERSON><PERSON> produk {{value}} ber<PERSON><PERSON> diperbarui."}, "delete": {"confirmation": "<PERSON><PERSON> akan mengh<PERSON>us jenis produk \"{{value}}\". Tindakan ini tidak dapat di<PERSON>an.", "successToast": "<PERSON><PERSON> produk \"{{value}}\" ber<PERSON><PERSON> dihapus."}, "fields": {"value": "<PERSON><PERSON>"}}, "productTags": {"domain": "Tag Produk", "create": {"header": "Buat Tag Produk", "subtitle": "Buat tag produk baru untuk mengkategorikan produk Anda.", "successToast": "Tag produk {{value}} berhasil dibuat."}, "edit": {"header": "Edit Tag Produk", "subtitle": "Edit nilai tag produk.", "successToast": "Tag produk {{value}} berhasil diperbarui."}, "delete": {"confirmation": "<PERSON><PERSON> akan mengh<PERSON> tag produk {{value}}. Tindakan ini tidak dapat dibatalkan.", "successToast": "Tag produk {{value}} berhasil dihapus."}, "fields": {"value": "<PERSON><PERSON>"}}, "notifications": {"domain": "Notif<PERSON><PERSON>", "emptyState": {"title": "Tidak ada notifikasi", "description": "Anda belum memiliki notifikasi saat ini, tetapi setelah ada, akan muncul di sini."}, "accessibility": {"description": "Notifikasi tentang aktivitas Medusa akan dicantumkan di sini."}}, "errors": {"serverError": "Kesalahan server - Coba lagi nanti.", "invalidCredentials": "Email atau kata sandi salah"}, "statuses": {"scheduled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "active": "Aktif", "inactive": "Tidak aktif", "draft": "Draf", "enabled": "Diaktifkan", "disabled": "Dinonaktifkan"}, "labels": {"productVariant": "<PERSON><PERSON>", "prices": "<PERSON><PERSON>", "available": "Tersedia", "inStock": "Stok tersedia", "added": "Ditambahkan", "removed": "<PERSON><PERSON><PERSON>", "from": "<PERSON><PERSON>", "to": "<PERSON><PERSON><PERSON>", "beaware": "<PERSON><PERSON><PERSON><PERSON>", "loading": "Memuat"}, "fields": {"amount": "<PERSON><PERSON><PERSON>", "refundAmount": "<PERSON><PERSON><PERSON> dana", "name": "<PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "firstName": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "customTitle": "<PERSON><PERSON><PERSON> kustom", "manageInventory": "<PERSON><PERSON><PERSON> inventaris", "inventoryKit": "Memiliki kit inventaris", "inventoryItems": "Item inventaris", "inventoryItem": "Item inventaris", "requiredQuantity": "Kuantitas wajib", "description": "<PERSON><PERSON><PERSON><PERSON>", "email": "Email", "password": "<PERSON>a sandi", "repeatPassword": "<PERSON><PERSON><PERSON>", "confirmPassword": "<PERSON>n<PERSON><PERSON><PERSON>", "newPassword": "<PERSON><PERSON>", "repeatNewPassword": "Ulang<PERSON> Kat<PERSON> Sand<PERSON>", "categories": "<PERSON><PERSON><PERSON>", "shippingMethod": "<PERSON><PERSON>", "configurations": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "conditions": "<PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "collection": "<PERSON><PERSON><PERSON><PERSON>", "discountable": "<PERSON><PERSON><PERSON> diskon", "handle": "<PERSON><PERSON>", "subtitle": "Subjudul", "by": "<PERSON><PERSON>", "item": "<PERSON><PERSON>", "qty": "kuantitas.", "limit": "Batas", "tags": "Tag", "type": "<PERSON><PERSON>", "reason": "<PERSON><PERSON><PERSON>", "none": "tidak ada", "all": "semua", "search": "<PERSON><PERSON>", "percentage": "Persentase", "sales_channels": "<PERSON><PERSON><PERSON>", "customer_groups": "Grup Pelanggan", "product_tags": "Tag Produk", "product_types": "<PERSON><PERSON>", "product_collections": "Koleksi Produk", "status": "Status", "code": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "disabled": "Dinonaktifkan", "dynamic": "Dinamis", "normal": "Normal", "years": "<PERSON><PERSON>", "months": "<PERSON><PERSON><PERSON>", "days": "<PERSON>", "hours": "Jam", "minutes": "Menit", "totalRedemptions": "Total Penukaran", "countries": "Negara", "paymentProviders": "<PERSON><PERSON><PERSON>", "refundReason": "<PERSON><PERSON><PERSON>", "fulfillmentProviders": "<PERSON><PERSON><PERSON>", "fulfillmentProvider": "<PERSON><PERSON><PERSON>", "providers": "Penyedia", "availability": "<PERSON><PERSON><PERSON><PERSON>", "inventory": "Inventaris", "optional": "Opsional", "note": "Catatan", "automaticTaxes": "<PERSON><PERSON>", "taxInclusivePricing": "<PERSON><PERSON> term<PERSON> pajak", "currency": "<PERSON>", "address": "<PERSON><PERSON><PERSON>", "address2": "<PERSON>emen, suite, dll.", "city": "Kota", "postalCode": "Kode Pos", "country": "Negara", "state": "Negara Bagian", "province": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON><PERSON><PERSON>", "phone": "Telepon", "metadata": "<PERSON><PERSON><PERSON>", "selectCountry": "<PERSON><PERSON><PERSON> negara", "products": "Produk", "variants": "<PERSON><PERSON>", "orders": "<PERSON><PERSON><PERSON>", "account": "<PERSON><PERSON><PERSON>", "total": "Total Pesanan", "paidTotal": "Total diambil", "totalExclTax": "Total tidak termasuk pajak", "subtotal": "Subtotal", "shipping": "Pen<PERSON><PERSON>", "outboundShipping": "<PERSON><PERSON><PERSON>", "returnShipping": "Pen<PERSON><PERSON>", "tax": "<PERSON><PERSON>", "created": "Dibuat", "key": "<PERSON><PERSON><PERSON>", "customer": "Pelanggan", "date": "Tanggal", "order": "<PERSON><PERSON><PERSON>", "fulfillment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "provider": "Penyedia", "payment": "Pembayaran", "items": "<PERSON><PERSON>", "salesChannel": "<PERSON><PERSON><PERSON>", "region": "Wilayah", "discount": "Diskon", "role": "<PERSON><PERSON>", "sent": "Terkirim", "salesChannels": "<PERSON><PERSON><PERSON>", "product": "Produk", "createdAt": "Dibuat pada", "updatedAt": "<PERSON><PERSON><PERSON><PERSON> pada", "revokedAt": "Dicabut pada", "true": "<PERSON><PERSON>", "false": "<PERSON><PERSON>", "giftCard": "<PERSON><PERSON><PERSON>", "tag": "Tag", "dateIssued": "Tanggal diterbitkan", "issuedDate": "Tanggal diterbitkan", "expiryDate": "<PERSON><PERSON> ked<PERSON>", "price": "<PERSON><PERSON>", "priceTemplate": "<PERSON><PERSON> {{regionOrCurrency}}", "height": "Tingg<PERSON>", "width": "<PERSON><PERSON>", "length": "Panjang", "weight": "<PERSON><PERSON>", "midCode": "Kode MID", "hsCode": "Kode HS", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "Kuantitas inventaris", "barcode": "<PERSON><PERSON> batang", "countryOfOrigin": "<PERSON><PERSON><PERSON> asal", "material": "<PERSON><PERSON>", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "sku": "SKU", "managedInventory": "Inventaris dikelola", "allowBackorder": "Izinkan pesanan kembali", "inStock": "Stok tersedia", "location": "<PERSON><PERSON>", "quantity": "Kuantitas", "variant": "<PERSON><PERSON>", "id": "ID", "parent": "Induk", "minSubtotal": "Subtotal Min.", "maxSubtotal": "Subtotal Maks.", "shippingProfile": "<PERSON><PERSON>", "summary": "<PERSON><PERSON><PERSON>", "details": "Detail", "label": "Label", "rate": "<PERSON><PERSON><PERSON>", "requiresShipping": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unitPrice": "<PERSON><PERSON> satuan", "startDate": "<PERSON><PERSON> mulai", "endDate": "<PERSON><PERSON> akhir", "draft": "Draf", "values": "<PERSON><PERSON>"}, "quotes": {"domain": "Penawaran", "title": "Penawaran", "subtitle": "<PERSON><PERSON><PERSON> pen<PERSON>ran dan proposal pelanggan", "noQuotes": "Tidak ada penawaran di<PERSON>ukan", "noQuotesDescription": "Saat ini tidak ada penawaran. Buat satu dari toko.", "table": {"id": "ID Penawaran", "customer": "Pelanggan", "status": "Status", "company": "<PERSON><PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>", "createdAt": "Dibuat", "updatedAt": "<PERSON><PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>"}, "status": {"pending_merchant": "<PERSON><PERSON><PERSON> Pedagang", "pending_customer": "<PERSON><PERSON><PERSON>", "merchant_rejected": "<PERSON><PERSON><PERSON>", "customer_rejected": "<PERSON><PERSON><PERSON>", "accepted": "Diterima", "unknown": "Tidak Diketahui"}, "actions": {"sendQuote": "<PERSON><PERSON>", "rejectQuote": "<PERSON><PERSON>", "viewOrder": "<PERSON><PERSON>"}, "details": {"header": "Detail <PERSON>", "quoteSummary": "<PERSON><PERSON><PERSON>", "customer": "Pelanggan", "company": "<PERSON><PERSON><PERSON><PERSON>", "items": "<PERSON><PERSON>", "total": "Total", "subtotal": "Subtotal", "shipping": "Pen<PERSON><PERSON>", "tax": "<PERSON><PERSON>", "discounts": "Diskon", "originalTotal": "Total Asli", "quoteTotal": "Total Penawaran", "messages": "<PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>", "sendMessage": "<PERSON><PERSON>", "send": "<PERSON><PERSON>", "pickQuoteItem": "Pilih item penawaran", "selectQuoteItem": "Pilih item penawaran untuk berkomentar", "selectItem": "Pilih item", "manage": "<PERSON><PERSON><PERSON>", "phone": "Telepon", "spendingLimit": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "manageQuote": "<PERSON><PERSON><PERSON>", "noItems": "Tidak ada item dalam penawaran ini", "noMessages": "Tidak ada pesan untuk penawaran ini"}, "items": {"title": "Produk", "quantity": "Kuantitas", "unitPrice": "<PERSON><PERSON>", "total": "Total"}, "messages": {"admin": "Administrator", "customer": "Pelanggan", "placeholder": "Ke<PERSON>k pesan <PERSON>a di sini..."}, "filters": {"status": "Filter berdasarkan status"}, "confirmations": {"sendTitle": "<PERSON><PERSON>", "sendDescription": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin mengirim penawaran ini kepada pelanggan?", "rejectTitle": "<PERSON><PERSON>", "rejectDescription": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menolak penawaran ini?"}, "acceptance": {"message": "Penawaran telah diterima"}, "toasts": {"sendSuccess": "<PERSON><PERSON><PERSON><PERSON>m penawaran kepada pelanggan", "sendError": "<PERSON><PERSON> men<PERSON>m penawaran", "rejectSuccess": "<PERSON><PERSON><PERSON><PERSON>k penawaran pelanggan", "rejectError": "<PERSON><PERSON> menolak penawaran", "messageSuccess": "<PERSON><PERSON><PERSON><PERSON>m pesan kepada pelanggan", "messageError": "<PERSON><PERSON> men<PERSON>m pesan", "updateSuccess": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "manage": {"overridePriceHint": "Timpa harga asli untuk item ini", "updatePrice": "<PERSON><PERSON><PERSON>"}}, "companies": {"domain": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> hubungan bisnis", "noCompanies": "Tidak ada per<PERSON> di<PERSON>n", "noCompaniesDescription": "<PERSON>uat perusahaan pertama Anda untuk memulai.", "notFound": "<PERSON><PERSON><PERSON><PERSON> tidak di<PERSON>n", "table": {"name": "<PERSON><PERSON>", "phone": "Telepon", "email": "Email", "address": "<PERSON><PERSON><PERSON>", "employees": "<PERSON><PERSON><PERSON>", "customerGroup": "Grup Pelanggan", "actions": "<PERSON><PERSON><PERSON>"}, "fields": {"name": "<PERSON><PERSON>", "email": "Email", "phone": "Telepon", "website": "Website", "address": "<PERSON><PERSON><PERSON>", "city": "Kota", "state": "<PERSON><PERSON><PERSON>", "zip": "<PERSON><PERSON> pos", "zipCode": "<PERSON><PERSON> pos", "country": "Negara", "currency": "<PERSON>", "logoUrl": "URL logo", "description": "<PERSON><PERSON><PERSON><PERSON>", "employees": "<PERSON><PERSON><PERSON>", "customerGroup": "Grup pelanggan", "approvalSettings": "<PERSON><PERSON><PERSON><PERSON>"}, "placeholders": {"name": "<PERSON><PERSON><PERSON><PERSON> nama <PERSON>an", "email": "<PERSON><PERSON><PERSON><PERSON> email", "phone": "Ma<PERSON>kkan nomor telepon", "website": "Masukkan URL website", "address": "<PERSON><PERSON><PERSON><PERSON>", "city": "Masukkan kota", "state": "<PERSON><PERSON><PERSON><PERSON> provinsi", "zip": "Masukkan kode pos", "logoUrl": "Masukkan URL logo", "description": "<PERSON><PERSON><PERSON><PERSON>", "selectCountry": "<PERSON><PERSON><PERSON> negara", "selectCurrency": "<PERSON><PERSON><PERSON> mata uang"}, "validation": {"nameRequired": "<PERSON><PERSON> wajib diisi", "emailRequired": "<PERSON><PERSON> wa<PERSON>", "emailInvalid": "Alamat email tidak valid", "addressRequired": "<PERSON><PERSON><PERSON> wa<PERSON>", "cityRequired": "Kota wajib diisi", "stateRequired": "<PERSON><PERSON><PERSON> waji<PERSON>", "zipRequired": "<PERSON><PERSON> pos wajib diisi"}, "create": {"title": "<PERSON><PERSON><PERSON>", "description": "Buat perusahaan baru untuk mengelola hubungan bisnis.", "submit": "<PERSON><PERSON><PERSON>"}, "edit": {"title": "<PERSON>", "submit": "<PERSON><PERSON><PERSON>"}, "details": {"actions": "<PERSON><PERSON><PERSON>"}, "approvals": {"requiresAdminApproval": "<PERSON><PERSON><PERSON><PERSON> admin", "requiresSalesManagerApproval": "<PERSON><PERSON><PERSON><PERSON> per<PERSON> manajer <PERSON>", "noApprovalRequired": "Tidak memer<PERSON>an per<PERSON>"}, "deleteWarning": "Ini akan menghapus perusahaan dan semua data terkait secara permanen.", "approvalSettings": {"title": "<PERSON><PERSON><PERSON><PERSON>", "requiresAdminApproval": "<PERSON><PERSON><PERSON><PERSON> admin", "requiresSalesManagerApproval": "<PERSON><PERSON><PERSON><PERSON> per<PERSON> manajer <PERSON>", "requiresAdminApprovalDesc": "<PERSON><PERSON>an dari per<PERSON>an ini memerlukan persetu<PERSON>an admin sebelum diproses", "requiresSalesManagerApprovalDesc": "<PERSON><PERSON>an dari perusahaan ini memerlukan persetu<PERSON>an manajer penjualan sebelum diproses", "updateSuccess": "<PERSON><PERSON><PERSON><PERSON> per<PERSON> ber<PERSON>", "updateError": "<PERSON><PERSON> me<PERSON> pen<PERSON>n <PERSON>"}, "customerGroup": {"title": "Kelola grup pelanggan", "hint": "Tetapkan perusahaan ini ke grup pelanggan untuk menerapkan harga dan izin khusus grup.", "name": "Nama grup pelanggan", "groupName": "Grup pelanggan", "actions": "<PERSON><PERSON><PERSON>", "add": "Tambah", "remove": "Hapus", "description": "Kelola grup pelanggan untuk perusahaan ini", "noGroups": "Tidak ada grup pelanggan tersedia", "addSuccess": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON><PERSON> ditambahkan ke grup pelanggan", "addError": "Gagal menambahkan perusahaan ke grup pelanggan", "removeSuccess": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON><PERSON> di<PERSON>pus dari grup pelanggan", "removeError": "<PERSON><PERSON> men<PERSON><PERSON><PERSON> per<PERSON>an dari grup pelanggan"}, "actions": {"edit": "<PERSON>", "editDetails": "Edit detail", "manageCustomerGroup": "Kelola grup pelanggan", "approvalSettings": "<PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON>", "confirmDelete": "Kon<PERSON><PERSON><PERSON> hapus"}, "delete": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus perusahaan ini? Tindakan ini tidak dapat dibatalkan."}, "employees": {"title": "<PERSON><PERSON><PERSON>", "noEmployees": "Tidak ada karyawan ditemukan untuk perusahaan ini", "name": "<PERSON><PERSON>", "email": "Email", "phone": "Telepon", "role": "<PERSON><PERSON>", "spendingLimit": "<PERSON><PERSON>", "admin": "Administrator", "employee": "<PERSON><PERSON><PERSON>", "add": "Tam<PERSON> karyawan", "create": {"title": "<PERSON><PERSON><PERSON> ka<PERSON>wan", "success": "<PERSON><PERSON><PERSON> ber<PERSON><PERSON> dibuat", "error": "<PERSON><PERSON> memb<PERSON>t karyawan"}, "form": {"details": "Informasi detail", "permissions": "<PERSON><PERSON>", "firstName": "<PERSON><PERSON> de<PERSON>", "lastName": "<PERSON><PERSON>", "email": "Email", "phone": "Telepon", "spendingLimit": "<PERSON><PERSON>", "adminAccess": "<PERSON>kses administrator", "isAdmin": "Adalah administrator", "isAdminDesc": "Berikan hak administrator kep<PERSON> karyawan ini", "isAdminTooltip": "Administrator da<PERSON><PERSON> men<PERSON><PERSON> pengaturan per<PERSON>an dan karyawan lain", "firstNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama depan", "lastNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama belakang", "emailPlaceholder": "<PERSON><PERSON><PERSON><PERSON> email", "phonePlaceholder": "Ma<PERSON>kkan nomor telepon", "spendingLimitPlaceholder": "<PERSON><PERSON><PERSON><PERSON> batas pengeluaran", "save": "Simpan", "saving": "Menyimpan..."}, "delete": {"confirmation": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus karyawan ini?", "success": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "edit": {"title": "<PERSON>"}, "toasts": {"updateSuccess": "<PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON>", "updateError": "<PERSON><PERSON> ka<PERSON>"}}, "toasts": {"createSuccess": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON> di<PERSON>at", "createError": "<PERSON><PERSON> membuat <PERSON>", "updateSuccess": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON>", "updateError": "<PERSON><PERSON>", "deleteSuccess": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON>", "deleteError": "<PERSON><PERSON><PERSON>"}}, "approvals": {"domain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> kerja <PERSON>", "noApprovals": "Tidak ada per<PERSON> di<PERSON>n", "noApprovalsDescription": "Saat ini tidak ada persetujuan untuk ditinjau.", "table": {"id": "ID", "type": "<PERSON><PERSON>", "company": "<PERSON><PERSON><PERSON><PERSON>", "customer": "Pelanggan", "amount": "<PERSON><PERSON><PERSON>", "status": "Status", "createdAt": "Dibuat"}, "status": {"pending": "<PERSON><PERSON><PERSON>", "approved": "Disetuju<PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unknown": "Tidak diketahui"}, "details": {"header": "Detail <PERSON>", "summary": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON><PERSON><PERSON>", "customer": "Pelanggan", "order": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON><PERSON>", "reason": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>"}, "actions": {"approve": "<PERSON><PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON>", "confirmApprove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmReject": "Konfirmasi penolakan", "reasonPlaceholder": "<PERSON><PERSON><PERSON><PERSON> (opsional)..."}, "filters": {"status": "Filter berdasarkan status"}, "toasts": {"approveSuccess": "<PERSON><PERSON><PERSON><PERSON>", "approveError": "<PERSON><PERSON>", "rejectSuccess": "<PERSON><PERSON><PERSON><PERSON>", "rejectError": "<PERSON><PERSON>"}}, "dateTime": {"years_one": "<PERSON><PERSON>", "years_other": "<PERSON><PERSON>", "months_one": "<PERSON><PERSON><PERSON>", "months_other": "<PERSON><PERSON><PERSON>", "weeks_one": "<PERSON><PERSON>", "weeks_other": "<PERSON><PERSON>", "days_one": "<PERSON>", "days_other": "<PERSON>", "hours_one": "Jam", "hours_other": "Jam", "minutes_one": "Menit", "minutes_other": "Menit", "seconds_one": "<PERSON><PERSON>", "seconds_other": "<PERSON><PERSON>"}}