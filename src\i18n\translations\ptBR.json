{"$schema": "./$schema.json", "general": {"ascending": "Ascendente", "descending": "Descendente", "add": "<PERSON><PERSON><PERSON><PERSON>", "start": "Início", "end": "Fim", "open": "Abrir", "close": "<PERSON><PERSON><PERSON>", "apply": "Aplicar", "range": "Intervalo", "search": "Buscar", "of": "de", "results": "resultados", "pages": "p<PERSON><PERSON><PERSON>", "next": "Próximo", "prev": "Anterior", "is": "é", "timeline": "Linha do tempo", "success": "Sucesso", "warning": "Aviso", "tip": "Dica", "error": "Erro", "select": "Selecionar", "selected": "Selecionado", "enabled": "Habilitado", "disabled": "Desabilitado", "expired": "<PERSON><PERSON><PERSON>", "active": "Ativo", "revoked": "Rev<PERSON><PERSON>", "new": "Novo", "modified": "Modificado", "added": "<PERSON><PERSON><PERSON><PERSON>", "removed": "Removido", "admin": "Admin", "store": "<PERSON><PERSON>", "details": "<PERSON><PERSON><PERSON>", "items_one": "{{count}} item", "items_other": "{{count}} itens", "countSelected": "{{count}} se<PERSON><PERSON><PERSON>(s)", "countOfTotalSelected": "{{count}} de {{total}} selecionado(s)", "plusCount": "+ {{count}}", "plusCountMore": "+ {{count}} mais", "areYouSure": "Tem certeza?", "noRecordsFound": "Nenhum registro encontrado", "typeToConfirm": "Digite {val} para confirmar:", "noResultsTitle": "Sem resultados", "noResultsMessage": "Tente alterar os filtros ou a consulta de busca", "noSearchResults": "Nenhum resultado de busca", "noSearchResultsFor": "Nenhum resultado de busca para <0>'{{query}}'</0>", "noRecordsTitle": "Sem registros", "noRecordsMessage": "Não há registros para mostrar", "unsavedChangesTitle": "Tem certeza de que deseja sair deste formulário?", "unsavedChangesDescription": "Você tem alterações não salvas que serão perdidas se sair deste formulário.", "includesTaxTooltip": "<PERSON>s preços nesta coluna incluem impostos.", "excludesTaxTooltip": "Os preços nesta coluna não incluem impostos.", "noMoreData": "Sem mais dados", "actions": "Ações"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}} chave", "numberOfKeys_other": "{{count}} chaves", "drawer": {"header_one": "JSON <0>· {{count}} chave</0>", "header_other": "JSON <0>· {{count}} chaves</0>", "description": "Visualizar os dados JSON deste objeto."}}, "metadata": {"header": "Metadados", "numberOfKeys_one": "{{count}} chave", "numberOfKeys_other": "{{count}} chaves", "edit": {"header": "<PERSON><PERSON>", "description": "Editar os metadados deste objeto.", "successToast": "Os metadados foram atualizados com sucesso.", "actions": {"insertRowAbove": "<PERSON><PERSON><PERSON> linha acima", "insertRowBelow": "<PERSON>ser<PERSON> linha a<PERSON>o", "deleteRow": "Excluir linha"}, "labels": {"key": "Chave", "value": "Valor"}, "complexRow": {"label": "Algumas linhas estão desativadas", "description": "Este objeto contém metadados não primitivos, como arrays ou objetos, que não podem ser editados aqui. Para editar as linhas desativadas, use a API diretamente.", "tooltip": "Esta linha está desativada porque contém dados não primitivos."}}}, "validation": {"mustBeInt": "O valor deve ser um número inteiro.", "mustBePositive": "O valor deve ser um número positivo."}, "actions": {"save": "<PERSON><PERSON>", "saveAsDraft": "<PERSON><PERSON> como rascunho", "copy": "Copiar", "copied": "Copiado", "duplicate": "Duplicar", "publish": "Publicar", "create": "<PERSON><PERSON><PERSON>", "delete": "Excluir", "remove": "Remover", "revoke": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "forceConfirm": "Confirmar <PERSON>ça<PERSON><PERSON>", "continueEdit": "Continuar edição", "enable": "Habilitar", "disable": "Desabilitar", "undo": "<PERSON><PERSON><PERSON>", "complete": "Concluir", "viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "back": "Voltar", "close": "<PERSON><PERSON><PERSON>", "showMore": "<PERSON><PERSON> mais", "continue": "<PERSON><PERSON><PERSON><PERSON>", "continueWithEmail": "Continuar com Email", "idCopiedToClipboard": "ID copiado para a área de transferência", "addReason": "Adicionar motivo", "addNote": "<PERSON><PERSON><PERSON><PERSON> nota", "reset": "Redefinir", "confirm": "Confirmar", "edit": "<PERSON><PERSON>", "addItems": "<PERSON><PERSON><PERSON><PERSON> itens", "download": "Baixar", "clear": "Limpar", "clearAll": "<PERSON><PERSON> tudo", "apply": "Aplicar", "add": "<PERSON><PERSON><PERSON><PERSON>", "select": "Selecionar", "browse": "Procurar", "logout": "<PERSON><PERSON>", "hide": "Ocultar", "export": "Exportar", "import": "Importar"}, "operators": {"in": "Em"}, "app": {"search": {"label": "Buscar", "title": "Buscar", "description": "Pesquise em toda a sua loja, incluindo pedidos, produtos, clientes e mais.", "allAreas": "<PERSON><PERSON> as <PERSON><PERSON><PERSON>", "navigation": "Navegação", "openResult": "Abrir resultado", "showMore": "<PERSON><PERSON> mais", "placeholder": "Vá para ou encontre qualquer coisa...", "noResultsTitle": "Nenhum resultado encontrado", "noResultsMessage": "Não conseguimos encontrar nada que correspondesse à sua pesquisa.", "emptySearchTitle": "Digite para buscar", "emptySearchMessage": "Digite uma palavra-chave ou frase para explorar.", "loadMore": "Carregar {{count}} mais", "groups": {"all": "<PERSON><PERSON> as <PERSON><PERSON><PERSON>", "customer": "Clientes", "customerGroup": "Grupos de Clientes", "product": "<PERSON><PERSON><PERSON>", "productVariant": "V<PERSON>tes de Produto", "inventory": "Inventário", "reservation": "<PERSON><PERSON><PERSON>", "category": "Categorias", "collection": "Coleções", "order": "Pedidos", "promotion": "Promoções", "campaign": "<PERSON><PERSON><PERSON>", "priceList": "Listas de Preços", "user": "Usuários", "region": "Regiões", "taxRegion": "Regiões <PERSON>", "returnReason": "Motivos de Devolução", "salesChannel": "Canais de Vendas", "productType": "Tipos de Produto", "productTag": "Tags de Produto", "location": "Localizações", "shippingProfile": "<PERSON><PERSON><PERSON>", "publishableApiKey": "Chaves de API Publicáveis", "secretApiKey": "Chaves de API Secretas", "command": "<PERSON><PERSON><PERSON>", "navigation": "Navegação"}}, "keyboardShortcuts": {"pageShortcut": "<PERSON>r <PERSON>", "settingShortcut": "Configurações", "commandShortcut": "<PERSON><PERSON><PERSON>", "then": "então", "navigation": {"goToOrders": "Pedidos", "goToProducts": "<PERSON><PERSON><PERSON>", "goToCollections": "Coleções", "goToCategories": "Categorias", "goToCustomers": "Clientes", "goToCustomerGroups": "Grupos de Clientes", "goToInventory": "Inventário", "goToReservations": "<PERSON><PERSON><PERSON>", "goToPriceLists": "Listas de Preços", "goToPromotions": "Promoções", "goToCampaigns": "<PERSON><PERSON><PERSON>"}, "settings": {"goToSettings": "Configurações", "goToStore": "<PERSON><PERSON>", "goToUsers": "Usuários", "goToRegions": "Regiões", "goToTaxRegions": "Regiões <PERSON>", "goToSalesChannels": "Canais de Vendas", "goToProductTypes": "Tipos de Produto", "goToLocations": "Localizações", "goToPublishableApiKeys": "Chaves de API Publicáveis", "goToSecretApiKeys": "Chaves de API Secretas", "goToWorkflows": "Fluxos de Trabalho", "goToProfile": "Perfil", "goToReturnReasons": "Motivos de Devolução"}}, "menus": {"user": {"documentation": "Documentação", "changelog": "Registro de Alterações", "shortcuts": "Atalhos", "profileSettings": "Configurações do perfil", "theme": {"label": "<PERSON><PERSON>", "dark": "Escuro", "light": "<PERSON><PERSON><PERSON>", "system": "Sistema"}}, "store": {"label": "<PERSON><PERSON>", "storeSettings": "Configurações da loja"}, "actions": {"logout": "<PERSON><PERSON>"}}, "nav": {"accessibility": {"title": "Navegação", "description": "Menu de navegação para o painel."}, "common": {"extensions": "Extensões"}, "main": {"store": "<PERSON><PERSON>", "storeSettings": "Configurações da loja"}, "settings": {"header": "Configurações", "general": "G<PERSON>", "developer": "<PERSON><PERSON><PERSON><PERSON>", "myAccount": "<PERSON><PERSON>"}}}, "dataGrid": {"columns": {"view": "Visualizar", "resetToDefault": "<PERSON><PERSON><PERSON>", "disabled": "Alterar quais colunas são visíveis está desativado."}, "shortcuts": {"label": "Atalhos", "commands": {"undo": "<PERSON><PERSON><PERSON>", "redo": "<PERSON><PERSON><PERSON>", "copy": "Copiar", "paste": "Colar", "edit": "<PERSON><PERSON>", "delete": "Excluir", "clear": "Limpar", "moveUp": "Mover para cima", "moveDown": "Mover para baixo", "moveLeft": "Mover para a esquerda", "moveRight": "Mover para a direita", "moveTop": "Mover para o topo", "moveBottom": "Mover para o fundo", "selectDown": "Selecionar para baixo", "selectUp": "Selecionar para cima", "selectColumnDown": "Selecionar coluna para baixo", "selectColumnUp": "Selecionar coluna para cima", "focusToolbar": "Focar na barra de ferramentas", "focusCancel": "Focar no cancelamento"}}, "errors": {"fixError": "<PERSON><PERSON><PERSON><PERSON> erro", "count_one": "{{count}} erro", "count_other": "{{count}} erros"}}, "filters": {"date": {"today": "Hoje", "lastSevenDays": "Últimos 7 dias", "lastThirtyDays": "Últimos 30 dias", "lastNinetyDays": "Últimos 90 dias", "lastTwelveMonths": "Últimos 12 meses", "custom": "Personalizado", "from": "De", "to": "<PERSON><PERSON>"}, "compare": {"lessThan": "<PERSON><PERSON> que", "greaterThan": "<PERSON><PERSON> que", "exact": "Exato", "range": "Faixa", "lessThanLabel": "menor que {{value}}", "greaterThanLabel": "maior que {{value}}", "andLabel": "e"}, "radio": {"yes": "<PERSON>m", "no": "Não", "true": "<PERSON><PERSON><PERSON><PERSON>", "false": "<PERSON><PERSON><PERSON>"}, "addFilter": "<PERSON><PERSON><PERSON><PERSON> filtro"}, "errorBoundary": {"badRequestTitle": "400 - Solicitação inválida", "badRequestMessage": "A solicitação não pôde ser compreendida pelo servidor devido a uma sintaxe malformada.", "notFoundTitle": "404 - <PERSON><PERSON> há página neste endereço", "notFoundMessage": "Verifique a URL e tente novamente, ou use a barra de pesquisa para encontrar o que você está procurando.", "internalServerErrorTitle": "500 - Erro interno do servidor", "internalServerErrorMessage": "Ocorreu um erro inesperado no servidor. Tente novamente mais tarde.", "defaultTitle": "Ocorreu um erro", "defaultMessage": "Ocorreu um erro inesperado ao renderizar esta página.", "noMatchMessage": "A página que você está procurando não existe.", "backToDashboard": "Voltar para o painel"}, "addresses": {"title": "Endereços", "shippingAddress": {"header": "Endereço de entrega", "editHeader": "Editar endereço de entrega", "editLabel": "Endereço de entrega", "label": "Endereço de entrega"}, "billingAddress": {"header": "Endereço de cobrança", "editHeader": "Editar endereço de cobrança", "editLabel": "Endereço de cobrança", "label": "Endereço de cobrança", "sameAsShipping": "Mesmo que o endereço de entrega"}, "contactHeading": "Contato", "locationHeading": "Localização"}, "email": {"editHeader": "Editar e-mail", "editLabel": "E-mail", "label": "E-mail"}, "transferOwnership": {"header": "Transferir propriedade", "label": "Transferir propriedade", "details": {"order": "Detalhes do pedido", "draft": "Detalhes do rascunho"}, "currentOwner": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "O proprietário atual do pedido."}, "newOwner": {"label": "Novo proprietário", "hint": "O novo proprietário para o qual o pedido será transferido."}, "validation": {"mustBeDifferent": "O novo proprietário deve ser diferente do proprietário atual.", "required": "Novo proprietário é obrigatório."}}, "sales_channels": {"availableIn": "Disponível em <0>{{x}}</0> de <1>{{y}}</1> canais de vendas"}, "products": {"domain": "<PERSON><PERSON><PERSON>", "list": {"noRecordsMessage": "Crie seu primeiro produto para começar a vender."}, "edit": {"header": "<PERSON>ar produto", "description": "Edite os detalhes do produto.", "successToast": "Produto {{title}} foi atualizado com sucesso."}, "create": {"title": "<PERSON><PERSON><PERSON> produto", "description": "Crie um novo produto.", "header": "G<PERSON>", "tabs": {"details": "<PERSON><PERSON><PERSON>", "organize": "Organizar", "variants": "<PERSON><PERSON><PERSON>", "inventory": "Kits de inventário"}, "errors": {"variants": "Por favor, selecione pelo menos uma variante.", "options": "Por favor, crie pelo menos uma opção.", "uniqueSku": "SKU deve ser único."}, "inventory": {"heading": "Kits de inventário", "label": "Adicione itens de inventário ao kit de inventário da variante.", "itemPlaceholder": "Selecione o item de inventário", "quantityPlaceholder": "Quantos desses são necessários para o kit?"}, "variants": {"header": "<PERSON><PERSON><PERSON>", "subHeadingTitle": "Sim, este é um produto com variantes", "subHeadingDescription": "<PERSON><PERSON><PERSON>, criaremos uma variante padrão para você", "optionTitle": {"placeholder": "<PERSON><PERSON><PERSON>"}, "optionValues": {"placeholder": "Pequeno, Médio, Grande"}, "productVariants": {"label": "Variantes do produto", "hint": "Essa classificação afetará a ordem das variantes na sua vitrine.", "alert": "Adicione opções para criar variantes.", "tip": "Variantes desmarcadas não serão criadas. Você pode sempre criar e editar variantes depois, mas essa lista organiza as variações nas suas opções de produto."}, "productOptions": {"label": "Opções do produto", "hint": "Defina as opções para o produto, por exemplo, cor, tamanho, etc."}}, "successToast": "Produto {{title}} foi criado com sucesso."}, "export": {"header": "Exportar lista de produtos", "description": "Exporte a lista de produtos para um arquivo CSV.", "success": {"title": "Estamos processando sua exportação", "description": "A exportação de dados pode levar alguns minutos. Vamos notificar você quando terminarmos."}, "filters": {"title": "<PERSON><PERSON><PERSON>", "description": "Aplique filtros na visão geral da tabela para ajustar essa visualização"}, "columns": {"title": "Colunas", "description": "Personalize os dados exportados para atender a necessidades específicas"}}, "import": {"header": "Importar lista de produtos", "uploadLabel": "Importar produtos", "uploadHint": "Arraste e solte um arquivo CSV ou clique para enviar", "description": "Importe produtos fornecendo um arquivo CSV em um formato pré-definido", "template": {"title": "Não tem certeza de como organizar sua lista?", "description": "Baixe o modelo abaixo para garantir que está seguindo o formato correto."}, "upload": {"title": "Envie um arquivo CSV", "description": "Por meio de importações, você pode adicionar ou atualizar produtos. Para atualizar produtos existentes, você deve usar o identificador e ID existentes, para atualizar variantes existentes, você deve usar o ID existente. Você será solicitado a confirmação antes de importarmos os produtos.", "preprocessing": "Pré-processando...", "productsToCreate": "Produtos serão criados", "productsToUpdate": "Produtos serão atualizados"}, "success": {"title": "Estamos processando sua importação", "description": "A importação de dados pode demorar um pouco. Vamos notificar você quando terminarmos."}}, "deleteWarning": "Você está prestes a excluir o produto {{title}}. Esta ação não pode ser desfeita.", "variants": {"header": "<PERSON><PERSON><PERSON>", "empty": {"heading": "Nenhuma variante", "description": "Não há variantes para exibir."}, "filtered": {"heading": "Nenhuma variante", "description": "Nenhuma variante corresponde aos critérios de filtro atuais."}}, "attributes": "Atributos", "editAttributes": "<PERSON>ar at<PERSON>", "editOptions": "Editar <PERSON>", "editPrices": "<PERSON><PERSON>", "media": {"label": "Mí<PERSON>", "editHint": "Adicione mídias ao produto para exibi-lo em sua vitrine.", "makeThumbnail": "Criar miniatura", "uploadImagesLabel": "Enviar imagens", "uploadImagesHint": "<PERSON><PERSON><PERSON> e solte as imagens aqui ou clique para enviar.", "invalidFileType": "'{{name}}' não é um tipo de arquivo suportado. Os tipos de arquivos suportados são: {{types}}.", "failedToUpload": "Falha ao enviar a mídia adicionada. Tente novamente.", "deleteWarning_one": "Você está prestes a excluir {{count}} imagem. Esta ação não pode ser desfeita.", "deleteWarning_other": "Você está prestes a excluir {{count}} imagens. Esta ação não pode ser desfeita.", "deleteWarningWithThumbnail_one": "Você está prestes a excluir {{count}} imagem, incluindo a miniatura. Esta ação não pode ser desfeita.", "deleteWarningWithThumbnail_other": "Você está prestes a excluir {{count}} imagens, incluindo a miniatura. Esta ação não pode ser desfeita.", "thumbnailTooltip": "Miniatura", "galleryLabel": "Galeria", "downloadImageLabel": "Baixar imagem atual", "deleteImageLabel": "Excluir imagem atual", "emptyState": {"header": "Sem mídia ainda", "description": "Adicione mídias ao produto para exibi-lo em sua vitrine.", "action": "<PERSON><PERSON><PERSON><PERSON>"}, "successToast": "Mídia foi atualizada com sucesso."}, "discountableHint": "<PERSON>uan<PERSON>, os descontos não serão aplicados a este produto.", "noSalesChannels": "Não disponível em nenhum canal de vendas", "variantCount_one": "{{count}} variante", "variantCount_other": "{{count}} variantes", "deleteVariantWarning": "Você está prestes a excluir a variante {{title}}. Esta ação não pode ser desfeita.", "productStatus": {"draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "published": "Publicado", "proposed": "Proposto", "rejected": "<PERSON><PERSON><PERSON><PERSON>"}, "fields": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Dê ao seu produto um título curto e claro.<0/>50-60 caracteres é o comprimento recomendado para motores de busca.", "placeholder": "Jaqueta de inverno"}, "subtitle": {"label": "Subtítulo", "placeholder": "Quente e aconchegante"}, "handle": {"label": "Identificador", "tooltip": "O identificador é usado para referenciar o produto na sua vitrine. Se não especificado, o identificador será gerado a partir do título do produto.", "placeholder": "jaqueta-de-inverno"}, "description": {"label": "Descrição", "hint": "Dê ao seu produto uma descrição curta e clara.<0/>120-160 caracteres é o comprimento recomendado para motores de busca.", "placeholder": "Uma jaqueta quente e aconchegante"}, "discountable": {"label": "<PERSON><PERSON>t<PERSON>vel", "hint": "<PERSON>uan<PERSON>, os descontos não serão aplicados a este produto"}, "type": {"label": "Tipo"}, "collection": {"label": "Coleção"}, "categories": {"label": "Categorias"}, "tags": {"label": "Tags"}, "sales_channels": {"label": "Canais de vendas", "hint": "Este produto estará disponível apenas no canal de vendas padrão, se não for alterado."}, "countryOrigin": {"label": "<PERSON><PERSON> or<PERSON>"}, "material": {"label": "Material"}, "width": {"label": "<PERSON><PERSON><PERSON>"}, "length": {"label": "Comprimento"}, "height": {"label": "Altura"}, "weight": {"label": "Peso"}, "options": {"label": "Opções de produto", "hint": "As opções são usadas para definir a cor, o tamanho, etc. do produto", "add": "Adicionar <PERSON>", "optionTitle": "Título da opção", "optionTitlePlaceholder": "Cor", "variations": "Variações (separadas por vírgula)", "variantionsPlaceholder": "Vermelho, Azul, Verde"}, "variants": {"label": "Variantes do produto", "hint": "Variantes desmarcadas não serão criadas. Esta classificação afetará a ordem das variantes no seu front-end."}, "mid_code": {"label": "Código Mid"}, "hs_code": {"label": "Código HS"}}, "variant": {"edit": {"header": "Editar variante", "success": "Variante do produto editada com sucesso"}, "create": {"header": "<PERSON>al<PERSON> da variante"}, "deleteWarning": "Você tem certeza de que deseja excluir esta variante?", "pricesPagination": "1 - {{current}} de {{total}} preços", "tableItemAvailable": "{{availableCount}} disponível", "tableItem_one": "{{availableCount}} disponível em {{locationCount}} local", "tableItem_other": "{{availableCount}} disponível em {{locationCount}} locais", "inventory": {"notManaged": "Não gerenciado", "manageItems": "Gerenciar itens de inventário", "notManagedDesc": "O inventário não é gerenciado para esta variante. Ative ‘Gerenciar Inventário’ para controlar o inventário da variante.", "manageKit": "Gerenciar kit de inventário", "navigateToItem": "Ir para item de inventário", "actions": {"inventoryItems": "Ir para item de inventário", "inventoryKit": "Mostrar itens de inventário"}, "inventoryKit": "<PERSON> inventário", "inventoryKitHint": "Esta variante consiste em vários itens de inventário?", "validation": {"itemId": "Por favor, selecione um item de inventário.", "quantity": "Quantidade é obrigatória. Por favor, insira um número positivo."}, "header": "Estoque e inventário", "editItemDetails": "Editar de<PERSON>hes do item", "manageInventoryLabel": "Gerenciar inventário", "manageInventoryHint": "Quando ativado, vamos alterar a quantidade de inventário para você quando pedidos e devoluções forem criados.", "allowBackordersLabel": "<PERSON><PERSON><PERSON> pedido<PERSON> em atraso", "allowBackordersHint": "<PERSON>uando ativado, os clientes podem comprar a variante mesmo sem estoque disponível.", "toast": {"levelsBatch": "Níveis de inventário atualizados.", "update": "Item de inventário atualizado com sucesso.", "updateLevel": "Nível de inventário atualizado com sucesso.", "itemsManageSuccess": "Itens de inventário atualizados com sucesso."}}}, "options": {"header": "Opções", "edit": {"header": "Editar <PERSON>", "successToast": "Opção {{title}} foi atualizada com sucesso."}, "create": {"header": "Criar <PERSON>", "successToast": "Opção {{title}} foi criada com sucesso."}, "deleteWarning": "Você está prestes a excluir a opção de produto: {{title}}. Esta ação não pode ser desfeita."}, "organization": {"header": "Organizar", "edit": {"header": "Editar organização", "toasts": {"success": "Organização de {{title}} atualizada com sucesso."}}}, "toasts": {"delete": {"success": {"header": "Produto excluído", "description": "{{title}} foi excluído com sucesso."}, "error": {"header": "Falha ao excluir produto"}}}}, "collections": {"domain": "Coleções", "subtitle": "Organize os produtos em coleções.", "createCollection": "<PERSON><PERSON><PERSON>", "createCollectionHint": "Crie uma nova coleção para organizar seus produtos.", "createSuccess": "Coleção criada com sucesso.", "editCollection": "<PERSON><PERSON>", "handleTooltip": "O identificador é usado para referenciar a coleção na sua vitrine. Se não especificado, o identificador será gerado a partir do título da coleção.", "deleteWarning": "Você está prestes a excluir a coleção {{title}}. Esta ação não pode ser desfeita.", "removeSingleProductWarning": "Você está prestes a remover o produto {{title}} da coleção. Esta ação não pode ser desfeita.", "removeProductsWarning_one": "Você está prestes a remover {{count}} produto da coleção. Esta ação não pode ser desfeita.", "removeProductsWarning_other": "Você está prestes a remover {{count}} produtos da coleção. Esta ação não pode ser desfeita.", "products": {"list": {"noRecordsMessage": "Não há produtos na coleção."}, "add": {"successToast_one": "Produto adicionado com sucesso à coleção.", "successToast_other": "Produtos adicionados com sucesso à coleção."}, "remove": {"successToast_one": "Produto removido com sucesso da coleção.", "successToast_other": "Produtos removidos com sucesso da coleção."}}}, "categories": {"domain": "Categorias", "subtitle": "Organize os produtos em categorias e gerencie o ranking e a hierarquia dessas categorias.", "create": {"header": "Criar Categoria", "hint": "Crie uma nova categoria para organizar seus produtos.", "tabs": {"details": "<PERSON><PERSON><PERSON>", "organize": "Organizar Ranking"}, "successToast": "Categoria {{name}} criada com sucesso."}, "edit": {"header": "Editar Categoria", "description": "Edite a categoria para atualizar seus detalhes.", "successToast": "Categoria atualizada com sucesso."}, "delete": {"confirmation": "Você está prestes a excluir a categoria {{name}}. Esta ação não pode ser desfeita.", "successToast": "Categoria {{name}} excluída com sucesso."}, "products": {"add": {"disabledTooltip": "O produto já está nesta categoria.", "successToast_one": "Adicionado {{count}} produto à categoria.", "successToast_other": "Adicionados {{count}} produtos à categoria."}, "remove": {"confirmation_one": "Você está prestes a remover {{count}} produto da categoria. Esta ação não pode ser desfeita.", "confirmation_other": "Você está prestes a remover {{count}} produtos da categoria. Esta ação não pode ser desfeita.", "successToast_one": "Removido {{count}} produto da categoria.", "successToast_other": "Removid<PERSON> {{count}} produtos da categoria."}, "list": {"noRecordsMessage": "Não há produtos nesta categoria."}}, "organize": {"header": "Organizar", "action": "Editar ranking"}, "fields": {"visibility": {"label": "Visibilidade", "internal": "Interno", "public": "Público"}, "status": {"label": "Status", "active": "Ativo", "inactive": "Inativo"}, "path": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "Exibir o caminho completo da categoria."}, "children": {"label": "<PERSON>l<PERSON>"}, "new": {"label": "Novo"}}}, "inventory": {"domain": "Inventário", "subtitle": "Gerencie os itens do seu inventário", "reserved": "Reservado", "available": "Disponível", "locationLevels": "Localizações", "associatedVariants": "<PERSON><PERSON><PERSON> associadas", "manageLocations": "Gerenciar localizações", "deleteWarning": "Você está prestes a excluir um item do inventário. Esta ação não pode ser desfeita.", "editItemDetails": "Editar de<PERSON>hes do item", "create": {"title": "<PERSON><PERSON><PERSON> <PERSON> Inventário", "details": "<PERSON><PERSON><PERSON>", "availability": "Disponibilidade", "locations": "Localizações", "attributes": "Atributos", "requiresShipping": "<PERSON><PERSON> envio", "requiresShippingHint": "O item de inventário requer envio?", "successToast": "Item de inventário criado com sucesso."}, "reservation": {"header": "Reserva de {{itemName}}", "editItemDetails": "Editar reserva", "lineItemId": "ID do item de linha", "orderID": "ID do pedido", "description": "Descrição", "location": "Localização", "inStockAtLocation": "Em estoque nesta localização", "availableAtLocation": "Disponível nesta localização", "reservedAtLocation": "Reservado nesta localização", "reservedAmount": "Quantidade reservada", "create": "C<PERSON>r reserva", "itemToReserve": "Item a reservar", "quantityPlaceholder": "Quanto você deseja reservar?", "descriptionPlaceholder": "Qual é o tipo de reserva?", "successToast": "Reserva criada com sucesso.", "updateSuccessToast": "Reserva atualizada com sucesso.", "deleteSuccessToast": "Reserva excluída com sucesso.", "errors": {"noAvaliableQuantity": "A localização do estoque não tem quantidade disponível.", "quantityOutOfRange": "A quantidade mínima é 1 e a máxima é {{max}}"}}, "toast": {"updateLocations": "Localizações atualizadas com sucesso.", "updateLevel": "Nível de inventário atualizado com sucesso.", "updateItem": "Item de inventário atualizado com sucesso."}}, "giftCards": {"domain": "Car<PERSON><PERSON><PERSON> Presente", "editGiftCard": "<PERSON><PERSON>", "createGiftCard": "<PERSON><PERSON><PERSON>", "createGiftCardHint": "Crie manualmente um cartão de presente que pode ser usado como método de pagamento na sua loja.", "selectRegionFirst": "Selecione uma região primeiro", "deleteGiftCardWarning": "Você está prestes a excluir o cartão de presente {{code}}. Esta ação não pode ser desfeita.", "balanceHigherThanValue": "O saldo não pode ser superior ao valor original.", "balanceLowerThanZero": "O saldo não pode ser negativo.", "expiryDateHint": "Os países têm leis diferentes sobre as datas de validade dos cartões de presente. Verifique as regulamentações locais antes de definir uma data de validade.", "regionHint": "Alterar a região do cartão de presente também alterará sua moeda, afetando potencialmente seu valor monetário.", "enabledHint": "Especifique se o cartão de presente está habilitado ou desabilitado.", "balance": "<PERSON><PERSON>", "currentBalance": "<PERSON><PERSON>", "initialBalance": "<PERSON><PERSON> inicial", "personalMessage": "Mensagem pessoal", "recipient": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "customers": {"domain": "Clientes", "list": {"noRecordsMessage": "Seus clientes aparecerão aqui."}, "create": {"header": "<PERSON><PERSON><PERSON>", "hint": "Crie um novo cliente e gerencie seus detalhes.", "successToast": "Cliente {{email}} criado com sucesso."}, "groups": {"label": "Grupos de clientes", "remove": "Tem certeza de que deseja remover o cliente do grupo \"{{name}}\"?", "removeMany": "Tem certeza de que deseja remover o cliente dos seguintes grupos de clientes: {{groups}}?", "alreadyAddedTooltip": "O cliente já está neste grupo de clientes.", "list": {"noRecordsMessage": "Este cliente não pertence a nenhum grupo."}, "add": {"success": "Cliente adicionado a: {{groups}}.", "list": {"noRecordsMessage": "Por favor, crie um grupo de clientes primeiro."}}, "removed": {"success": "Cliente removido de: {{groups}}.", "list": {"noRecordsMessage": "Por favor, crie um grupo de clientes primeiro."}}}, "edit": {"header": "<PERSON><PERSON>", "emailDisabledTooltip": "O endereço de e-mail não pode ser alterado para clientes registrados.", "successToast": "Cliente {{email}} atualizado com sucesso."}, "delete": {"title": "Excluir Cliente", "description": "Você está prestes a excluir o cliente {{email}}. Esta ação não pode ser desfeita.", "successToast": "Cliente {{email}} excluído com sucesso."}, "fields": {"guest": "Convidado", "registered": "Registrado", "groups": "Grupos"}, "registered": "Registrado", "guest": "Convidado", "hasAccount": "Tem conta"}, "customerGroups": {"domain": "Grupos de Clientes", "subtitle": "Organize os clientes em grupos. Os grupos podem ter promoções e preços diferentes.", "create": {"header": "Criar Grupo de Clientes", "hint": "Crie um novo grupo de clientes para segmentar seus clientes.", "successToast": "Grupo de clientes {{name}} criado com sucesso."}, "edit": {"header": "Editar Grupo de Clientes", "successToast": "Grupo de clientes {{name}} atualizado com sucesso."}, "delete": {"title": "Excluir Grupo de Clientes", "description": "Você está prestes a excluir o grupo de clientes {{name}}. Esta ação não pode ser desfeita.", "successToast": "Grupo de clientes {{name}} excluído com sucesso."}, "customers": {"alreadyAddedTooltip": "O cliente já foi adicionado ao grupo.", "add": {"successToast_one": "Cliente adicionado com sucesso ao grupo.", "successToast_other": "Clientes adicionados com sucesso ao grupo.", "list": {"noRecordsMessage": "Crie um cliente primeiro."}}, "remove": {"title_one": "Remover cliente", "title_other": "Remover clientes", "description_one": "Você está prestes a remover {{count}} cliente do grupo de clientes. Esta ação não pode ser desfeita.", "description_other": "Você está prestes a remover {{count}} clientes do grupo de clientes. Esta ação não pode ser desfeita."}, "list": {"noRecordsMessage": "Este grupo não tem clientes."}}}, "orders": {"domain": "Pedidos", "claim": "Reclamação", "exchange": "Troca", "return": "Devolução", "cancelWarning": "Você está prestes a cancelar o pedido {{id}}. Esta ação não pode ser desfeita.", "onDateFromSalesChannel": "{{date}} de {{salesChannel}}", "list": {"noRecordsMessage": "Seus pedidos aparecerão aqui."}, "summary": {"requestReturn": "Solicitar devolução", "allocateItems": "Alocar itens", "editOrder": "<PERSON><PERSON> pedido", "editOrderContinue": "Continuar edição do pedido", "inventoryKit": "Consiste em {{count}}x itens de inventário", "itemTotal": "Total dos itens", "shippingTotal": "Total do envio", "discountTotal": "Total de descontos", "taxTotalIncl": "Total de impostos (incluído)", "itemSubtotal": "Subtotal dos itens", "shippingSubtotal": "Subtotal do envio", "discountSubtotal": "Subtotal de descontos", "taxTotal": "Total de impostos"}, "transfer": {"title": "Transferir propriedade", "requestSuccess": "Solicitação de transferência do pedido enviada para: {{email}}.", "currentOwner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newOwner": "Novo proprietário", "currentOwnerDescription": "O cliente atualmente relacionado a este pedido.", "newOwnerDescription": "O cliente para o qual este pedido será transferido."}, "payment": {"title": "Pagamentos", "isReadyToBeCaptured": "Pagamento <0/> está pronto para ser capturado.", "totalPaidByCustomer": "Total pago pelo cliente", "capture": "<PERSON><PERSON><PERSON> paga<PERSON>", "capture_short": "Capturar", "refund": "Reembolso", "markAsPaid": "Marcar como pago", "statusLabel": "Status do pagamento", "statusTitle": "Status do pagamento", "status": {"notPaid": "Não pago", "authorized": "Autorizado", "partiallyAuthorized": "Parcialmente autorizado", "awaiting": "Aguardando", "captured": "<PERSON><PERSON><PERSON>", "partiallyRefunded": "Parcialmente reembolsado", "partiallyCaptured": "Parcialmente capturado", "refunded": "Reembolsado", "canceled": "Cancelado", "requiresAction": "Requer ação"}, "capturePayment": "O pagamento de {{amount}} será capturado.", "capturePaymentSuccess": "Pagamento de {{amount}} capturado com sucesso.", "markAsPaidPayment": "O pagamento de {{amount}} será marcado como pago.", "markAsPaidPaymentSuccess": "Pagamento de {{amount}} marcado como pago com sucesso.", "createRefund": "<PERSON><PERSON><PERSON>", "refundPaymentSuccess": "Reembolso de {{amount}} realizado com sucesso.", "createRefundWrongQuantity": "A quantidade deve ser um número entre 1 e {{number}}", "refundAmount": "<PERSON><PERSON><PERSON><PERSON> de {{amount}}", "paymentLink": "Copiar link de pagamento para {{amount}}", "selectPaymentToRefund": "Selecione o pagamento para reembolso"}, "edits": {"title": "<PERSON><PERSON> pedido", "confirm": "Confirmar edição", "confirmText": "Você está prestes a confirmar uma edição de pedido. Esta ação não pode ser desfeita.", "cancel": "Cancelar edição", "currentItems": "<PERSON><PERSON> atuais", "currentItemsDescription": "Ajuste a quantidade dos itens ou remova-os.", "addItemsDescription": "Você pode adicionar novos itens ao pedido.", "addItems": "<PERSON><PERSON><PERSON><PERSON> itens", "amountPaid": "Valor pago", "newTotal": "Novo total", "differenceDue": "Diferença devida", "create": "<PERSON><PERSON> pedido", "currentTotal": "Total atual", "noteHint": "Adicione uma nota interna para a edição", "cancelSuccessToast": "Edição de pedido cancelada", "createSuccessToast": "Solicitação de edição de pedido criada", "activeChangeError": "Já existe uma alteração ativa no pedido (devolução, reclamação, troca etc.). Por favor, finalize ou cancele a alteração antes de editar o pedido.", "panel": {"title": "Edição de pedido solicitada", "titlePending": "Edição de pedido pendente"}, "toast": {"canceledSuccessfully": "Edição de pedido cancelada com sucesso.", "confirmedSuccessfully": "Edição de pedido confirmada com sucesso."}, "validation": {"quantityLowerThanFulfillment": "Não é possível definir a quantidade como menor ou igual à quantidade cumprida"}}, "returns": {"create": "<PERSON><PERSON><PERSON>", "confirm": "Confirma<PERSON>", "confirmText": "Você está prestes a confirmar uma devolução. Esta ação não pode ser desfeita.", "inbound": "Entrada", "outbound": "<PERSON><PERSON><PERSON>", "sendNotification": "Enviar notificação", "sendNotificationHint": "Notifique o cliente sobre a devolução.", "returnTotal": "Total da devolução", "inboundTotal": "Total de entrada", "refundAmount": "Valor do reembolso", "outstandingAmount": "Valor pendente", "reason": "Motivo", "reasonHint": "Escolha o motivo pelo qual o cliente deseja devolver os itens.", "note": "<PERSON>a", "noInventoryLevel": "Sem nível de inventário", "noInventoryLevelDesc": "A localização selecionada não possui nível de inventário para os itens selecionados. A devolução pode ser solicitada, mas não pode ser recebida até que um nível de inventário seja criado para a localização selecionada.", "noteHint": "Você pode digitar livremente caso queira especificar algo.", "location": "Localização", "locationHint": "Escolha a localização para a qual deseja devolver os itens.", "inboundShipping": "<PERSON>vio de devolução", "inboundShippingHint": "Escolha o método de envio que deseja usar.", "returnableQuantityLabel": "Quantidade devolvível", "refundableAmountLabel": "Valor reembolsável", "returnRequestedInfo": "{{requestedItemsCount}}x itens de devolução solicitados", "returnReceivedInfo": "{{requestedItemsCount}}x itens de devolução recebidos", "itemReceived": "<PERSON><PERSON> recebidos", "returnRequested": "Devolução solicitada", "damagedItemReceived": "Itens danificados recebidos", "damagedItemsReturned": "{{quantity}}x itens danificados devolvidos", "activeChangeError": "Há uma alteração ativa no pedido em andamento. Por favor, finalize ou descarte a alteração primeiro.", "cancel": {"title": "<PERSON><PERSON>ar <PERSON>", "description": "Tem certeza de que deseja cancelar a solicitação de devolução?"}, "placeholders": {"noReturnShippingOptions": {"title": "Nenhuma opção de envio de devolução encontrada", "hint": "Nenhuma opção de envio de devolução foi criada para a localização. Você pode criar uma em <LinkComponent>Localização & Envio</LinkComponent>."}, "outboundShippingOptions": {"title": "Nenhuma opção de envio de saída encontrada", "hint": "Nenhuma opção de envio de saída foi criada para a localização. Você pode criar uma em <LinkComponent>Localização & Envio</LinkComponent>."}}, "receive": {"action": "Receber itens", "receiveItems": "{{returnType}} {{id}}", "restockAll": "Repor todos os itens", "itemsLabel": "<PERSON><PERSON> recebidos", "title": "Receber itens para #{{returnId}}", "sendNotificationHint": "Notifique o cliente sobre a devolução recebida.", "inventoryWarning": "Por favor, observe que ajustaremos automaticamente os níveis de inventário com base no seu input acima.", "writeOffInputLabel": "Quantos itens estão danificados?", "toast": {"success": "Devolução recebida com sucesso.", "errorLargeValue": "A quantidade informada é maior do que a quantidade solicitada de itens.", "errorNegativeValue": "A quantidade não pode ser um valor negativo.", "errorLargeDamagedValue": "A quantidade de itens danificados + a quantidade de itens não danificados recebidos excede a quantidade total de itens na devolução. Por favor, diminua a quantidade de itens não danificados."}}, "toast": {"canceledSuccessfully": "Devolução cancelada com sucesso", "confirmedSuccessfully": "Devolução confirmada com sucesso"}, "panel": {"title": "Devolução iniciada", "description": "Há uma solicitação de devolução aberta a ser concluída"}}, "claims": {"create": "<PERSON><PERSON><PERSON>", "confirm": "Confirma<PERSON>", "confirmText": "Você está prestes a confirmar uma reclamação. Esta ação não pode ser desfeita.", "manage": "Gerenciar Reclamação", "outbound": "<PERSON><PERSON><PERSON>", "outboundItemAdded": "{{itemsCount}}x adicionados pela reclamação", "outboundTotal": "Total de saída", "outboundShipping": "<PERSON><PERSON>", "outboundShippingHint": "Escolha o método de envio que deseja usar.", "refundAmount": "Diferença estimada", "activeChangeError": "Há uma alteração ativa no pedido. Por favor, finalize ou descarte a alteração anterior.", "actions": {"cancelClaim": {"successToast": "Reclamação cancelada com sucesso."}}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "description": "Tem certeza de que deseja cancelar a reclamação?"}, "tooltips": {"onlyReturnShippingOptions": "Esta lista consistirá apenas de opções de envio de devolução."}, "toast": {"canceledSuccessfully": "Reclamação cancelada com sucesso", "confirmedSuccessfully": "Reclamação confirmada com sucesso"}, "panel": {"title": "Reclamação iniciada", "description": "Há uma solicitação de reclamação aberta a ser concluída"}}, "exchanges": {"create": "<PERSON><PERSON><PERSON>", "manage": "Gerenciar Troca", "confirm": "Confirmar <PERSON>", "confirmText": "Você está prestes a confirmar uma troca. Esta ação não pode ser desfeita.", "outbound": "<PERSON><PERSON><PERSON>", "outboundItemAdded": "{{itemsCount}}x adicionados através da troca", "outboundTotal": "Total de saída", "outboundShipping": "<PERSON><PERSON>", "outboundShippingHint": "Escolha o método que deseja usar.", "refundAmount": "Diferença estimada", "activeChangeError": "Há uma alteração ativa no pedido. Por favor, finalize ou descarte a alteração anterior.", "actions": {"cancelExchange": {"successToast": "Troca cancelada com sucesso."}}, "cancel": {"title": "<PERSON><PERSON>ar <PERSON>", "description": "Tem certeza de que deseja cancelar a troca?"}, "tooltips": {"onlyReturnShippingOptions": "Esta lista consistirá apenas de opções de envio de devolução."}, "toast": {"canceledSuccessfully": "Troca cancelada com sucesso", "confirmedSuccessfully": "Troca confirmada com sucesso"}, "panel": {"title": "Troca iniciada", "description": "Há uma solicitação de troca aberta a ser concluída"}}, "reservations": {"allocatedLabel": "Alocado", "notAllocatedLabel": "Não alocado"}, "allocateItems": {"action": "Alocar itens", "title": "Alocar itens do pedido", "locationDescription": "Escolha de qual localização você deseja alocar.", "itemsToAllocate": "Itens a alocar", "itemsToAllocateDesc": "Selecione a quantidade de itens que deseja alocar", "search": "Buscar itens", "consistsOf": "Consiste em {{num}}x itens de inventário", "requires": "Exige {{num}} por variante", "toast": {"created": "Itens alocados com sucesso"}, "error": {"quantityNotAllocated": "Existem itens não alocados."}}, "shipment": {"title": "Marcar atendimento como enviado", "trackingNumber": "Número de rastreamento", "addTracking": "Adicionar número de rastreamento", "sendNotification": "Enviar notificação", "sendNotificationHint": "Notificar o cliente sobre este envio.", "toastCreated": "Envio criado com sucesso."}, "fulfillment": {"cancelWarning": "Você está prestes a cancelar um atendimento. Esta ação não pode ser desfeita.", "markAsDeliveredWarning": "Você está prestes a marcar o atendimento como entregue. Esta ação não pode ser desfeita.", "unfulfilledItems": "Itens não atendidos", "statusLabel": "Status do atendimento", "statusTitle": "Status do Atendimento", "fulfillItems": "Atender itens", "awaitingFulfillmentBadge": "Aguardando atendimento", "requiresShipping": "<PERSON><PERSON> envio", "number": "Atendimento #{{number}}", "itemsToFulfill": "Itens a serem atendidos", "create": "<PERSON><PERSON><PERSON>", "available": "Disponível", "inStock": "Em estoque", "markAsShipped": "Marcar como enviado", "markAsDelivered": "Marcar como entregue", "itemsToFulfillDesc": "Escolha os itens e quantidades a serem atendidos", "locationDescription": "Escolha de qual local você deseja atender os itens.", "sendNotificationHint": "Notificar os clientes sobre o atendimento criado.", "methodDescription": "Escolha um método de envio diferente do selecionado pelo cliente", "error": {"wrongQuantity": "Apenas um item está disponível para atendimento", "wrongQuantity_other": "A quantidade deve ser um número entre 1 e {{number}}", "noItems": "Sem itens para atender."}, "status": {"notFulfilled": "<PERSON>ão atendido", "partiallyFulfilled": "<PERSON><PERSON><PERSON><PERSON> atendido", "fulfilled": "Atendido", "partiallyShipped": "Parcialmente enviado", "shipped": "Enviado", "delivered": "<PERSON><PERSON><PERSON>", "partiallyDelivered": "Parcialmente entregue", "partiallyReturned": "Parcialmente devolvido", "returned": "Devol<PERSON><PERSON>", "canceled": "Cancelado", "requiresAction": "Requer ação"}, "toast": {"created": "Atendimento criado com sucesso", "canceled": "Atendimento cancelado com sucesso", "fulfillmentShipped": "Não é possível cancelar um atendimento já enviado", "fulfillmentDelivered": "Atendimento marcado como entregue com sucesso"}, "trackingLabel": "Rastreamento", "shippingFromLabel": "Enviado de", "itemsLabel": "<PERSON><PERSON>"}, "refund": {"title": "<PERSON><PERSON><PERSON>", "sendNotificationHint": "Notificar os clientes sobre o reembolso criado.", "systemPayment": "Pagamento do sistema", "systemPaymentDesc": "Um ou mais dos seus pagamentos é um pagamento do sistema. Observe que capturas e reembolsos não são tratados pela Medusa para esses pagamentos.", "error": {"amountToLarge": "Não é possível reembolsar mais do que o valor original do pedido.", "amountNegative": "O valor do reembolso deve ser um número positivo.", "reasonRequired": "Por favor, selecione um motivo para o reembolso."}}, "customer": {"contactLabel": "Contato", "editEmail": "Editar e-mail", "transferOwnership": "Transferir titularidade", "editBillingAddress": "Editar endereço de cobrança", "editShippingAddress": "Editar endereço de entrega"}, "activity": {"header": "Atividade", "showMoreActivities_one": "Mostrar mais {{count}} atividade", "showMoreActivities_other": "Mostrar mais {{count}} atividades", "comment": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "Deixe um comentário", "addButtonText": "<PERSON><PERSON><PERSON><PERSON>", "deleteButtonText": "Excluir coment<PERSON>"}, "from": "De", "to": "<PERSON><PERSON>", "events": {"common": {"toReturn": "A devolver", "toSend": "A enviar"}, "placed": {"title": "Pedido realizado", "fromSalesChannel": "de {{salesChannel}}"}, "canceled": {"title": "Pedido cancelado"}, "payment": {"awaiting": "<PERSON><PERSON><PERSON><PERSON>aga<PERSON>", "captured": "Pagamento capturado", "canceled": "Pagamento cancelado", "refunded": "Pagamento reembolsado"}, "fulfillment": {"created": "Itens atendidos", "canceled": "Atendimento cancelado", "shipped": "Itens enviados", "delivered": "<PERSON>ens entregues", "items_one": "{{count}} item", "items_other": "{{count}} itens"}, "return": {"created": "Devolução #{{returnId}} solicitada", "canceled": "Devolução #{{returnId}} cancelada", "received": "Devolução #{{returnId}} recebida", "items_one": "{{count}} item devolvido", "items_other": "{{count}} itens devolvidos"}, "note": {"comment": "<PERSON><PERSON><PERSON><PERSON>", "byLine": "por {{author}}"}, "claim": {"created": "Reclamação #{{claimId}} solicitada", "canceled": "Reclamação #{{claimId}} cancelada", "itemsInbound": "{{count}} item a devolver", "itemsOutbound": "{{count}} item a enviar"}, "exchange": {"created": "Troca #{{exchangeId}} solicitada", "canceled": "Troca #{{exchangeId}} cancelada", "itemsInbound": "{{count}} item a devolver", "itemsOutbound": "{{count}} item a enviar"}, "edit": {"requested": "Edição de pedido #{{editId}} solicitada", "confirmed": "Edição de pedido #{{editId}} confirmada"}, "transfer": {"requested": "Transferência do pedido #{{transferId}} solicitada", "confirmed": "Transferência do pedido #{{transferId}} confirmada"}}}, "fields": {"displayId": "ID de exibição", "refundableAmount": "Valor reembolsável", "returnableQuantity": "Quantidade retornável"}}, "draftOrders": {"domain": "Pedidos em Rascunho", "deleteWarning": "Você está prestes a excluir o pedido em rascunho {{id}}. Esta ação não pode ser desfeita.", "paymentLinkLabel": "Link de pagamento", "cartIdLabel": "ID do carrinho", "markAsPaid": {"label": "Marcar como pago", "warningTitle": "Marcar como Pago", "warningDescription": "Você está prestes a marcar o pedido em rascunho como pago. Esta ação não pode ser desfeita, e a coleta de pagamento não será possível posteriormente."}, "status": {"open": "Abe<PERSON>o", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "create": {"createDraftOrder": "<PERSON><PERSON>r Pedido em Rascunho", "createDraftOrderHint": "Crie um novo pedido em rascunho para gerenciar os detalhes de um pedido antes de ser realizado.", "chooseRegionHint": "Escolha a região", "existingItemsLabel": "Itens existentes", "existingItemsHint": "Adicione produtos existentes ao pedido em rascunho.", "customItemsLabel": "Itens personalizados", "customItemsHint": "Adicione itens personalizados ao pedido em rascunho.", "addExistingItemsAction": "Adicionar itens existentes", "addCustomItemAction": "Adicionar item personalizado", "noCustomItemsAddedLabel": "Nenhum item personalizado adicionado ainda", "noExistingItemsAddedLabel": "Nenhum item existente adicionado ainda", "chooseRegionTooltip": "Escolha uma região primeiro", "useExistingCustomerLabel": "Usar cliente existente", "addShippingMethodsAction": "Adicionar <PERSON> envio", "unitPriceOverrideLabel": "Substituir preço unitário", "shippingOptionLabel": "Opção de envio", "shippingOptionHint": "Escolha a opção de envio para o pedido em rascunho.", "shippingPriceOverrideLabel": "Substituir preço de envio", "shippingPriceOverrideHint": "Substitua o preço de envio para o pedido em rascunho.", "sendNotificationLabel": "Enviar notificação", "sendNotificationHint": "Envie uma notificação ao cliente quando o pedido em rascunho for criado."}, "validation": {"requiredEmailOrCustomer": "Email ou cliente é obrigatório.", "requiredItems": "Pelo menos um item é obrigatório.", "invalidEmail": "O email deve ser um endereço de email válido."}}, "stockLocations": {"domain": "Locais e Envio", "list": {"description": "Gerencie os locais de estoque e as opções de envio da sua loja."}, "create": {"header": "Criar Local de Estoque", "hint": "Um local de estoque é um site físico onde os produtos são armazenados e enviados.", "successToast": "Local {{name}} foi criado com sucesso."}, "edit": {"header": "Editar Local de Estoque", "viewInventory": "<PERSON>er inventá<PERSON>", "successToast": "Local {{name}} foi atualizado com sucesso."}, "delete": {"confirmation": "Você está prestes a excluir o local de estoque {{name}}. Esta ação não pode ser desfeita."}, "fulfillmentProviders": {"header": "Provedores de Fulfillment", "shippingOptionsTooltip": "Este menu suspenso consistirá apenas em provedores habilitados para este local. Adicione-os ao local se o menu estiver desabilitado.", "label": "Provedores de fulfillment conectados", "connectedTo": "Conectado a {{count}} de {{total}} provedores de fulfillment", "noProviders": "Este Local de Estoque não está conectado a nenhum provedor de fulfillment.", "action": "Conectar Provedores", "successToast": "Provedores de fulfillment para o local de estoque foram atualizados com sucesso."}, "fulfillmentSets": {"pickup": {"header": "Re<PERSON><PERSON>"}, "shipping": {"header": "<PERSON><PERSON>"}, "disable": {"confirmation": "Tem certeza de que deseja desativar {{name}}? <PERSON><PERSON> excluir<PERSON> to<PERSON> as zonas de serviço e opções de envio associadas, e não poderá ser desfeito.", "pickup": "Retirada foi desativada com sucesso.", "shipping": "Envio foi desativado com sucesso."}, "enable": {"pickup": "Retirada foi ativada com sucesso.", "shipping": "Envio foi ativado com sucesso."}}, "sidebar": {"header": "Configuração de Envio", "shippingProfiles": {"label": "<PERSON><PERSON><PERSON>", "description": "Agrupar produtos por requisitos de envio"}}, "salesChannels": {"header": "Canais de Vendas", "label": "Canais de vendas conectados", "connectedTo": "Conectado a {{count}} de {{total}} canais de vendas", "noChannels": "O local não está conectado a nenhum canal de vendas.", "action": "Conectar canais de vendas", "successToast": "Canais de vendas foram atualizados com sucesso."}, "shippingOptions": {"create": {"shipping": {"header": "Criar Opção de Envio para {{zone}}", "hint": "Crie uma nova opção de envio para definir como os produtos são enviados a partir deste local.", "label": "Opções de envio", "successToast": "Opção de envio {{name}} foi criada com sucesso."}, "returns": {"header": "Criar uma Opção de Devolução para {{zone}}", "hint": "Crie uma nova opção de devolução para definir como os produtos são devolvidos a este local.", "label": "Opções de devolução", "successToast": "Opção de devolução {{name}} foi criada com sucesso."}, "tabs": {"details": "<PERSON><PERSON><PERSON>", "prices": "Preços"}, "action": "Criar <PERSON>"}, "delete": {"confirmation": "Você está prestes a excluir a opção de envio {{name}}. Esta ação não pode ser desfeita.", "successToast": "Opção de envio {{name}} foi excluída com sucesso."}, "edit": {"header": "Editar Opção de Envio", "action": "Editar <PERSON>", "successToast": "Opção de envio {{name}} foi atualizada com sucesso."}, "pricing": {"action": "<PERSON><PERSON>"}, "fields": {"count": {"shipping_one": "{{count}} opção de envio", "shipping_other": "{{count}} opç<PERSON>es de envio", "returns_one": "{{count}} opção de devolução", "returns_other": "{{count}} opções de devolução"}, "priceType": {"label": "Tipo de preço", "options": {"fixed": {"label": "Fixo", "hint": "O preço da opção de envio é fixo e não muda com base no conteúdo do pedido."}, "calculated": {"label": "Calculado", "hint": "O preço da opção de envio é calculado pelo provedor de fulfillment durante o checkout."}}}, "enableInStore": {"label": "Habilitar na loja", "hint": "Se os clientes podem usar essa opção durante o checkout."}, "provider": "Provedor de fulfillment", "profile": "<PERSON><PERSON><PERSON> de envio"}}, "serviceZones": {"create": {"headerPickup": "Criar Zona de Serviço para Retirada de {{location}}", "headerShipping": "Criar Zona de Serviço para Envio de {{location}}", "action": "Criar zona de serviço", "successToast": "Zona de serviço {{name}} foi criada com sucesso."}, "edit": {"header": "Editar Zona de Serviço", "successToast": "Zona de serviço {{name}} foi atualizada com sucesso."}, "delete": {"confirmation": "Você está prestes a excluir a zona de serviço {{name}}. Esta ação não pode ser desfeita.", "successToast": "Zona de serviço {{name}} foi excluída com sucesso."}, "manageAreas": {"header": "Gerenciar <PERSON><PERSON><PERSON> para {{name}}", "action": "Gerenciar <PERSON>", "label": "<PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON><PERSON> as áreas geográficas que a zona de serviço cobre.", "successToast": "<PERSON><PERSON><PERSON> para {{name}} foram atualizadas com sucesso."}, "fields": {"noRecords": "Não há zonas de serviço para adicionar opções de envio.", "tip": "Uma zona de serviço é um conjunto de zonas ou áreas geográficas. Ela é usada para restringir as opções de envio disponíveis a um conjunto definido de locais."}}}, "shippingProfile": {"domain": "<PERSON><PERSON><PERSON>", "subtitle": "Agrupe produtos com requisitos de envio semelhantes em perfis.", "create": {"header": "<PERSON><PERSON><PERSON>", "hint": "Crie um novo perfil de envio para agrupar produtos com requisitos de envio semelhantes.", "successToast": "O perfil de envio {{name}} foi criado com sucesso."}, "delete": {"title": "Excluir Per<PERSON>l <PERSON> Envio", "description": "Você está prestes a excluir o perfil de envio {{name}}. Esta ação não pode ser desfeita.", "successToast": "O perfil de envio {{name}} foi excluído com sucesso."}, "tooltip": {"type": "Insira o tipo de perfil de envio, por exemplo: Pesado, Sobredimensionado, Apenas Frete, etc."}}, "taxRegions": {"domain": "Regiões <PERSON>", "list": {"hint": "Gerenc<PERSON> o que você cobra de seus clientes quando eles compram de diferentes países e regiões."}, "delete": {"confirmation": "Você está prestes a excluir uma região fiscal. Esta ação não pode ser desfeita.", "successToast": "A região fiscal foi excluída com sucesso."}, "create": {"header": "C<PERSON><PERSON>", "hint": "Crie uma nova região fiscal para definir as taxas de imposto para um país específico.", "errors": {"rateIsRequired": "A taxa de imposto é obrigatória ao criar uma taxa de imposto padrão.", "nameIsRequired": "O nome é obrigatório ao criar uma taxa de imposto padrão."}, "successToast": "A região fiscal foi criada com sucesso."}, "province": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Criar <PERSON> Fi<PERSON>l da Província", "hint": "Crie uma nova região fiscal para definir as taxas de imposto para uma província específica."}}, "state": {"header": "Estados", "create": {"header": "Criar Região Fiscal do Estado", "hint": "Crie uma nova região fiscal para definir as taxas de imposto para um estado específico."}}, "stateOrTerritory": {"header": "Estados ou Territórios", "create": {"header": "Criar Região Fiscal de Estado/Território", "hint": "Crie uma nova região fiscal para definir as taxas de imposto para um estado/território específico."}}, "county": {"header": "Condados", "create": {"header": "Criar Região Fiscal do Condado", "hint": "Crie uma nova região fiscal para definir as taxas de imposto para um condado específico."}}, "region": {"header": "Regiões", "create": {"header": "Criar <PERSON> Fiscal da Região", "hint": "Crie uma nova região fiscal para definir as taxas de imposto para uma região específica."}}, "department": {"header": "Departamentos", "create": {"header": "Criar Região Fiscal do Departamento", "hint": "Crie uma nova região fiscal para definir as taxas de imposto para um departamento específico."}}, "territory": {"header": "Territórios", "create": {"header": "Criar Região Fiscal do Território", "hint": "Crie uma nova região fiscal para definir as taxas de imposto para um território específico."}}, "prefecture": {"header": "Prefeituras", "create": {"header": "Criar <PERSON> Fiscal da Prefeitura", "hint": "Crie uma nova região fiscal para definir as taxas de imposto para uma prefeitura específica."}}, "district": {"header": "Distritos", "create": {"header": "Criar Região Fiscal do Distrito", "hint": "Crie uma nova região fiscal para definir as taxas de imposto para um distrito específico."}}, "governorate": {"header": "Governadorias", "create": {"header": "C<PERSON><PERSON> Fi<PERSON>l da Governadoria", "hint": "Crie uma nova região fiscal para definir as taxas de imposto para uma governadoria específica."}}, "canton": {"header": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Criar Região Fiscal do Cantão", "hint": "Crie uma nova região fiscal para definir as taxas de imposto para um cantão específico."}}, "emirate": {"header": "Emirados", "create": {"header": "Criar Região Fiscal do Emirado", "hint": "Crie uma nova região fiscal para definir as taxas de imposto para um emirado específico."}}, "sublevel": {"header": "Subníveis", "create": {"header": "Criar Região Fiscal de Subnível", "hint": "Crie uma nova região fiscal para definir as taxas de imposto para um subnível específico."}}, "taxOverrides": {"header": "Substituições", "create": {"header": "Criar Substituição", "hint": "Crie uma taxa de imposto que substitui as taxas de imposto padrão para condições selecionadas."}, "edit": {"header": "Editar Substituição", "hint": "Edite a taxa de imposto que substitui as taxas de imposto padrão para condições selecionadas."}}, "taxRates": {"create": {"header": "Criar Taxa de Imposto", "hint": "Crie uma nova taxa de imposto para definir a taxa de imposto de uma região.", "successToast": "A taxa de imposto foi criada com sucesso."}, "edit": {"header": "Editar Taxa de Imposto", "hint": "Edite a taxa de imposto para definir a taxa de imposto de uma região.", "successToast": "A taxa de imposto foi atualizada com sucesso."}, "delete": {"confirmation": "Você está prestes a excluir a taxa de imposto {{name}}. Esta ação não pode ser desfeita.", "successToast": "A taxa de imposto foi excluída com sucesso."}}, "fields": {"isCombinable": {"label": "Combinável", "hint": "Se esta taxa de imposto pode ser combinada com a taxa padrão da região fiscal.", "true": "Combinável", "false": "<PERSON>ão combinável"}, "defaultTaxRate": {"label": "Taxa de imposto padrão", "tooltip": "A taxa de imposto padrão para esta região. Um exemplo é a taxa padrão de IVA para um país ou região.", "action": "Criar taxa de imposto padrão"}, "taxRate": "Taxa de imposto", "taxCode": "Código de imposto", "targets": {"label": "Alvos", "hint": "Selecione os alvos aos quais esta taxa de imposto se aplicará.", "options": {"product": "<PERSON><PERSON><PERSON>", "productCollection": "Coleções de produtos", "productTag": "Tags de produtos", "productType": "Tipos de produtos", "customerGroup": "Grupos de clientes"}, "operators": {"in": "em", "on": "em", "and": "e"}, "placeholders": {"product": "Pes<PERSON><PERSON> produtos", "productCollection": "Pesquisar coleções de produtos", "productTag": "Pesquisar tags de produtos", "productType": "Pesquisar tipos de produtos", "customerGroup": "Pesquisar grupos de clientes"}, "tags": {"product": "Produ<PERSON>", "productCollection": "Coleção de produtos", "productTag": "Tag de produto", "productType": "Tipo de produto", "customerGroup": "Grupo de clientes"}, "modal": {"header": "<PERSON><PERSON><PERSON><PERSON>"}, "values_one": "{{count}} valor", "values_other": "{{count}} valores", "numberOfTargets_one": "{{count}} alvo", "numberOfTargets_other": "{{count}} alvos", "additionalValues_one": "e mais {{count}} valor", "additionalValues_other": "e mais {{count}} valores", "action": "Adicionar alvo"}, "sublevels": {"labels": {"province": "<PERSON>v<PERSON><PERSON>", "state": "Estado", "region": "Região", "stateOrTerritory": "Estado/Território", "department": "Departamento", "county": "Condado", "territory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prefecture": "Prefeitura", "district": "Distrito", "governorate": "Governadoria", "emirate": "Emirado", "canton": "Cantão", "sublevel": "Código do subnível"}, "placeholders": {"province": "Selecione a província", "state": "Selecione o estado", "region": "Selecione a região", "stateOrTerritory": "Selecione o estado/território", "department": "Selecione o departamento", "county": "Selecione o condado", "territory": "Selecione o território", "prefecture": "Selecione a prefeitura", "district": "Selecione o distrito", "governorate": "Selecione a governadoria", "emirate": "Selecione o emirado", "canton": "Selecione o cantão"}, "tooltips": {"sublevel": "Insira o código ISO 3166-2 para a região tributária do subnível.", "notPartOfCountry": "{{province}} não parece fazer parte de {{country}}. Por favor, verifique se isso está correto."}, "alert": {"header": "Regiões de subnível estão desativadas para esta região tributária", "description": "Regiões de subnível estão desativadas por padrão para esta região. Você pode ativá-las para criar regiões de subnível, como províncias, estados ou territórios.", "action": "Ativar regiões de subnível"}}, "noDefaultRate": {"label": "Sem taxa padrão", "tooltip": "Esta região tributária não possui uma taxa padrão. Se houver uma taxa padrão, como o IVA do país, adicione-a a esta região."}}}, "promotions": {"domain": "Promoções", "sections": {"details": "Detalhes da Promoção"}, "tabs": {"template": "Tipo", "details": "<PERSON><PERSON><PERSON>", "campaign": "<PERSON>an<PERSON>"}, "fields": {"type": "Tipo", "value_type": "Tipo de Valor", "value": "Valor", "campaign": "<PERSON>an<PERSON>", "method": "<PERSON><PERSON><PERSON><PERSON>", "allocation": "Alocação", "addCondition": "Adicionar condi<PERSON>", "clearAll": "<PERSON><PERSON> tudo", "amount": {"tooltip": "Selecione o código da moeda para habilitar a definição do valor"}, "conditions": {"rules": {"title": "Quem pode usar este código?", "description": "Qual cliente está autorizado a usar o código de promoção? O código pode ser usado por todos os clientes se não for alterado."}, "target-rules": {"title": "A quais itens a promoção será aplicada?", "description": "A promoção será aplicada aos itens que corresponderem às condições abaixo."}, "buy-rules": {"title": "O que precisa estar no carrinho para ativar a promoção?", "description": "Se estas condições forem atendidas, a promoção será habilitada para os itens-alvo."}}}, "tooltips": {"campaignType": "O código da moeda deve ser selecionado na promoção para definir um orçamento de gasto."}, "errors": {"requiredField": "Campo obrigatório", "promotionTabError": "Corrija os erros na aba Promoção antes de continuar"}, "toasts": {"promotionCreateSuccess": "Promoção ({{code}}) criada com sucesso."}, "create": {}, "edit": {"title": "<PERSON><PERSON> da Promoção", "rules": {"title": "Editar condições de uso"}, "target-rules": {"title": "Editar condições de itens"}, "buy-rules": {"title": "Editar regras de compra"}}, "campaign": {"header": "<PERSON>an<PERSON>", "edit": {"header": "<PERSON><PERSON>", "successToast": "Campanha da promoção atualizada com sucesso."}, "actions": {"goToCampaign": "Ir para a campanha"}}, "campaign_currency": {"tooltip": "Esta é a moeda da promoção. Altere-a na aba Detalhes."}, "form": {"required": "Obrigatório", "and": "E", "selectAttribute": "Selecione o Atributo", "campaign": {"existing": {"title": "<PERSON><PERSON><PERSON>", "description": "Adicione a promoção a uma campanha existente.", "placeholder": {"title": "<PERSON><PERSON><PERSON>a campanha existente", "desc": "Você pode criar uma para acompanhar várias promoções e definir limites de orçamento."}}, "new": {"title": "Nova Campanha", "description": "Crie uma nova campanha para esta promoção."}, "none": {"title": "<PERSON><PERSON>", "description": "Prossiga sem associar a promoção a uma campanha"}}, "status": {"title": "Status"}, "method": {"label": "<PERSON><PERSON><PERSON><PERSON>", "code": {"title": "Código de Promoção", "description": "Os clientes devem inserir este código no checkout"}, "automatic": {"title": "Automático", "description": "Os clientes verão esta promoção no checkout"}}, "max_quantity": {"title": "Quantidade Máxima", "description": "Quantidade máxima de itens aos quais esta promoção se aplica."}, "type": {"standard": {"title": "Padrão", "description": "Uma promoção padrão"}, "buyget": {"title": "Compre e Ganhe", "description": "Promoção do tipo Compre X e Ganhe Y"}}, "allocation": {"each": {"title": "Cada", "description": "Aplica o valor a cada item"}, "across": {"title": "Dividido", "description": "Aplica o valor entre os itens"}}, "code": {"title": "Código", "description": "O código que seus clientes deverão inserir durante o checkout."}, "value": {"title": "Valor da Promoção"}, "value_type": {"fixed": {"title": "Valor da Promoção", "description": "O valor a ser descontado, ex.: 100"}, "percentage": {"title": "Valor da Promoção", "description": "A porcentagem de desconto, ex.: 8%"}}}, "deleteWarning": "Você está prestes a excluir a promoção {{code}}. Esta ação não pode ser desfeita.", "createPromotionTitle": "Criar Promoção", "type": "Tipo de Promoção", "conditions": {"add": "Adicionar condi<PERSON>", "list": {"noRecordsMessage": "Adicione uma condição para restringir os itens aos quais a promoção se aplica."}}}, "campaigns": {"domain": "<PERSON><PERSON><PERSON>", "details": "<PERSON><PERSON><PERSON> da campanha", "status": {"active": "Ativa", "expired": "Expirada", "scheduled": "Agendada"}, "delete": {"title": "Você tem certeza?", "description": "Você está prestes a excluir a campanha '{{name}}'. Esta ação não pode ser desfeita.", "successToast": "Campanha '{{name}}' foi criada com sucesso."}, "edit": {"header": "<PERSON><PERSON>", "description": "<PERSON>e os de<PERSON>hes da campanha.", "successToast": "Campanha '{{name}}' foi atualizada com sucesso."}, "configuration": {"header": "Configuração", "edit": {"header": "Editar Configuração da Campanha", "description": "Edite a configuração da campanha.", "successToast": "Configuração da campanha foi atualizada com sucesso."}}, "create": {"title": "<PERSON><PERSON><PERSON>", "description": "Crie uma campanha promocional.", "hint": "Crie uma campanha promocional.", "header": "<PERSON><PERSON><PERSON>", "successToast": "Campanha '{{name}}' foi criada com sucesso."}, "fields": {"name": "Nome", "identifier": "Identificador", "start_date": "Data de início", "end_date": "Data de término", "total_spend": "Orça<PERSON>", "total_used": "Orçamento utilizado", "budget_limit": "Limite de orçamento", "campaign_id": {"hint": "Somente campanhas com o mesmo código de moeda da promoção aparecem nesta lista."}}, "budget": {"create": {"hint": "Crie um orçamento para a campanha.", "header": "Orçamento da Campanha"}, "details": "Orçamento da campanha", "fields": {"type": "Tipo", "currency": "<PERSON><PERSON>", "limit": "Limite", "used": "<PERSON><PERSON><PERSON><PERSON>"}, "type": {"spend": {"title": "Gasto", "description": "Defina um limite para o valor total descontado de todas as utilizações da promoção."}, "usage": {"title": "<PERSON><PERSON>", "description": "Defina um limite de quantas vezes a promoção pode ser usada."}}, "edit": {"header": "Editar <PERSON> da Campanha"}}, "promotions": {"remove": {"title": "Remover promoção da campanha", "description": "Você está prestes a remover {{count}} promoção(ões) da campanha. Esta ação não pode ser desfeita."}, "alreadyAdded": "Esta promoção já foi adicionada à campanha.", "alreadyAddedDiffCampaign": "Esta promoção já foi adicionada a uma campanha diferente ({{name}}).", "currencyMismatch": "A moeda da promoção e da campanha não correspondem", "toast": {"success": "Sucesso ao adicionar {{count}} promoção(ões) à campanha"}, "add": {"list": {"noRecordsMessage": "Crie uma promoção primeiro."}}, "list": {"noRecordsMessage": "Não há promoções na campanha."}}, "deleteCampaignWarning": "Você está prestes a excluir a campanha {{name}}. Esta ação não pode ser desfeita.", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "Listas de Preços", "subtitle": "Crie vendas ou substitua preços para condições específicas.", "delete": {"confirmation": "Você está prestes a excluir a lista de preços {{title}}. Esta ação não pode ser desfeita.", "successToast": "Lista de preços {{title}} foi excluída com sucesso."}, "create": {"header": "Criar Lista de Preços", "subheader": "Crie uma nova lista de preços para gerenciar os preços de seus produtos.", "tabs": {"details": "<PERSON><PERSON><PERSON>", "products": "<PERSON><PERSON><PERSON>", "prices": "Preços"}, "successToast": "Lista de preços {{title}} foi criada com sucesso.", "products": {"list": {"noRecordsMessage": "Crie um produto primeiro."}}}, "edit": {"header": "Editar Lista de Preços", "successToast": "Lista de preços {{title}} foi atualizada com sucesso."}, "configuration": {"header": "Configuração", "edit": {"header": "Editar Configuração da Lista de Preços", "description": "Edite a configuração da lista de preços.", "successToast": "Configuração da lista de preços foi atualizada com sucesso."}}, "products": {"header": "<PERSON><PERSON><PERSON>", "actions": {"addProducts": "<PERSON><PERSON><PERSON><PERSON> produtos", "editPrices": "<PERSON><PERSON>"}, "delete": {"confirmation_one": "Você está prestes a excluir os preços de {{count}} produto na lista de preços. Esta ação não pode ser desfeita.", "confirmation_other": "Você está prestes a excluir os preços de {{count}} produtos na lista de preços. Esta ação não pode ser desfeita.", "successToast_one": "Preços de {{count}} produto excluídos com sucesso.", "successToast_other": "Preços de {{count}} produtos excluídos com sucesso."}, "add": {"successToast": "Preços foram adicionados com sucesso à lista de preços."}, "edit": {"successToast": "Preços foram atualizados com sucesso."}}, "fields": {"priceOverrides": {"label": "Substituições de preços", "header": "Substituições de Preços"}, "status": {"label": "Status", "options": {"active": "Ativa", "draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expired": "Expirada", "scheduled": "Agendada"}}, "type": {"label": "Tipo", "hint": "Escolha o tipo de lista de preços que deseja criar.", "options": {"sale": {"label": "<PERSON><PERSON><PERSON>", "description": "Preços de venda são alterações temporárias nos preços dos produtos."}, "override": {"label": "Substituição", "description": "Substituições geralmente são usadas para criar preços específicos para clientes."}}}, "startsAt": {"label": "A lista de preços tem uma data de início?", "hint": "Agende a lista de preços para ativar no futuro."}, "endsAt": {"label": "A lista de preços tem uma data de expiração?", "hint": "Agende a lista de preços para desativar no futuro."}, "customerAvailability": {"header": "Escolha grupos de clientes", "label": "Disponibilidade para clientes", "hint": "Escolha quais grupos de clientes devem usar a lista de preços.", "placeholder": "Pesquise por grupos de clientes", "attribute": "Grupos de clientes"}}}, "profile": {"domain": "Perfil", "manageYourProfileDetails": "Gerencie os detalhes do seu perfil.", "fields": {"languageLabel": "Idioma", "usageInsightsLabel": "Informações de uso"}, "edit": {"header": "<PERSON><PERSON>", "languageHint": "O idioma que você deseja usar no painel de administração. Isso não altera o idioma da sua loja.", "languagePlaceholder": "Selecione o idioma", "usageInsightsHint": "Compartilhe informações de uso e nos ajude a melhorar o Medusa. Você pode ler mais sobre o que coletamos e como usamos na nossa <0>documentação</0>."}, "toast": {"edit": "Alterações no perfil salvas"}}, "users": {"domain": "Usuários", "editUser": "<PERSON><PERSON>", "inviteUser": "<PERSON><PERSON><PERSON>", "inviteUserHint": "Convide um novo usuário para sua loja.", "sendInvite": "Enviar convite", "pendingInvites": "<PERSON><PERSON><PERSON>", "deleteInviteWarning": "Você está prestes a excluir o convite para {{email}}. Esta ação não pode ser desfeita.", "resendInvite": "Reenviar convite", "copyInviteLink": "Copiar link do convite", "expiredOnDate": "<PERSON><PERSON><PERSON> em {{date}}", "validFromUntil": "<PERSON><PERSON><PERSON><PERSON> de <0>{{from}}</0> - <1>{{until}}</1>", "acceptedOnDate": "<PERSON><PERSON> em {{date}}", "inviteStatus": {"accepted": "<PERSON><PERSON>", "pending": "Pendente", "expired": "<PERSON><PERSON><PERSON>"}, "roles": {"admin": "Administrador", "developer": "<PERSON><PERSON><PERSON><PERSON>", "member": "Membro"}, "deleteUserWarning": "Você está prestes a excluir o usuário {{name}}. Esta ação não pode ser desfeita.", "invite": "<PERSON><PERSON><PERSON>"}, "store": {"domain": "<PERSON><PERSON>", "manageYourStoresDetails": "<PERSON><PERSON><PERSON><PERSON> os de<PERSON>hes da sua loja", "editStore": "<PERSON><PERSON> loja", "defaultCurrency": "Moe<PERSON> pad<PERSON>", "defaultRegion": "<PERSON><PERSON><PERSON>", "swapLinkTemplate": "Modelo de link de troca", "paymentLinkTemplate": "Modelo de link de pagamento", "inviteLinkTemplate": "Modelo de link de convite", "currencies": "<PERSON><PERSON>", "addCurrencies": "<PERSON><PERSON><PERSON><PERSON> moe<PERSON>", "enableTaxInclusivePricing": "Ativar preços com impostos inclusos", "disableTaxInclusivePricing": "Desativar preços com impostos inclusos", "removeCurrencyWarning_one": "Você está prestes a remover {{count}} moeda da sua loja. Certifique-se de remover todos os preços que usam essa moeda antes de continuar.", "removeCurrencyWarning_other": "Você está prestes a remover {{count}} moedas da sua loja. Certifique-se de remover todos os preços que usam essas moedas antes de continuar.", "currencyAlreadyAdded": "A moeda já foi adicionada à sua loja.", "edit": {"header": "<PERSON><PERSON>"}, "toast": {"update": "Loja atualizada com sucesso", "currenciesUpdated": "Moedas atualizadas com sucesso", "currenciesRemoved": "<PERSON><PERSON> removidas da loja com sucesso", "updatedTaxInclusivitySuccessfully": "Preços com impostos inclusos atualizados com sucesso"}}, "regions": {"domain": "Regiões", "subtitle": "Uma região é uma área onde você vende produtos. Ela pode abranger vários países e ter diferentes taxas, provedores e moedas.", "createRegion": "<PERSON><PERSON><PERSON>", "createRegionHint": "Gerencie taxas e provedores para um conjunto de países.", "addCountries": "Adicionar pa<PERSON>", "editRegion": "<PERSON><PERSON>", "countriesHint": "Adicione os países incluídos nesta região.", "deleteRegionWarning": "Você está prestes a excluir a região {{name}}. Esta ação não pode ser desfeita.", "removeCountriesWarning_one": "Você está prestes a remover {{count}} país da região. Esta ação não pode ser desfeita.", "removeCountriesWarning_other": "Você está prestes a remover {{count}} países da região. Esta ação não pode ser desfeita.", "removeCountryWarning": "Você está prestes a remover o país {{name}} da região. Esta ação não pode ser desfeita.", "automaticTaxesHint": "<PERSON>uando ativado, os impostos serão calculados apenas no checkout com base no endereço de entrega.", "taxInclusiveHint": "<PERSON>uando ativado, os preços na região incluirão impostos.", "providersHint": "Adicione os provedores de pagamento disponíveis nesta região.", "shippingOptions": "Opções de Envio", "deleteShippingOptionWarning": "Você está prestes a excluir a opção de envio {{name}}. Esta ação não pode ser desfeita.", "return": "Devolução", "outbound": "<PERSON><PERSON>", "priceType": "Tipo de Preço", "flatRate": "Taxa Fixa", "calculated": "Calculado", "list": {"noRecordsMessage": "Crie uma região para as <PERSON><PERSON><PERSON> onde você vende."}, "toast": {"delete": "Região excluída com sucesso", "edit": "Edição da região salva", "create": "Região criada com sucesso", "countries": "Países da região atualizados com sucesso"}, "shippingOption": {"createShippingOption": "Criar Opção de Envio", "createShippingOptionHint": "Crie uma nova opção de envio para a região.", "editShippingOption": "Editar Opção de Envio", "fulfillmentMethod": "Método <PERSON> Entrega", "type": {"outbound": "<PERSON><PERSON>", "outboundHint": "Use esta opção se estiver criando uma opção de envio para enviar produtos ao cliente.", "return": "Devolução", "returnHint": "Use esta opção se estiver criando uma opção de envio para o cliente devolver produtos a você."}, "priceType": {"label": "Tipo de Preço", "flatRate": "Taxa fixa", "calculated": "Calculado"}, "availability": {"adminOnly": "Apenas para Admin", "adminOnlyHint": "Quando ativado, a opção de envio estará disponível apenas no painel de administração e não na vitrine."}, "taxInclusiveHint": "<PERSON>uando ativado, o preço da opção de envio incluirá impostos.", "requirements": {"label": "Requisitos", "hint": "Especifique os requisitos para a opção de envio."}}}, "taxes": {"domain": "Regiões <PERSON>", "domainDescription": "Gerencie sua região fiscal", "countries": {"taxCountriesHint": "As configurações fiscais se aplicam aos países listados."}, "settings": {"editTaxSettings": "Editar Configu<PERSON>", "taxProviderLabel": "<PERSON><PERSON><PERSON>", "systemTaxProviderLabel": "<PERSON><PERSON><PERSON> Fi<PERSON>l do Sistema", "calculateTaxesAutomaticallyLabel": "Calcular impostos automaticamente", "calculateTaxesAutomaticallyHint": "<PERSON>uando ativado, as taxas serão calculadas automaticamente e aplicadas aos carrinhos. <PERSON>uando desativado, os impostos devem ser calculados manualmente no checkout. Impostos manuais são recomendados para uso com provedores fiscais de terceiros.", "applyTaxesOnGiftCardsLabel": "Aplicar impostos em cartões-presente", "applyTaxesOnGiftCardsHint": "<PERSON>uando ativado, os impostos serão aplicados aos cartões-presente no checkout. Em alguns países, as regulamentações fiscais exigem a aplicação de impostos aos cartões-presente na compra.", "defaultTaxRateLabel": "Taxa padrão de imposto", "defaultTaxCodeLabel": "<PERSON>ódigo pad<PERSON> de imposto"}, "defaultRate": {"sectionTitle": "Taxa Padrão de Imposto"}, "taxRate": {"sectionTitle": "Taxas de Imposto", "createTaxRate": "Criar Taxa de Imposto", "createTaxRateHint": "Crie uma nova taxa de imposto para a região.", "deleteRateDescription": "Você está prestes a excluir a taxa de imposto {{name}}. Esta ação não pode ser desfeita.", "editTaxRate": "Editar Taxa de Imposto", "editRateAction": "Editar taxa", "editOverridesAction": "Editar exceções", "editOverridesTitle": "Editar Exceções de Taxa de Imposto", "editOverridesHint": "Especifique as exceções para a taxa de imposto.", "deleteTaxRateWarning": "Você está prestes a excluir a taxa de imposto {{name}}. Esta ação não pode ser desfeita.", "productOverridesLabel": "Exceções de produtos", "productOverridesHint": "Especifique as exceções de produtos para a taxa de imposto.", "addProductOverridesAction": "Adicionar exceções de produtos", "productTypeOverridesLabel": "Exceções por tipo de produto", "productTypeOverridesHint": "Especifique as exceções por tipo de produto para a taxa de imposto.", "addProductTypeOverridesAction": "Adicionar exceções por tipo de produto", "shippingOptionOverridesLabel": "Exceções de opções de envio", "shippingOptionOverridesHint": "Especifique as exceções de opções de envio para a taxa de imposto.", "addShippingOptionOverridesAction": "Adicionar exceções de opções de envio", "productOverridesHeader": "<PERSON><PERSON><PERSON>", "productTypeOverridesHeader": "Tipos de Produto", "shippingOptionOverridesHeader": "Opções de Envio"}}, "locations": {"domain": "Locais", "editLocation": "Editar local", "addSalesChannels": "Adicionar canais de venda", "noLocationsFound": "Nenhum local encontrado", "selectLocations": "Selecione os locais que possuem o item em estoque.", "deleteLocationWarning": "Você está prestes a excluir o local {{name}}. Esta ação não pode ser desfeita.", "removeSalesChannelsWarning_one": "Você está prestes a remover {{count}} canal de venda do local.", "removeSalesChannelsWarning_other": "Você está prestes a remover {{count}} canais de venda do local.", "toast": {"create": "Local criado com sucesso", "update": "Local atualizado com sucesso", "removeChannel": "Canal de venda removido com sucesso"}}, "reservations": {"domain": "<PERSON><PERSON><PERSON>", "subtitle": "Gerencie a quantidade reservada de itens de inventário.", "deleteWarning": "Você está prestes a excluir uma reserva. Esta ação não pode ser desfeita."}, "salesChannels": {"domain": "Canais de Vendas", "subtitle": "Gerencie os canais online e offline nos quais você vende seus produtos.", "createSalesChannel": "Criar Canal de Vendas", "createSalesChannelHint": "Crie um novo canal de vendas para comercializar seus produtos.", "enabledHint": "Especifique se o canal de vendas está habilitado.", "removeProductsWarning_one": "Você está prestes a remover {{count}} produto do {{sales_channel}}.", "removeProductsWarning_other": "Você está prestes a remover {{count}} produtos do {{sales_channel}}.", "addProducts": "<PERSON><PERSON><PERSON><PERSON>", "editSalesChannel": "Editar canal de vendas", "productAlreadyAdded": "O produto já foi adicionado ao canal de vendas.", "deleteSalesChannelWarning": "Você está prestes a excluir o canal de vendas {{name}}. Esta ação não pode ser desfeita.", "toast": {"create": "Canal de vendas criado com sucesso", "update": "Canal de vendas atualizado com sucesso", "delete": "Canal de vendas excluído com sucesso"}, "products": {"list": {"noRecordsMessage": "Não há produtos no canal de vendas."}, "add": {"list": {"noRecordsMessage": "Crie um produto primeiro."}}}}, "apiKeyManagement": {"domain": {"publishable": "Chaves de API Publicáveis", "secret": "Chaves de API Secretas"}, "subtitle": {"publishable": "<PERSON><PERSON><PERSON><PERSON> as chaves de API usadas na vitrine para limitar o escopo das solicitações a canais de vendas específicos.", "secret": "<PERSON><PERSON><PERSON><PERSON> as chaves de API usadas para autenticar usuários administrativos em aplicativos administrativos."}, "status": {"active": "Ativo", "revoked": "Rev<PERSON><PERSON>"}, "type": {"publishable": "Publicável", "secret": "<PERSON>a"}, "create": {"createPublishableHeader": "Criar Chave de API Publicável", "createPublishableHint": "Crie uma nova chave de API publicável para limitar o escopo das solicitações a canais de vendas específicos.", "createSecretHeader": "<PERSON><PERSON>r Cha<PERSON> de <PERSON>a", "createSecretHint": "Crie uma nova chave de API secreta para acessar a API Medusa como um usuário administrativo autenticado.", "secretKeyCreatedHeader": "<PERSON><PERSON>", "secretKeyCreatedHint": "Sua nova chave secreta foi gerada. Copie e armazene-a com segurança agora. Este é o único momento em que ela será exibida.", "copySecretTokenSuccess": "Chave secreta copiada para a área de transferência.", "copySecretTokenFailure": "Falha ao copiar a chave secreta para a área de transferência.", "successToast": "Chave de API criada com sucesso."}, "edit": {"header": "Editar <PERSON>", "description": "Edite o título da chave de API.", "successToast": "Chave de API {{title}} atualizada com sucesso."}, "salesChannels": {"title": "Adicionar <PERSON> Vendas", "description": "Adicione os canais de vendas que a chave de API deve ter como escopo.", "successToast_one": "{{count}} canal de vendas foi adicionado com sucesso à chave de API.", "successToast_other": "{{count}} canais de vendas foram adicionados com sucesso à chave de API.", "alreadyAddedTooltip": "O canal de vendas já foi adicionado à chave de API.", "list": {"noRecordsMessage": "Não há canais de vendas no escopo da chave de API publicável."}}, "delete": {"warning": "Você está prestes a excluir a chave de API {{title}}. Esta ação não pode ser desfeita.", "successToast": "Chave de API {{title}} excluída com sucesso."}, "revoke": {"warning": "Você está prestes a revogar a chave de API {{title}}. Esta ação não pode ser desfeita.", "successToast": "Chave de API {{title}} revogada com sucesso."}, "addSalesChannels": {"list": {"noRecordsMessage": "Crie um canal de vendas primeiro."}}, "removeSalesChannel": {"warning": "Você está prestes a remover o canal de vendas {{name}} da chave de API. Esta ação não pode ser desfeita.", "warningBatch_one": "Você está prestes a remover {{count}} canal de vendas da chave de API. Esta ação não pode ser desfeita.", "warningBatch_other": "Você está prestes a remover {{count}} canais de vendas da chave de API. Esta ação não pode ser desfeita.", "successToast": "Canal de vendas removido com sucesso da chave de API.", "successToastBatch_one": "{{count}} canal de vendas removido com sucesso da chave de API.", "successToastBatch_other": "{{count}} canais de vendas removidos com sucesso da chave de API."}, "actions": {"revoke": "<PERSON><PERSON><PERSON>", "copy": "Copiar Chave de API", "copySuccessToast": "Chave de API copiada para a área de transferência."}, "table": {"lastUsedAtHeader": "Último <PERSON>", "createdAtHeader": "Criado Em"}, "fields": {"lastUsedAtLabel": "Último uso em", "revokedByLabel": "Revogado por", "revokedAtLabel": "Revogado em", "createdByLabel": "<PERSON><PERSON><PERSON> por"}}, "returnReasons": {"domain": "Razões de Devolução", "subtitle": "<PERSON><PERSON><PERSON><PERSON> as razões para itens devolvidos.", "calloutHint": "<PERSON><PERSON><PERSON><PERSON> as razões para categorizar as devoluções.", "editReason": "Editar Razão de Devolução", "create": {"header": "Adicionar Razão de Devolução", "subtitle": "Especifique as razões mais comuns para devoluções.", "hint": "Crie uma nova razão de devolução para categorizar as devoluções.", "successToast": "Razão de devolução {{label}} criada com sucesso."}, "edit": {"header": "Editar Razão de Devolução", "subtitle": "Edite o valor da razão de devolução.", "successToast": "Razão de devolução {{label}} atualizada com sucesso."}, "delete": {"confirmation": "Você está prestes a excluir a razão de devolução {{label}}. Esta ação não pode ser desfeita.", "successToast": "Razão de devolução {{label}} excluída com sucesso."}, "fields": {"value": {"label": "Valor", "placeholder": "taman<PERSON>_errado", "tooltip": "O valor deve ser um identificador único para a razão da devolução."}, "label": {"label": "Etiqueta", "placeholder": "<PERSON><PERSON><PERSON> er<PERSON>"}, "description": {"label": "Descrição", "placeholder": "O cliente recebeu o tamanho errado"}}}, "login": {"forgotPassword": "Esque<PERSON>u a senha? - <0>Redefinir</0>", "title": "Bem-vindo ao Me<PERSON>", "hint": "Faça login para acessar a área da conta"}, "invite": {"title": "Bem-vindo ao Me<PERSON>", "hint": "Crie sua conta abaixo", "backToLogin": "Voltar para o login", "createAccount": "C<PERSON><PERSON> conta", "alreadyHaveAccount": "Já tem uma conta? - <0>Fazer login</0>", "emailTooltip": "Seu e-mail não pode ser alterado. Se desejar usar outro e-mail, um novo convite deve ser enviado.", "invalidInvite": "O convite é inválido ou expirou.", "successTitle": "Sua conta foi registrada", "successHint": "Comece a usar o Medusa Admin imediatamente.", "successAction": "Iniciar <PERSON><PERSON>", "invalidTokenTitle": "Seu token de convite é inválido", "invalidTokenHint": "Tente solicitar um novo link de convite.", "passwordMismatch": "As senhas não correspondem", "toast": {"accepted": "Convite aceito com sucesso"}}, "resetPassword": {"title": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Digite seu e-mail abaixo e enviaremos instruções sobre como redefinir sua senha.", "email": "E-mail", "sendResetInstructions": "Enviar instruções de redefinição", "backToLogin": "<0>Voltar para o login</0>", "newPasswordHint": "Escolha uma nova senha a<PERSON>o.", "invalidTokenTitle": "Seu token de redefinição é inválido", "invalidTokenHint": "Tente solicitar um novo link de redefinição.", "expiredTokenTitle": "Seu token de redefinição expirou", "goToResetPassword": "<PERSON>r <PERSON> <PERSON><PERSON><PERSON><PERSON>", "resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "newPassword": "Nova senha", "repeatNewPassword": "<PERSON>etir nova senha", "tokenExpiresIn": "O token expira em <0>{{time}}</0> minutos", "successfulRequestTitle": "E-mail enviado com sucesso", "successfulRequest": "Enviamos um e-mail que você pode usar para redefinir sua senha. Verifique a pasta de spam se não o recebeu após alguns minutos.", "successfulResetTitle": "Redefinição de senha bem-sucedida", "successfulReset": "Faça login na página de login.", "passwordMismatch": "As senhas não correspondem", "invalidLinkTitle": "Seu link de redefinição é inválido", "invalidLinkHint": "Tente redefinir sua senha novamente."}, "workflowExecutions": {"domain": "Fluxos de Trabalho", "subtitle": "Visualize e acompanhe as execuções de fluxos de trabalho em sua aplicação Medusa.", "transactionIdLabel": "ID da Transação", "workflowIdLabel": "ID do Fluxo de Trabalho", "progressLabel": "Progresso", "stepsCompletedLabel_one": "{{completed}} de {{count}} etapa", "stepsCompletedLabel_other": "{{completed}} de {{count}} etapas", "list": {"noRecordsMessage": "Nenhum fluxo de trabalho foi executado ainda."}, "history": {"sectionTitle": "Hist<PERSON><PERSON><PERSON>", "runningState": "Em execução...", "awaitingState": "Aguardando", "failedState": "Fal<PERSON>", "skippedState": "<PERSON><PERSON><PERSON>", "skippedFailureState": "<PERSON><PERSON><PERSON> (Falha)", "definitionLabel": "Definição", "outputLabel": "<PERSON><PERSON><PERSON>", "compensateInputLabel": "Compensar entrada", "revertedLabel": "Revertido", "errorLabel": "Erro"}, "state": {"done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "failed": "Fal<PERSON>", "reverted": "Revertido", "invoking": "Invocando", "compensating": "Compensando", "notStarted": "Não iniciado"}, "transaction": {"state": {"waitingToCompensate": "Aguardando compensa<PERSON>"}}, "step": {"state": {"skipped": "<PERSON><PERSON><PERSON>", "skippedFailure": "<PERSON><PERSON><PERSON> (Falha)", "dormant": "Inativo", "timeout": "Tempo limite"}}}, "productTypes": {"domain": "Tipos de Produtos", "subtitle": "Organize seus produtos em tipos.", "create": {"header": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>du<PERSON>", "hint": "Crie um novo tipo de produto para categorizar seus produtos.", "successToast": "Tipo de produto {{value}} foi criado com sucesso."}, "edit": {"header": "Editar T<PERSON> de Produto", "successToast": "Tipo de produto {{value}} foi atualizado com sucesso."}, "delete": {"confirmation": "Você está prestes a excluir o tipo de produto {{value}}. Esta ação não pode ser desfeita.", "successToast": "Tipo de produto {{value}} foi excluído com sucesso."}, "fields": {"value": "Valor"}}, "productTags": {"domain": "Etiquetas de Produtos", "create": {"header": "C<PERSON>r Etiqueta de Produto", "subtitle": "Crie uma nova etiqueta de produto para categorizar seus produtos.", "successToast": "Etiqueta de produto {{value}} foi criada com sucesso."}, "edit": {"header": "Editar Etiqueta de Produto", "subtitle": "Edite o valor da etiqueta do produto.", "successToast": "Etiqueta de produto {{value}} foi atualizada com sucesso."}, "delete": {"confirmation": "Você está prestes a excluir a etiqueta de produto {{value}}. Esta ação não pode ser desfeita.", "successToast": "Etiqueta de produto {{value}} foi excluída com sucesso."}, "fields": {"value": "Valor"}}, "notifications": {"domain": "Notificações", "emptyState": {"title": "Sem notificações", "description": "Você não tem nenhuma notificação no momento, mas quando tiver, elas aparecerão aqui."}, "accessibility": {"description": "Notificações sobre atividades do Medusa serão listadas aqui."}}, "errors": {"serverError": "Erro no servidor - Tente novamente mais tarde.", "invalidCredentials": "<PERSON>ail ou senha incorretos"}, "statuses": {"scheduled": "Agendado", "expired": "<PERSON><PERSON><PERSON>", "active": "Ativo", "enabled": "Habilitado", "disabled": "Desabilitado"}, "labels": {"productVariant": "<PERSON><PERSON><PERSON> de Produto", "prices": "Preços", "available": "Disponível", "inStock": "Em estoque", "added": "<PERSON><PERSON><PERSON><PERSON>", "removed": "Removido"}, "fields": {"amount": "Quantidade", "refundAmount": "Valor do reembolso", "name": "Nome", "default": "Padrão", "lastName": "Sobrenome", "firstName": "Primeiro nome", "title": "<PERSON><PERSON><PERSON><PERSON>", "customTitle": "<PERSON><PERSON><PERSON><PERSON>", "manageInventory": "Gerenciar inventário", "inventoryKit": "Tem kit de inventário", "inventoryItems": "Itens de inventário", "inventoryItem": "Item de inventário", "requiredQuantity": "Quantidade necessária", "description": "Descrição", "email": "Email", "password": "<PERSON><PERSON>", "repeatPassword": "<PERSON><PERSON><PERSON>", "confirmPassword": "Confirmar <PERSON><PERSON><PERSON>", "newPassword": "Nova senha", "repeatNewPassword": "<PERSON>etir nova senha", "categories": "Categorias", "shippingMethod": "M<PERSON><PERSON><PERSON>", "configurations": "Configurações", "conditions": "Condições", "category": "Categoria", "collection": "Coleção", "discountable": "<PERSON><PERSON>t<PERSON>vel", "handle": "Identificador", "subtitle": "Subtítulo", "item": "<PERSON><PERSON>", "qty": "Qtd.", "limit": "Limite", "tags": "Tags", "type": "Tipo", "reason": "Razão", "none": "<PERSON><PERSON><PERSON>", "all": "Todos", "search": "Buscar", "percentage": "Porcentagem", "sales_channels": "Canais de vendas", "customer_groups": "Grupos de clientes", "product_tags": "Tags de produtos", "product_types": "Tipos de produtos", "product_collections": "Coleções de produtos", "status": "Status", "code": "Código", "value": "Valor", "disabled": "Desabilitado", "dynamic": "Dinâmico", "normal": "Normal", "years": "<PERSON><PERSON>", "months": "Meses", "days": "<PERSON><PERSON>", "hours": "<PERSON><PERSON>", "minutes": "<PERSON><PERSON><PERSON>", "totalRedemptions": "Total de resgates", "countries": "Países", "paymentProviders": "Provedores de pagamento", "refundReason": "Razão do reembolso", "fulfillmentProviders": "Provedores de atendimento", "fulfillmentProvider": "<PERSON><PERSON>or de atendimento", "providers": "Provedores", "availability": "Disponibilidade", "inventory": "Inventário", "optional": "Opcional", "note": "<PERSON>a", "automaticTaxes": "Impostos automáticos", "taxInclusivePricing": "Preço com impostos incluídos", "currency": "<PERSON><PERSON>", "address": "Endereço", "address2": "Apartamento, suíte, etc.", "city": "Cidade", "postalCode": "CEP", "country": "<PERSON><PERSON>", "state": "Estado", "province": "<PERSON>v<PERSON><PERSON>", "company": "Empresa", "phone": "Telefone", "metadata": "Metadados", "selectCountry": "Selecione o país", "products": "<PERSON><PERSON><PERSON>", "variants": "<PERSON><PERSON><PERSON>", "orders": "Pedidos", "account": "Conta", "total": "Total do pedido", "paidTotal": "Total capturado", "totalExclTax": "Total sem impostos", "subtotal": "Subtotal", "shipping": "Frete", "outboundShipping": "Frete de saída", "returnShipping": "Frete de retorno", "tax": "Imposto", "created": "<PERSON><PERSON><PERSON>", "key": "Chave", "customer": "Cliente", "date": "Data", "order": "Pedido", "fulfillment": "Atendimento", "provider": "<PERSON><PERSON><PERSON>", "payment": "Pagamento", "items": "<PERSON><PERSON>", "salesChannel": "Canal de vendas", "region": "Região", "discount": "Desconto", "role": "Função", "sent": "Enviado", "salesChannels": "Canais de vendas", "product": "Produ<PERSON>", "createdAt": "C<PERSON><PERSON> em", "updatedAt": "Atualizado em", "revokedAt": "Revogado em", "true": "<PERSON><PERSON><PERSON><PERSON>", "false": "<PERSON><PERSON><PERSON>", "giftCard": "Cartão presente", "tag": "Tag", "dateIssued": "Data de emissão", "issuedDate": "Data de emissão", "expiryDate": "Data de validade", "price": "Preço", "priceTemplate": "Preço {{regionOrCurrency}}", "height": "Altura", "width": "<PERSON><PERSON><PERSON>", "length": "Comprimento", "weight": "Peso", "midCode": "Código <PERSON>", "hsCode": "Código HS", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "Quantidade de inventário", "barcode": "Código de <PERSON>", "countryOfOrigin": "<PERSON><PERSON> or<PERSON>", "material": "Material", "thumbnail": "Miniatura", "sku": "SKU", "managedInventory": "Invent<PERSON><PERSON> gerido", "allowBackorder": "<PERSON><PERSON><PERSON>", "inStock": "Em estoque", "location": "Localização", "quantity": "Quantidade", "variant": "<PERSON><PERSON><PERSON>", "id": "ID", "parent": "<PERSON><PERSON>", "minSubtotal": "Subtotal mínimo", "maxSubtotal": "Subtotal máximo", "shippingProfile": "<PERSON><PERSON><PERSON> de frete", "summary": "Resumo", "details": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>", "rate": "Taxa", "requiresShipping": "<PERSON><PERSON> envio", "unitPrice": "Preço unitário", "startDate": "Data de início", "endDate": "Data de término", "draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": "Valores"}, "quotes": {"domain": "Cotações", "title": "Cotações", "subtitle": "Gerenciar cotações e propostas de clientes", "noQuotes": "Nenhuma cotação encontrada", "noQuotesDescription": "Atualmente não há cotações. Crie uma na loja.", "table": {"id": "ID da Cotação", "customer": "Cliente", "status": "Status", "company": "Empresa", "amount": "Valor", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Atualizado", "actions": "Ações"}, "status": {"pending_merchant": "A<PERSON><PERSON><PERSON>", "pending_customer": "Aguardando Cliente", "merchant_rejected": "Reje<PERSON><PERSON> pelo Comerciante", "customer_rejected": "Rejeitado pelo Cliente", "accepted": "<PERSON><PERSON>", "unknown": "Desconhecido"}, "actions": {"sendQuote": "Enviar Cotação", "rejectQuote": "Rejeitar <PERSON>", "viewOrder": "Ver Pedido"}, "details": {"header": "<PERSON><PERSON><PERSON> da Cotação", "quoteSummary": "Resumo da Cotação", "customer": "Cliente", "company": "Empresa", "items": "<PERSON><PERSON>", "total": "Total", "subtotal": "Subtotal", "shipping": "Frete", "tax": "Imposto", "discounts": "Descontos", "originalTotal": "Total Original", "quoteTotal": "Total da Cotação", "messages": "Mensagens", "actions": "Ações", "sendMessage": "Enviar Mensagem", "send": "Enviar", "pickQuoteItem": "Escolher item da cotação", "selectQuoteItem": "Selecione um item da cotação para comentar", "selectItem": "Selecionar item", "manage": "Gerenciar", "phone": "Telefone", "spendingLimit": "Limite de gastos", "name": "Nome", "manageQuote": "Gerenciar Cotação", "noItems": "Nenhum item nesta cotação", "noMessages": "Nenhuma mensagem para esta cotação"}, "items": {"title": "Produ<PERSON>", "quantity": "Quantidade", "unitPrice": "Preço Unitário", "total": "Total"}, "messages": {"admin": "Administrador", "customer": "Cliente", "placeholder": "Digite sua mensagem aqui..."}, "filters": {"status": "Filtrar por status"}, "confirmations": {"sendTitle": "Enviar Cotação", "sendDescription": "Tem certeza de que deseja enviar esta cotação para o cliente?", "rejectTitle": "Rejeitar <PERSON>", "rejectDescription": "Tem certeza de que deseja rejeitar esta cotação?"}, "acceptance": {"message": "A cotação foi aceita"}, "toasts": {"sendSuccess": "Cotação enviada com sucesso para o cliente", "sendError": "Falha ao enviar cotação", "rejectSuccess": "Cotação do cliente rejeitada com sucesso", "rejectError": "Falha ao rejeitar cotação", "messageSuccess": "Mensagem enviada com sucesso para o cliente", "messageError": "Falha ao enviar mensagem", "updateSuccess": "Cotação atualizada com sucesso"}, "manage": {"overridePriceHint": "Substituir o preço original deste item", "updatePrice": "Atualizar <PERSON>"}}, "companies": {"domain": "Empresas", "title": "Empresas", "subtitle": "Gerenciar relacionamentos comerciais", "noCompanies": "Nenhuma empresa encontrada", "noCompaniesDescription": "Crie sua primeira empresa para começar.", "notFound": "Empresa não encontrada", "table": {"name": "Nome", "phone": "Telefone", "email": "E-mail", "address": "Endereço", "employees": "Funcionários", "customerGroup": "Grupo de Clientes", "actions": "Ações"}, "fields": {"name": "Nome da empresa", "email": "E-mail", "phone": "Telefone", "website": "Website", "address": "Endereço", "city": "Cidade", "state": "Estado", "zip": "CEP", "zipCode": "CEP", "country": "<PERSON><PERSON>", "currency": "<PERSON><PERSON>", "logoUrl": "URL do logo", "description": "Descrição", "employees": "Funcionários", "customerGroup": "Grupo de clientes", "approvalSettings": "Configurações de aprovação"}, "placeholders": {"name": "Digite o nome da empresa", "email": "Digite o endereço de e-mail", "phone": "Digite o número de telefone", "website": "Digite a URL do website", "address": "Digite o endereço", "city": "Digite a cidade", "state": "Digite o estado", "zip": "Digite o CEP", "logoUrl": "Digite a URL do logo", "description": "Digite a descrição da empresa", "selectCountry": "Selecionar país", "selectCurrency": "Selecionar moeda"}, "validation": {"nameRequired": "Nome da empresa é obrigatório", "emailRequired": "E-mail é obrigatório", "emailInvalid": "Endereço de e-mail inválido", "addressRequired": "Endereço é obrigatório", "cityRequired": "Cidade é obrigatória", "stateRequired": "Estado é obrigatório", "zipRequired": "CEP é obrigatório"}, "create": {"title": "<PERSON><PERSON><PERSON> empresa", "description": "Criar uma nova empresa para gerenciar relacionamentos comerciais.", "submit": "<PERSON><PERSON><PERSON> empresa"}, "edit": {"title": "Editar empresa", "submit": "Atualizar empresa"}, "details": {"actions": "Ações"}, "approvals": {"requiresAdminApproval": "Requer aprovação do administrador", "requiresSalesManagerApproval": "Requer aprovação do gerente de vendas", "noApprovalRequired": "Nenhuma aprovação necessária"}, "deleteWarning": "Isso excluirá permanentemente a empresa e todos os dados associados.", "approvalSettings": {"title": "Configurações de aprovação", "requiresAdminApproval": "Requer aprovação do administrador", "requiresSalesManagerApproval": "Requer aprovação do gerente de vendas", "requiresAdminApprovalDesc": "Pedidos desta empresa requerem aprovação do administrador antes do processamento", "requiresSalesManagerApprovalDesc": "Pedidos desta empresa requerem aprovação do gerente de vendas antes do processamento", "updateSuccess": "Configurações de aprovação atualizadas com sucesso", "updateError": "Falha ao atualizar configurações de aprovação"}, "customerGroup": {"title": "Gerenciar grupo de clientes", "hint": "Atribua esta empresa a um grupo de clientes para aplicar preços e permissões específicas do grupo.", "name": "Nome do grupo de clientes", "groupName": "Grupo de clientes", "actions": "Ações", "add": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Remover", "description": "Gerenciar grupos de clientes para esta empresa", "noGroups": "Nenhum grupo de clientes disponível", "addSuccess": "Empresa adicionada com sucesso ao grupo de clientes", "addError": "Falha ao adicionar empresa ao grupo de clientes", "removeSuccess": "Empresa removida com sucesso do grupo de clientes", "removeError": "Falha ao remover empresa do grupo de clientes"}, "actions": {"edit": "Editar empresa", "editDetails": "<PERSON><PERSON>", "manageCustomerGroup": "Gerenciar grupo de clientes", "approvalSettings": "Configurações de aprovação", "delete": "Excluir empresa", "confirmDelete": "Confirmar exclusão"}, "delete": {"title": "Excluir empresa", "description": "Tem certeza de que deseja excluir esta empresa? Esta ação não pode ser desfeita."}, "employees": {"title": "Funcionários", "noEmployees": "Nenhum funcionário encontrado para esta empresa", "name": "Nome", "email": "E-mail", "phone": "Telefone", "role": "Função", "spendingLimit": "Limite de gastos", "admin": "Administrador", "employee": "Funcionário", "add": "Adicionar <PERSON>", "create": {"title": "<PERSON><PERSON><PERSON>", "success": "Funcionário criado com sucesso", "error": "Falha ao criar funcionário"}, "form": {"details": "Informações detalhadas", "permissions": "Permissões", "firstName": "Nome", "lastName": "Sobrenome", "email": "E-mail", "phone": "Telefone", "spendingLimit": "Limite de gastos", "adminAccess": "Acesso de administrador", "isAdmin": "É administrador", "isAdminDesc": "Conceder privilé<PERSON><PERSON> de administrador a este funcionário", "isAdminTooltip": "Administradores podem gerenciar configurações da empresa e outros funcionários", "firstNamePlaceholder": "Digite o nome", "lastNamePlaceholder": "Digite o sobrenome", "emailPlaceholder": "Digite o endereço de e-mail", "phonePlaceholder": "Digite o número de telefone", "spendingLimitPlaceholder": "Digite o limite de gastos", "save": "<PERSON><PERSON>", "saving": "Salvando..."}, "delete": {"confirmation": "Tem certeza de que deseja excluir este funcionário?", "success": "Funcionário excluído com sucesso"}, "edit": {"title": "Editar funcioná<PERSON>"}, "toasts": {"updateSuccess": "Funcionário atualizado com sucesso", "updateError": "Falha ao atualizar funcionário"}}, "toasts": {"createSuccess": "Empresa criada com sucesso", "createError": "Falha ao criar empresa", "updateSuccess": "Empresa atualizada com sucesso", "updateError": "Falha ao atualizar empresa", "deleteSuccess": "Empresa excluída com sucesso", "deleteError": "Falha ao excluir empresa"}}, "approvals": {"domain": "Aprovações", "title": "Aprovações", "subtitle": "Gerenciar fluxos de aprovação", "noApprovals": "Nenhuma aprovação encontrada", "noApprovalsDescription": "Atualmente não há aprovações para revisar.", "table": {"id": "ID", "type": "Tipo", "company": "Empresa", "customer": "Cliente", "amount": "Valor", "status": "Status", "createdAt": "<PERSON><PERSON><PERSON>"}, "status": {"pending": "Pendente", "approved": "<PERSON><PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON>", "unknown": "Desconhecido"}, "details": {"header": "Detalhes da aprovação", "summary": "Resumo da aprovação", "company": "Empresa", "customer": "Cliente", "order": "Pedido", "amount": "Valor", "updatedAt": "Atualizado", "reason": "Motivo", "actions": "Ações"}, "actions": {"approve": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON><PERSON><PERSON>", "confirmApprove": "Confirma<PERSON>", "confirmReject": "Confirma<PERSON>", "reasonPlaceholder": "Digite o motivo (opcional)..."}, "filters": {"status": "Filtrar por status"}, "toasts": {"approveSuccess": "Aprovado com sucesso", "approveError": "<PERSON>alha ao aprovar", "rejectSuccess": "Rejeitado com sucesso", "rejectError": "Falha ao rejeitar"}}, "dateTime": {"years_one": "<PERSON><PERSON>", "years_other": "<PERSON><PERSON>", "months_one": "<PERSON><PERSON><PERSON>", "months_other": "Meses", "weeks_one": "Se<PERSON>", "weeks_other": "Semanas", "days_one": "<PERSON>a", "days_other": "<PERSON><PERSON>", "hours_one": "<PERSON><PERSON>", "hours_other": "<PERSON><PERSON>", "minutes_one": "Min<PERSON>", "minutes_other": "<PERSON><PERSON><PERSON>", "seconds_one": "<PERSON><PERSON><PERSON>", "seconds_other": "<PERSON><PERSON><PERSON>"}}