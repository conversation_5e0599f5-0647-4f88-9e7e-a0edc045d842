{"$schema": "./$schema.json", "general": {"ascending": "Aufsteigend", "descending": "Absteigend", "add": "Hinzufügen", "start": "Start", "end": "<PERSON><PERSON>", "open": "<PERSON>en", "close": "Schließen", "apply": "<PERSON><PERSON><PERSON>", "range": "Reichweite", "search": "<PERSON><PERSON>", "of": "von", "results": "Ergebnisse", "pages": "Seiten", "next": "Nächste", "prev": "<PERSON><PERSON><PERSON>", "is": "Ist", "timeline": "Zeitleiste", "success": "Erfolg", "warning": "<PERSON><PERSON><PERSON>", "tip": "<PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON>", "selected": "Ausgewählt", "enabled": "Aktiviert", "disabled": "Deaktiviert", "expired": "Abgelaufen", "active": "Aktiv", "revoked": "Widerrufen", "new": "<PERSON>eu", "modified": "G<PERSON><PERSON>ndert", "added": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "removed": "ENTFERNT", "admin": "Admin", "store": "Shop", "details": "Einzelheiten", "items_one": "{{count}} <PERSON><PERSON><PERSON>", "items_other": "{{count}} <PERSON><PERSON><PERSON>", "countSelected": "{{count}} ausgewählt", "countOfTotalSelected": "{{count}} von {{total}} ausgewählt", "plusCount": "+ {{count}}", "plusCountMore": "+ {{count}} mehr", "areYouSure": "Bist du sicher?", "noRecordsFound": "<PERSON><PERSON> gefunden", "typeToConfirm": "Bitte geben Sie zur Bestätigung {val} ein:", "noResultsTitle": "<PERSON><PERSON>", "noResultsMessage": "Versuchen Sie, die Filter oder die Suchabfrage zu ändern", "noSearchResults": "<PERSON><PERSON>", "noSearchResultsFor": "<PERSON><PERSON> Suchergebnis<PERSON> für <0>'{{query}}'</0>", "noRecordsTitle": "<PERSON><PERSON>", "noRecordsMessage": "Es sind keine Datensätze vorhanden", "unsavedChangesTitle": "Sind <PERSON> sicher, dass Sie dieses Formular verlassen möchten?", "unsavedChangesDescription": "Sie haben nicht gespeicherte Änderungen, die verloren gehen, wenn Sie dieses Formular verlassen.", "includesTaxTooltip": "Die Preise in dieser Spalte verstehen sich inklusive Mehrwertsteuer.", "excludesTaxTooltip": "Die Preise in dieser Spalte verstehen sich exklusive Mehrwertsteuer.", "noMoreData": "<PERSON><PERSON>hr", "actions": "Aktionen"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "numberOfKeys_other": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "drawer": {"header_one": "JSON <0>· {{count}} Schlüssel</0>", "header_other": "JSON <0>· {{count}} Schlüssel</0>", "description": "<PERSON><PERSON> sich die JSON-Daten für dieses Objekt an."}}, "metadata": {"header": "<PERSON><PERSON><PERSON>", "numberOfKeys_one": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "numberOfKeys_other": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "edit": {"header": "<PERSON><PERSON><PERSON> bearbeiten", "description": "Bearbeiten Sie die Metadaten für dieses Objekt.", "successToast": "Metadaten wurden erfolgreich aktualisiert.", "actions": {"insertRowAbove": "<PERSON><PERSON><PERSON> oben einfügen", "insertRowBelow": "<PERSON><PERSON><PERSON> unten einfügen", "deleteRow": "Zeile löschen"}, "labels": {"key": "Schlüssel", "value": "Wert"}, "complexRow": {"label": "Einige Zeilen sind deaktiviert", "description": "Dieses Objekt enthält nicht-primitive Metadaten wie Arrays oder Objekte, die hier nicht bearbeitet werden können. Um die deaktivierten Zeilen zu bearbeiten, verwenden Sie direkt die API.", "tooltip": "<PERSON><PERSON> ist deaktiviert, da sie nicht-primitive Daten enthält."}}}, "validation": {"mustBeInt": "Der Wert muss eine ganze <PERSON> sein.", "mustBePositive": "Der Wert muss eine positive <PERSON><PERSON> sein."}, "actions": {"save": "Speichern", "saveAsDraft": "Als Entwurf speichern", "copy": "<PERSON><PERSON>", "copied": "<PERSON><PERSON><PERSON>", "duplicate": "Duplikat", "publish": "Veröffentlichen", "create": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Löschen", "remove": "Entfernen", "revoke": "Widerrufen", "cancel": "Abbrechen", "forceConfirm": "Bestätigung erzwingen", "continueEdit": "Bearbeiten Sie weiter", "enable": "Aktivieren", "disable": "Deaktivieren", "undo": "<PERSON><PERSON>g<PERSON><PERSON><PERSON> machen", "complete": "Vollständig", "viewDetails": "Details anzeigen", "back": "Zurück", "close": "Schließen", "showMore": "<PERSON><PERSON> anzeigen", "continue": "Weitermachen", "continueWithEmail": "Mit E-Mail anmelden", "idCopiedToClipboard": "ID in die Zwischenablage kopiert", "addReason": "<PERSON><PERSON><PERSON> hinzuf<PERSON>", "addNote": "<PERSON><PERSON>", "reset": "Z<PERSON>ücksetzen", "confirm": "Bestätigen", "edit": "<PERSON><PERSON><PERSON>", "addItems": "Elemente hinzufügen", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON>", "clearAll": "Alles löschen", "apply": "<PERSON><PERSON><PERSON>", "add": "Hinzufügen", "select": "<PERSON><PERSON><PERSON><PERSON>", "browse": "Durchsuchen", "logout": "Abmelden", "hide": "Verstecken", "export": "Export", "import": "Import"}, "operators": {"in": "In"}, "app": {"search": {"label": "<PERSON><PERSON>", "title": "<PERSON><PERSON>", "description": "Durchsuchen Sie Ihren gesamten Shop, einschließlich Bestellungen, Produkte, Kunden und mehr.", "allAreas": "Alle Bereiche", "navigation": "Navigation", "openResult": "<PERSON><PERSON><PERSON><PERSON>", "showMore": "<PERSON><PERSON> anzeigen", "placeholder": "<PERSON>e zu oder finde etwas...", "noResultsTitle": "<PERSON><PERSON> gefunden", "noResultsMessage": "Wir konnten nichts finden, was <PERSON><PERSON><PERSON> Suche entspricht.", "emptySearchTitle": "<PERSON><PERSON><PERSON> ein, um zu suchen", "emptySearchMessage": "<PERSON><PERSON><PERSON> Si<PERSON> ein Schlüsselwort oder eine Phrase ein, die Si<PERSON> erkunden möchten.", "loadMore": "<PERSON>den <PERSON> {{count}} mehr", "groups": {"all": "Alle Bereiche", "customer": "<PERSON><PERSON>", "customerGroup": "Kundengruppen", "product": "Produkte", "productVariant": "Produktvarianten", "inventory": "Inventar", "reservation": "Reservierungen", "category": "<PERSON><PERSON><PERSON>", "collection": "Sammlungen", "order": "Bestellungen", "promotion": "Werbeaktionen", "campaign": "<PERSON><PERSON>ag<PERSON>", "priceList": "Preislisten", "user": "<PERSON><PERSON><PERSON>", "region": "Regionen", "taxRegion": "Steuerregionen", "returnReason": "Rückgabegründe", "salesChannel": "Vertriebskanäle", "productType": "Produkttypen", "productTag": "Produkt-Tags", "location": "<PERSON><PERSON><PERSON>", "shippingProfile": "Versandprofile", "publishableApiKey": "Veröffentlichbare API-Schlüssel", "secretApiKey": "Geheime API-Schlüssel", "command": "<PERSON><PERSON><PERSON><PERSON>", "navigation": "Navigation"}}, "keyboardShortcuts": {"pageShortcut": "<PERSON><PERSON> zu", "settingShortcut": "Einstellungen", "commandShortcut": "<PERSON><PERSON><PERSON><PERSON>", "then": "<PERSON><PERSON>", "navigation": {"goToOrders": "Bestellungen", "goToProducts": "Produkte", "goToCollections": "Sammlungen", "goToCategories": "<PERSON><PERSON><PERSON>", "goToCustomers": "<PERSON><PERSON>", "goToCustomerGroups": "Kundengruppen", "goToInventory": "Inventar", "goToReservations": "Reservierungen", "goToPriceLists": "Preislisten", "goToPromotions": "Werbeaktionen", "goToCampaigns": "<PERSON><PERSON>ag<PERSON>"}, "settings": {"goToSettings": "Einstellungen", "goToStore": "Shop", "goToUsers": "<PERSON><PERSON><PERSON>", "goToRegions": "Regionen", "goToTaxRegions": "Steuerregionen", "goToSalesChannels": "Vertriebskanäle", "goToProductTypes": "Produkttypen", "goToLocations": "<PERSON><PERSON><PERSON>", "goToPublishableApiKeys": "Veröffentlichbare API-Schlüssel", "goToSecretApiKeys": "Geheime API-Schlüssel", "goToWorkflows": "Arbeitsabläufe", "goToProfile": "Profil", "goToReturnReasons": "Rückgabegründe"}}, "menus": {"user": {"documentation": "Dokumentation", "changelog": "Änderungsprotokoll", "shortcuts": "Tastenkürzel", "profileSettings": "Profileinstellungen", "theme": {"label": "<PERSON>a", "dark": "<PERSON><PERSON><PERSON>", "light": "Licht", "system": "System"}}, "store": {"label": "Shop", "storeSettings": "Shop-Einstellungen"}, "actions": {"logout": "Abmelden"}}, "nav": {"accessibility": {"title": "Navigation", "description": "Navigationsmenü für das Dashboard."}, "common": {"extensions": "Erweiterungen"}, "main": {"store": "Shop", "storeSettings": "Einstellungen speichern"}, "settings": {"header": "Einstellungen", "general": "Allgemein", "developer": "<PERSON><PERSON><PERSON><PERSON>", "myAccount": "<PERSON><PERSON>"}}}, "dataGrid": {"columns": {"view": "<PERSON><PERSON>", "resetToDefault": "Auf Standard zurücksetzen", "disabled": "Das Ändern der sichtbaren Spalten ist deaktiviert."}, "shortcuts": {"label": "Verknüpfungen", "commands": {"undo": "<PERSON><PERSON>g<PERSON><PERSON><PERSON> machen", "redo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON>", "paste": "Paste", "edit": "<PERSON><PERSON><PERSON>", "delete": "Löschen", "clear": "<PERSON><PERSON>", "moveUp": "Bewegen Si<PERSON> sich nach oben", "moveDown": "Bewegen Si<PERSON> sich nach unten", "moveLeft": "Bewegen Sie sich nach links", "moveRight": "Bewegen Sie sich nach rechts", "moveTop": "Nach oben verschieben", "moveBottom": "Nach unten bewegen", "selectDown": "<PERSON><PERSON><PERSON>en Si<PERSON> nach unten", "selectUp": "<PERSON><PERSON><PERSON>en Sie nach oben", "selectColumnDown": "Spalte nach unten auswählen", "selectColumnUp": "Spalte nach oben auswählen", "focusToolbar": "Fokus-Symbolleiste", "focusCancel": "Fokus abbrechen"}}, "errors": {"fixError": "<PERSON><PERSON> beheben", "count_one": "{{count}} <PERSON><PERSON>", "count_other": "{{count}} <PERSON><PERSON>"}}, "filters": {"date": {"today": "<PERSON><PERSON>", "lastSevenDays": "Letzte 7 Tage", "lastThirtyDays": "Letzte 30 Tage", "lastNinetyDays": "Letzte 90 Tage", "lastTwelveMonths": "Letzte 12 Monate", "custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "from": "<PERSON>", "to": "Bis"}, "compare": {"lessThan": "<PERSON><PERSON> als", "greaterThan": "<PERSON><PERSON><PERSON><PERSON><PERSON> als", "exact": "<PERSON><PERSON>", "range": "Reichweite", "lessThanLabel": "kleiner als {{value}}", "greaterThanLabel": "g<PERSON><PERSON><PERSON><PERSON> als {{value}}", "andLabel": "Und"}, "radio": {"yes": "<PERSON>a", "no": "<PERSON><PERSON>", "true": "<PERSON><PERSON><PERSON>", "false": "<PERSON><PERSON><PERSON>"}, "addFilter": "<PERSON><PERSON>"}, "errorBoundary": {"badRequestTitle": "400 – Ungültige Anfrage", "badRequestMessage": "Die Anfrage konnte aufgrund einer fehlerhaften Syntax vom Server nicht verstanden werden.", "notFoundTitle": "404 – <PERSON><PERSON> dieser Adresse gibt es keine Seite", "notFoundMessage": "Überprüfen Sie die URL und versuchen Sie es erneut, oder verwenden Sie die Suchleiste, um das Gesuchte zu finden.", "internalServerErrorTitle": "500 – <PERSON><PERSON>", "internalServerErrorMessage": "Auf dem <PERSON> ist ein unerwarteter Fehler aufgetreten. Bitte versuchen Si<PERSON> es später noch einmal.", "defaultTitle": "Es ist ein Fehler aufgetreten", "defaultMessage": "<PERSON><PERSON> dieser Seite ist ein unerwarteter Fehler aufgetreten.", "noMatchMessage": "Die gesuchte Seite existiert nicht.", "backToDashboard": "Zurück zum Dashboard"}, "addresses": {"title": "<PERSON><PERSON><PERSON>", "shippingAddress": {"header": "Lieferadresse", "editHeader": "Lieferadresse bearbeiten", "editLabel": "Lieferadresse", "label": "Lieferadresse"}, "billingAddress": {"header": "Re<PERSON>nungsadress<PERSON>", "editHeader": "Rechnungsadresse bearbeiten", "editLabel": "Re<PERSON>nungsadress<PERSON>", "label": "Re<PERSON>nungsadress<PERSON>", "sameAsShipping": "Entspricht der Lieferadresse"}, "contactHeading": "Kontakt", "locationHeading": "<PERSON><PERSON>"}, "email": {"editHeader": "E-Mail bearbeiten", "editLabel": "E-Mail", "label": "E-Mail"}, "transferOwnership": {"header": "Eigentum übertragen", "label": "Eigentum übertragen", "details": {"order": "Bestelldetails", "draft": "Entwurfsdetails"}, "currentOwner": {"label": "Aktueller Besitzer", "hint": "Der aktuelle Eigentümer der Bestellung."}, "newOwner": {"label": "<PERSON><PERSON><PERSON>", "hint": "Der neue Eigentümer, an den die Bestellung übertragen werden soll."}, "validation": {"mustBeDifferent": "Der neue Eigentümer muss sich vom aktuellen Eigentümer unterscheiden.", "required": "<PERSON>euer Eigentümer ist erforderlich."}}, "sales_channels": {"availableIn": "Verfügbar in <0>{{x}}</0> von <1>{{y}}</1> Vertriebskanälen"}, "products": {"domain": "Produkte", "list": {"noRecordsMessage": "<PERSON>rstellen Sie Ihr erstes Produkt, um mit dem Verkauf zu beginnen."}, "edit": {"header": "Produkt bearbeiten", "description": "Produktdetails bearbeiten.", "successToast": "Produkz {{title}} angepasst."}, "create": {"title": "Produkt erstellen", "description": "<PERSON><PERSON><PERSON><PERSON> ein neues Produkt.", "header": "Allgemein", "tabs": {"details": "Details", "organize": "Organisierren", "variants": "<PERSON><PERSON><PERSON>", "inventory": "Inventar-Kits"}, "errors": {"variants": "Bitte wählen Sie mindestens eine Variante aus.", "options": "Bitte erstellen Sie mindestens eine Option.", "uniqueSku": "Die SKU muss eindeutig sein."}, "inventory": {"heading": "Inventar-Kits", "label": "Fügen Sie Inventargegenstände zum Inventarkit der Variante hinzu.", "itemPlaceholder": "Wählen Sie den Inventargegenstand aus", "quantityPlaceholder": "Wie viele davon werden für den Bausatz benötigt?"}, "variants": {"header": "<PERSON><PERSON><PERSON>", "subHeadingTitle": "<PERSON><PERSON>, es handelt sich um ein Produkt mit Varianten", "subHeadingDescription": "<PERSON>n diese Option deaktiviert ist, erstellen wir eine Standardvariante für Sie", "optionTitle": {"placeholder": "Größe"}, "optionValues": {"placeholder": "Klein, Mittel, Groß"}, "productVariants": {"label": "Produktvarianten", "hint": "Dieses Ranking wirkt sich auf die Reihenfolge der Varianten in Ihrem Store aus.", "alert": "Fügen Sie Optionen hinzu, um Varianten zu erstellen.", "tip": "<PERSON><PERSON><PERSON>, die nicht aktiviert sind, werden nicht erstellt. Sie können nachträglich jederzeit Varianten erstellen und bearbeiten, diese Liste passt jedoch zu den Variationen in Ihren Produktoptionen."}, "productOptions": {"label": "Produktoptionen", "hint": "Definieren Sie die Optionen für das Produkt, z.B. Farbe, Größe usw."}}, "successToast": "Produkt {{title}} wurde erfolgreich erstellt."}, "export": {"header": "Produktliste exportieren", "description": "Exportieren Sie die Produktliste in eine CSV-Datei.", "success": {"title": "Wir bearbeiten Export ab", "description": "Der Datenexport kann einige Minuten dauern. Wir benachrichtigen Sie, wenn wir fertig sind."}, "filters": {"title": "Filter", "description": "Wenden Sie Filter in der Tabellenübersicht an, um diese Ansicht anzupassen"}, "columns": {"title": "Spalten", "description": "Passen Sie die exportierten Daten an spezifische Anforderungen an"}}, "import": {"header": "Produktliste importieren", "uploadLabel": "Produkte importieren", "uploadHint": "<PERSON><PERSON><PERSON> Sie eine CSV-Datei per Drag-and-Drop oder klicken Si<PERSON> zum Ho<PERSON>laden", "description": "Importieren Sie Produkte, indem Sie eine CSV-Datei in einem vordefinierten Format bereitstellen", "template": {"title": "Sie sind sich nicht sicher, wie Sie Ihre Liste ordnen sollen?", "description": "Laden Sie die Vorlage unten herunter, um sicherzustellen, dass Sie das richtige Format befolgen."}, "upload": {"title": "Laden Sie eine CSV-<PERSON><PERSON> hoch", "description": "Durch Importe können Sie Produkte hinzufügen oder aktualisieren. Um vorhandene Produkte zu aktualisieren, müssen Sie das vorhandene Handle und die vorhandene ID verwenden. Um vorhandene Varianten zu aktualisieren, müssen Sie die vorhandene ID verwenden. Bevor wir Produkte importieren, werden Sie um eine Bestätigung gebeten.", "preprocessing": "Vorverarbeitung...", "productsToCreate": "Es werden Produkte erstellt", "productsToUpdate": "Produkte werden aktualisiert"}, "success": {"title": "Wir bearbeiten Ihren Import", "description": "Das Importieren der Daten kann eine Weile dauern. Wir benachrichtigen Si<PERSON>, wenn wir fertig sind."}}, "deleteWarning": "<PERSON><PERSON> sind dabei, das Produkt {{title}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "variants": {"header": "<PERSON><PERSON><PERSON>", "empty": {"heading": "<PERSON><PERSON>", "description": "<PERSON>s gibt keine V<PERSON>, um angezeigt zu werden."}, "filtered": {"heading": "<PERSON><PERSON>", "description": "<PERSON><PERSON>ten stimmen mit den aktuellen Filterkriterien überein."}}, "attributes": "Attribute", "editAttributes": "Attribute bearbeiten", "editOptions": "Optionen bearbeiten", "editPrices": "<PERSON><PERSON> bear<PERSON>", "media": {"label": "Medien", "editHint": "Fügen Sie dem Produkt Medien hinzu, um es in Ihrem Schaufenster zu präsentieren.", "makeThumbnail": "Miniaturbild erstellen", "uploadImagesLabel": "Bilder hochladen", "uploadImagesHint": "<PERSON><PERSON><PERSON> Sie Bilder per Drag-and-Drop hierher oder klicken Si<PERSON> zum Ho<PERSON>laden.", "invalidFileType": "{{name}}' ist kein unterstützter Dateityp. Unterstützte Dateitypen sind: {{types}}.", "failedToUpload": "Das Hochladen der hinzugefügten Medien ist fehlgeschlagen. Bitte versuchen Sie es erneut.", "deleteWarning_one": "<PERSON>e sind dabei, {{count}} Bild zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "deleteWarning_other": "<PERSON><PERSON> sind dabei, {{count}} Bilder zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "deleteWarningWithThumbnail_one": "<PERSON>e sind dabei, {{count}} Bild einschließlich der Miniaturansicht zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "deleteWarningWithThumbnail_other": "<PERSON>e sind dabei, {{count}} Bilder einschließlich der Miniaturansicht zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "thumbnailTooltip": "Miniaturansicht", "galleryLabel": "Galerie", "downloadImageLabel": "Aktuelles Bild herunterladen", "deleteImageLabel": "Aktuelles Bild löschen", "emptyState": {"header": "Noch keine Medien", "description": "Fügen Sie dem Produkt Medien hinzu, um es in Ihrem Schaufenster zu präsentieren.", "action": "Medien hinzufügen"}, "successToast": "Medien wurden erfolgreich aktualisiert."}, "discountableHint": "<PERSON><PERSON> diese Option deaktiviert ist, werden auf dieses Produkt keine Rabatte gewährt.", "noSalesChannels": "In keinem Vertriebskanal verfügbar", "variantCount_one": "{{count}} <PERSON><PERSON><PERSON>", "variantCount_other": "{{count}} <PERSON><PERSON><PERSON>", "deleteVariantWarning": "<PERSON><PERSON> sind dabei, die Variante {{title}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "productStatus": {"draft": "<PERSON><PERSON><PERSON><PERSON>", "published": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "proposed": "Vorgeschlagen", "rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fields": {"title": {"label": "Titel", "hint": "Geben Sie Ihrem Produkt einen kurzen und klaren Titel.<0/>50-60 <PERSON><PERSON>chen ist die empfohlene Länge für Suchmaschinen.", "placeholder": "Winterjacke"}, "subtitle": {"label": "Untertitel", "placeholder": "Warm und gemütlich"}, "handle": {"label": "<PERSON><PERSON>", "tooltip": "Der Handle wird verwendet, um auf das Produkt in Ihrer Storefront zu verweisen. Wenn nicht angegeben, wird das Handle aus dem Produkttitel generiert.", "placeholder": "winterjacke"}, "description": {"label": "Beschreibung", "hint": "Geben Sie Ihrem Produkt eine kurze und klare Beschreibung.<0/>120-160 Zeichen ist die empfohlene Länge für Suchmaschinen.", "placeholder": "Eine warme und gemütliche Jacke"}, "discountable": {"label": "Rabattierbar", "hint": "<PERSON><PERSON> diese Option deaktiviert ist, werden auf dieses Produkt keine Rabatte gewährt"}, "type": {"label": "<PERSON><PERSON>"}, "collection": {"label": "<PERSON><PERSON><PERSON>"}, "categories": {"label": "<PERSON><PERSON><PERSON>"}, "tags": {"label": "Schlagworte"}, "sales_channels": {"label": "Vertriebskanäle", "hint": "Dieses Produkt ist nur dann im Standard-Vertriebskanal verfügbar, wenn es unberührt bleibt."}, "countryOrigin": {"label": "Ursprungsland"}, "material": {"label": "Material"}, "width": {"label": "Breite"}, "length": {"label": "<PERSON><PERSON><PERSON>"}, "height": {"label": "<PERSON><PERSON><PERSON>"}, "weight": {"label": "Gewicht"}, "options": {"label": "Produktoptionen", "hint": "<PERSON><PERSON><PERSON><PERSON> von Optionen werden Farbe, Größe usw. des Produkts definiert", "add": "Option hinzufügen", "optionTitle": "Optionstitel", "optionTitlePlaceholder": "Farbe", "variations": "Variationen (durch Kommas getrennt)", "variantionsPlaceholder": "Rot, Blau, Grün"}, "variants": {"label": "Produktvarianten", "hint": "<PERSON><PERSON><PERSON>, die nicht aktiviert sind, werden nicht erstellt. Diese Rangfolge wirkt sich auf die Rangfolge der Varianten in Ihrem Frontend aus."}, "mid_code": {"label": "Mittlerer Code"}, "hs_code": {"label": "HS-Code"}}, "variant": {"edit": {"header": "<PERSON><PERSON>te bearbeiten", "success": "Produktvariante erfolgreich bearbeitet"}, "create": {"header": "Variantendetails"}, "deleteWarning": "<PERSON><PERSON>chten Sie diese Variante wirklich löschen?", "pricesPagination": "1 – {{current}} von {{total}} Preisen", "tableItemAvailable": "{{availableCount}} verfügbar", "tableItem_one": "{{availableCount}} verfügbar am Standort {{locationCount}}", "tableItem_other": "{{availableCount}} an {{locationCount}} <PERSON><PERSON><PERSON> verfügbar", "inventory": {"notManaged": "<PERSON>cht verwaltet", "manageItems": "Verwalten Sie Inventargegenstände", "notManagedDesc": "<PERSON><PERSON><PERSON> diese Variante wird kein Lagerbestand verwaltet. Aktivieren Sie „Inventar verwalten“, um den Bestand der Variante zu verfolgen.", "manageKit": "Inventar-<PERSON>", "navigateToItem": "Gehen Sie zum Inventargegenstand", "actions": {"inventoryItems": "Gehen Sie zum Inventargegenstand", "inventoryKit": "Inventargegenstände anzeigen"}, "inventoryKit": "Inventar-Kit", "inventoryKitHint": "<PERSON><PERSON>t diese Variante aus mehreren Inventargegenständen?", "validation": {"itemId": "Bitte wählen Sie den Inventarartikel aus.", "quantity": "<PERSON>ge ist erforderlich. <PERSON>te geben Sie eine positive Zahl ein."}, "header": "Lager und Inventar", "editItemDetails": "Artikeldetails bearbeiten", "manageInventoryLabel": "<PERSON><PERSON><PERSON> verwalten", "manageInventoryHint": "<PERSON>n diese Option aktiviert ist, ändern wir die Lagerbestandsmenge für Sie, wenn Bestellungen und Retouren erstellt werden.", "allowBackordersLabel": "Rückstände zulassen", "allowBackordersHint": "<PERSON><PERSON> diese Option aktiviert ist, können Kunden die Variante auch dann kaufen, wenn keine verfügbare Menge vorhanden ist.", "toast": {"levelsBatch": "Lagerbestände aktualisiert.", "update": "Inventarartikel erfolgreich aktualisiert.", "updateLevel": "Der Lagerbestand wurde erfolgreich aktualisiert.", "itemsManageSuccess": "Inventargegenstände wurden erfolgreich aktualisiert."}}}, "options": {"header": "Optionen", "edit": {"header": "Option bearbeiten", "successToast": "Option {{title}} wurde erfolgreich aktualisiert."}, "create": {"header": "Option erstellen", "successToast": "Option {{title}} wurde erfolgreich erstellt."}, "deleteWarning": "<PERSON><PERSON> sind dabei, die Produktoption zu löschen: {{title}}. Diese Aktion kann nicht rückgängig gemacht werden."}, "organization": {"header": "Organisieren", "edit": {"header": "Organisation bearbeiten", "toasts": {"success": "Die Organisation von {{title}} wurde erfolgreich aktualisiert."}}}, "toasts": {"delete": {"success": {"header": "Produkt gelöscht", "description": "{{title}} wurde erfolgreich gelö<PERSON>t."}, "error": {"header": "Das Produkt konnte nicht gelöscht werden"}}}}, "collections": {"domain": "Sammlungen", "subtitle": "Organisieren Sie Produkte in Sammlungen.", "createCollection": "Sammlung er<PERSON>llen", "createCollectionHint": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> eine neue Sammlung, um Ihre Produkte zu organisieren.", "createSuccess": "Sammlung erfolgreich erstellt.", "editCollection": "<PERSON><PERSON><PERSON> bear<PERSON>ten", "handleTooltip": "Das Handle wird verwendet, um auf die Sammlung in Ihrer Storefront zu verweisen. Wenn nicht angegeben, wird das Handle aus dem Sammlungstitel generiert.", "deleteWarning": "<PERSON><PERSON> sind dabei, die Sammlung {{title}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "removeSingleProductWarning": "<PERSON><PERSON> sind dabei, das Produkt {{title}} aus der Sammlung zu entfernen. Diese Aktion kann nicht rückgängig gemacht werden.", "removeProductsWarning_one": "<PERSON><PERSON> sind dabei, {{count}} Produkt aus der Sammlung zu entfernen. Diese Aktion kann nicht rückgängig gemacht werden.", "removeProductsWarning_other": "<PERSON>e sind dabei, {{count}} Produkte aus der Sammlung zu entfernen. Diese Aktion kann nicht rückgängig gemacht werden.", "products": {"list": {"noRecordsMessage": "Es sind keine Produkte in der Sammlung vorhanden."}, "add": {"successToast_one": "Das Produkt wurde erfolgreich zur Sammlung hinzugefügt.", "successToast_other": "Produkte wurden erfolgreich zur Sammlung hinzugefügt."}, "remove": {"successToast_one": "Das Produkt wurde erfolgreich aus der Sammlung entfernt.", "successToast_other": "Produkte wurden erfolgreich aus der Sammlung entfernt."}}}, "categories": {"domain": "<PERSON><PERSON><PERSON>", "subtitle": "Organisieren Sie Produkte in Kategorien und verwalten Sie die Rangfolge und Hierarchie dieser Kategorien.", "create": {"header": "<PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> eine neue Kategorie, um Ihre Produkte zu organisieren.", "tabs": {"details": "Einzelheiten", "organize": "Ranking organisieren"}, "successToast": "<PERSON><PERSON><PERSON> {{name}} wurde erfolgreich erstellt."}, "edit": {"header": "<PERSON><PERSON><PERSON> bear<PERSON>", "description": "Bearbeiten Sie die Kategorie, um ihre Details zu aktualisieren.", "successToast": "Die Kategorie wurde erfolgreich aktualisiert."}, "delete": {"confirmation": "<PERSON><PERSON> sind dabei, die Kategorie {{name}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "successToast": "<PERSON><PERSON><PERSON> {{name}} wurde erfolgreich gelö<PERSON>t."}, "products": {"add": {"disabledTooltip": "Das Produkt ist bereits in dieser Kategorie.", "successToast_one": "{{count}} Produkt zur Kategorie hinzugefügt.", "successToast_other": "{{count}} Produkte zur Kategorie hinzugefügt."}, "remove": {"confirmation_one": "<PERSON><PERSON> sind dabei, {{count}} Produkt aus der Kategorie zu entfernen. Diese Aktion kann nicht rückgängig gemacht werden.", "confirmation_other": "<PERSON><PERSON> sind dabei, {{count}} Produkte aus der Kategorie zu entfernen. Diese Aktion kann nicht rückgängig gemacht werden.", "successToast_one": "{{count}} Produkt aus der Kategorie entfernt.", "successToast_other": "{{count}} Produkte aus der Kategorie entfernt."}, "list": {"noRecordsMessage": "<PERSON>s gibt keine Produkte in der Kategorie."}}, "organize": {"header": "Organisieren", "action": "Ranking bearbeiten"}, "fields": {"visibility": {"label": "Sichtweite", "internal": "Intern", "public": "<PERSON><PERSON><PERSON><PERSON>"}, "status": {"label": "Status", "active": "Aktiv", "inactive": "Inaktiv"}, "path": {"label": "Weg", "tooltip": "Zeigt den vollständigen Pfad der Kategorie an."}, "children": {"label": "Kinder"}, "new": {"label": "<PERSON>eu"}}}, "inventory": {"domain": "Inventar", "subtitle": "Verwalten Sie Ihre Inventargegenstände", "reserved": "Reserviert", "available": "Verfügbar", "locationLevels": "<PERSON><PERSON><PERSON>", "associatedVariants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manageLocations": "<PERSON><PERSON><PERSON> ver<PERSON>ten", "deleteWarning": "<PERSON><PERSON> sind dabei, einen Inventarartikel zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "editItemDetails": "Artikeldetails bearbeiten", "create": {"title": "Inventarartikel erstellen", "details": "Einzelheiten", "availability": "Verfügbarkeit", "locations": "<PERSON><PERSON><PERSON>", "attributes": "Attribute", "requiresShipping": "<PERSON><PERSON><PERSON><PERSON>", "requiresShippingHint": "Ist für den Lagerartikel ein V<PERSON>and erforderlich?", "successToast": "Der Inventarartikel wurde erfolgreich erstellt."}, "reservation": {"header": "Reservierung von {{itemName}}", "editItemDetails": "Reservierung bearbeiten", "lineItemId": "Werbebuchungs-ID", "orderID": "Bestell-ID", "description": "Beschreibung", "location": "<PERSON><PERSON>", "inStockAtLocation": "An diesem Standort auf Lager", "availableAtLocation": "Verfügbar an diesem Standort", "reservedAtLocation": "An diesem Standort reserviert", "reservedAmount": "Reservebetrag", "create": "Reservierung erstellen", "itemToReserve": "Artikel zum Reservieren", "quantityPlaceholder": "Wie viel möchten Sie reservieren?", "descriptionPlaceholder": "Um welche Art von Reservierung handelt es sich?", "successToast": "Die Reservierung wurde erfolgreich erstellt.", "updateSuccessToast": "Die Reservierung wurde erfolgreich aktualisiert.", "deleteSuccessToast": "Die Reservierung wurde erfolgreich gelöscht.", "errors": {"noAvaliableQuantity": "Der Lagerort hat keine verfügbare Menge.", "quantityOutOfRange": "Die Mindestmenge beträgt 1 und die Höchstmenge beträgt {{max}}."}}, "toast": {"updateLocations": "Standorte erfolgreich aktualisiert.", "updateLevel": "Der Lagerbestand wurde erfolgreich aktualisiert.", "updateItem": "Inventarartikel erfolgreich aktualisiert."}}, "giftCards": {"domain": "Geschenkkarten", "editGiftCard": "Geschenkkarte bearbeiten", "createGiftCard": "Geschenkkarte erstellen", "createGiftCardHint": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> manuell eine Geschenkkarte, die als Zahlungsmethode in Ihrem Shop verwendet werden kann.", "selectRegionFirst": "Wählen Sie zunächst eine Region aus", "deleteGiftCardWarning": "<PERSON>e sind dabei, die Geschenkkarte {{code}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "balanceHigherThanValue": "Der Saldo darf nicht höher sein als der ursprüngliche Betrag.", "balanceLowerThanZero": "<PERSON> Saldo darf nicht negativ sein.", "expiryDateHint": "<PERSON><PERSON><PERSON> haben unterschiedliche Gesetze bezüglich des Ablaufdatums von Geschenkkarten. Informieren Sie sich unbedingt über die örtlichen Vorschriften, bevor Sie ein Ablaufdatum festlegen.", "regionHint": "Wenn Sie die Region der Geschenkkarte ändern, ändert sich auch deren Währung, was möglicherweise Auswirkungen auf den Geldwert hat.", "enabledHint": "<PERSON>eb<PERSON>, ob die Geschenkkarte aktiviert oder deaktiviert ist.", "balance": "Gleichgewicht", "currentBalance": "Aktueller Kontostand", "initialBalance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "personalMessage": "Persönliche Nachricht", "recipient": "<PERSON><PERSON><PERSON><PERSON>"}, "customers": {"domain": "<PERSON><PERSON>", "list": {"noRecordsMessage": "Ihre Kunden werden hier angezeigt."}, "create": {"header": "<PERSON>nde anlegen", "hint": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> einen neuen Kunden und verwalten Sie dessen Daten.", "successToast": "Der Kunde {{email}} wurde erfolgreich erstellt."}, "groups": {"label": "Kundengruppen", "remove": "Sind <PERSON> sic<PERSON>, dass Si<PERSON> den Kunden aus der Kundengruppe „{{name}}“ entfernen möchten?", "removeMany": "<PERSON>d <PERSON>, dass Si<PERSON> Kunden aus den folgenden Kundengruppen gewinnen möchten: {{groups}}?", "alreadyAddedTooltip": "Der Kunde ist bereits in dieser Kundengruppe.", "list": {"noRecordsMessage": "Dieser Kunde gehört keiner Gruppe an."}, "add": {"success": "<PERSON><PERSON> hinzugefügt zu: {{groups}}.", "list": {"noRecordsMessage": "Bitte erstellen Sie zunächst eine Kundengruppe."}}, "removed": {"success": "Kunde entfernt aus: {{groups}}.", "list": {"noRecordsMessage": "Bitte erstellen Sie zunächst eine Kundengruppe."}}}, "edit": {"header": "Kunde bearbeiten", "emailDisabledTooltip": "Die E-Mail-Adresse kann für registrierte Kunden nicht geändert werden.", "successToast": "Der Kunde {{email}} wurde erfolgreich aktualisiert."}, "delete": {"title": "Kunde löschen", "description": "<PERSON><PERSON> sind dabei, den Kunden {{email}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "successToast": "Der Kunde {{email}} wurde erfolgreich gelöscht."}, "fields": {"guest": "Gas<PERSON>", "registered": "<PERSON><PERSON><PERSON>", "groups": "Gruppen"}, "registered": "<PERSON><PERSON><PERSON>", "guest": "Gas<PERSON>", "hasAccount": "<PERSON>"}, "customerGroups": {"domain": "Kundengruppen", "subtitle": "Organisieren Sie Kunden in Gruppen. Für Gruppen können unterschiedliche Aktionen und Preise gelten.", "create": {"header": "Kundengruppe erstellen", "hint": "<PERSON><PERSON><PERSON>n Si<PERSON> eine neue Kundengruppe, um Ihre Kunden zu segmentieren.", "successToast": "Die Kundengruppe {{name}} wurde erfolgreich erstellt."}, "edit": {"header": "Kundengruppe bearbeiten", "successToast": "Die Kundengruppe {{name}} wurde erfolgreich aktualisiert."}, "delete": {"title": "Kundengruppe löschen", "description": "<PERSON>e sind dabei, die Kundengruppe {{name}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "successToast": "Die Kundengruppe {{name}} wurde erfolgreich gelöscht."}, "customers": {"alreadyAddedTooltip": "Der Kunde wurde bereits zur Gruppe hinzugefügt.", "add": {"successToast_one": "Der Kunde wurde erfolgreich zur Gruppe hinzugefügt.", "successToast_other": "Kunden wurden erfolgreich zur Gruppe hinzugefügt.", "list": {"noRecordsMessage": "<PERSON><PERSON><PERSON>n Si<PERSON> zu<PERSON>ä<PERSON>t einen Kunden."}}, "remove": {"title_one": "Kunde entfernen", "title_other": "Kunden entfernen", "description_one": "<PERSON><PERSON> sind dabei, {{count}} Kunden aus der Kundengruppe zu entfernen. Diese Aktion kann nicht rückgängig gemacht werden.", "description_other": "<PERSON><PERSON> sind dabei, {{count}} Kunden aus der Kundengruppe zu entfernen. Diese Aktion kann nicht rückgängig gemacht werden."}, "list": {"noRecordsMessage": "Diese Gruppe hat keine Kunden."}}}, "orders": {"domain": "Bestellungen", "claim": "Beanspruchen", "exchange": "<PERSON><PERSON><PERSON><PERSON>", "return": "Zurückkehren", "cancelWarning": "<PERSON>e sind dabei, die Bestellung {{id}} zu stornieren. Diese Aktion kann nicht rückgängig gemacht werden.", "onDateFromSalesChannel": "{{date}} von {{salesChannel}}", "list": {"noRecordsMessage": "Ihre Bestellungen werden hier angezeigt."}, "summary": {"requestReturn": "Rücksendung anfordern", "allocateItems": "<PERSON><PERSON><PERSON>", "editOrder": "Bestellung bearbeiten", "editOrderContinue": "Bearbeiten Sie die Bestellung weiter", "inventoryKit": "Besteht aus {{count}}x Inventargegenständen", "itemTotal": "Artikelsumme", "shippingTotal": "Versand insgesamt", "discountTotal": "<PERSON><PERSON><PERSON> insgesamt", "taxTotalIncl": "Steuersumme (inklusive)", "itemSubtotal": "Zwischensumme des Artikels", "shippingSubtotal": "Versand-Zwischensumme", "discountSubtotal": "Rabatt-Zwischensumme", "taxTotal": "Steuersumme"}, "transfer": {"title": "Eigentumsübertragung", "requestSuccess": "Anfrage zur Eigentumsübertragung an: {{email}}.", "currentOwner": "Aktueller Besitzer", "newOwner": "<PERSON><PERSON><PERSON>", "currentOwnerDescription": "Der Kunde, der derzeit mit dieser Bestellung verbunden ist.", "newOwnerDescription": "Der Kunde, an den Sie diese Bestellung übertragen möchten."}, "payment": {"title": "Zahlungen", "isReadyToBeCaptured": "Die Zahlung <0/> kann erfasst werden.", "totalPaidByCustomer": "Vom Kunden bezahlter Gesamtbetrag", "capture": "Zahlung erfassen", "capture_short": "Erfassen", "refund": "Erstattung", "markAsPaid": "Als bezahlt markieren", "statusLabel": "Zahlungsstatus", "statusTitle": "Zahlungsstatus", "status": {"notPaid": "<PERSON><PERSON> bezahlt", "authorized": "Autorisiert", "partiallyAuthorized": "Teilweise autorisiert", "awaiting": "<PERSON><PERSON>", "captured": "<PERSON><PERSON><PERSON><PERSON>", "partiallyRefunded": "Teilweise erstattet", "partiallyCaptured": "Teilweise erfasst", "refunded": "Erstattet", "canceled": "Abgebrochen", "requiresAction": "<PERSON><PERSON><PERSON><PERSON>"}, "capturePayment": "Die Zahlung von {{amount}} wird erfasst.", "capturePaymentSuccess": "Die Zahlung von {{amount}} wurde erfolgreich erfasst", "markAsPaidPayment": "Die Zahlung von {{amount}} wird als bezahlt markiert.", "markAsPaidPaymentSuccess": "Zahlung von {{amount}} erfolgreich als bezahlt markiert", "createRefund": "Rückerstattung erstellen", "refundPaymentSuccess": "Rückerstattung des Betrags {{amount}} erfolgreich", "createRefundWrongQuantity": "Die Menge sollte eine Zahl zwischen 1 und {{number}} sein.", "refundAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von {{amount}}", "paymentLink": "Zahlungslink für {{amount}} kopieren", "selectPaymentToRefund": "Wählen Sie die Zahlung zur Rückerstattung aus"}, "edits": {"title": "Bestellung bearbeiten", "confirm": "Bestätigen Sie Bearbeiten", "confirmText": "<PERSON><PERSON> sind dabei, eine Bestellbearbeitung zu bestätigen. Diese Aktion kann nicht rückgängig gemacht werden.", "cancel": "Bearbeiten abbrechen", "currentItems": "Aktuelle Artikel", "currentItemsDescription": "Artikelmenge anpassen oder entfernen.", "addItemsDescription": "Sie können der Bestellung neue Artikel hinzufügen.", "addItems": "Elemente hinzufügen", "amountPaid": "Gezahlter Betrag", "newTotal": "Neue Summe", "differenceDue": "Differ<PERSON>z fällig", "create": "Bestellung bearbeiten", "currentTotal": "Aktuelle Gesamtsumme", "noteHint": "Fügen Sie eine interne Notiz für die Bearbeitung hinzu", "cancelSuccessToast": "Auftragsbearbeitung abgebrochen", "createSuccessToast": "Anfrage zur Auftragsbearbeitung erstellt", "activeChangeError": "<PERSON>ür die Bestellung gibt es bereits eine aktive Bestelländerung (Rückgabe, Reklamation, Umtausch usw.). Bitte schließen Sie die Änderung ab oder brechen Sie sie ab, bevor Sie die Bestellung bearbeiten.", "panel": {"title": "Auftragsbearbeitung angefordert", "titlePending": "Auftragsbearbeitung steht aus"}, "toast": {"canceledSuccessfully": "Auftragsbearbeitung abgebrochen", "confirmedSuccessfully": "Auftragsbearbeitung bestätigt"}, "validation": {"quantityLowerThanFulfillment": "Die Menge kann nicht kleiner oder gleich der erfüllten Menge sein"}}, "returns": {"create": "<PERSON><PERSON><PERSON> er<PERSON>llen", "confirm": "Bestätigen Sie die Rückgabe", "confirmText": "<PERSON><PERSON> sind dabei, eine Rücksendung zu bestätigen. Diese Aktion kann nicht rückgängig gemacht werden.", "inbound": "Ein<PERSON><PERSON>", "outbound": "Ausgehend", "sendNotification": "Benachrichtigung senden", "sendNotificationHint": "Benachrichtigen Sie den Kunden über die Rücksendung.", "returnTotal": "Gesamtrückgabe", "inboundTotal": "Eingehende Summe", "refundAmount": "Rückerstattungsbetrag", "outstandingAmount": "Ausstehender <PERSON>", "reason": "<PERSON><PERSON><PERSON>", "reasonHint": "<PERSON><PERSON><PERSON><PERSON> Sie aus, warum der Kunde Artikel zurückgeben möchte.", "note": "Notiz", "noInventoryLevel": "<PERSON><PERSON>", "noInventoryLevelDesc": "Der ausgewählte Standort verfügt über keinen Lagerbestand für die ausgewählten Artikel. Die Rücksendung kann angefordert werden, kann jedoch erst dann entgegengenommen werden, wenn ein Lagerbestand für den ausgewählten Standort erstellt wurde.", "noteHint": "<PERSON>e können frei tippen, wenn Sie etwas spezifizieren möchten.", "location": "<PERSON><PERSON>", "locationHint": "Wählen Sie den Ort aus, an den Sie die Artikel zurückgeben möchten.", "inboundShipping": "<PERSON><PERSON><PERSON><PERSON>", "inboundShippingHint": "<PERSON><PERSON><PERSON>en Sie die Methode aus, die Si<PERSON> verwenden möchten.", "returnableQuantityLabel": "Rückgabemenge", "refundableAmountLabel": "Rückerstattungsbetrag", "returnRequestedInfo": "Rückgabe von {{requestedItemsCount}}x Artikeln angefordert", "returnReceivedInfo": "{{requestedItemsCount}}x Artikel wurden erhalten zurückgegeben", "itemReceived": "<PERSON><PERSON><PERSON><PERSON>", "returnRequested": "Rückgabe erbeten", "damagedItemReceived": "Beschädigte Artikel erhalten", "damagedItemsReturned": "{{quantity}}x beschädigte Artikel zurückgegeben", "activeChangeError": "<PERSON><PERSON><PERSON> diese Bestellung wird gerade eine aktive Auftragsänderung durchgeführt. Bitte schließen Sie die Änderung zunächst ab oder verwerfen Sie sie.", "cancel": {"title": "Rückgabe abbrechen", "description": "Sind <PERSON> sicher, dass Sie die Rückgabeanfrage stornieren möchten?"}, "placeholders": {"noReturnShippingOptions": {"title": "<PERSON><PERSON>optionen gefunden", "hint": "<PERSON><PERSON><PERSON> den Standort wurden keine Rücksendeoptionen erstellt. Sie können eine unter <LinkComponent>Standort & Versand</LinkComponent> erstellen."}, "outboundShippingOptions": {"title": "Keine Optionen für den ausgehenden Versand gefunden", "hint": "<PERSON><PERSON><PERSON> den Standort wurden keine ausgehenden Versandoptionen erstellt. Sie können eine unter <LinkComponent>Standort & Versand</LinkComponent> erstellen."}}, "receive": {"action": "<PERSON><PERSON><PERSON>", "receiveItems": "{{ returnType }} {{ id }}", "restockAll": "Alle Artikel wieder auffüllen", "itemsLabel": "<PERSON><PERSON><PERSON><PERSON>", "title": "Erhalten Sie Artikel für #{{returnId}}", "sendNotificationHint": "Benachrichtigen Sie den Kunden über den Erhalt der Rücksendung.", "inventoryWarning": "<PERSON>te beachten Si<PERSON>, dass wir die Lagerbestände basierend auf Ihrer Eingabe oben automatisch anpassen.", "writeOffInputLabel": "Wie viele der Gegenstände sind beschädigt?", "toast": {"success": "Rücksendung erfolgreich erhalten.", "errorLargeValue": "Die Menge ist größer als die angeforderte Artikelmenge.", "errorNegativeValue": "Die Menge darf kein negativer Wert sein.", "errorLargeDamagedValue": "Die Menge der beschädigten Artikel + die Menge der unbeschädigten erhaltenen Artikel übersteigt die Gesamtmenge der Artikel in der Rücksendung. Bitte reduzieren Sie die Menge unbeschädigter Artikel."}}, "toast": {"canceledSuccessfully": "Rücksendung erfolgreich storniert", "confirmedSuccessfully": "Rücksendung erfolgreich bestätigt"}, "panel": {"title": "Rückkehr eingeleitet", "description": "Es muss ein offener Rückgabeantrag ausgefüllt werden"}}, "claims": {"create": "Ansp<PERSON><PERSON> erstellen", "confirm": "Anspruch bestätigen", "confirmText": "<PERSON><PERSON> sind dabei, einen Anspruch zu bestätigen. Diese Aktion kann nicht rückgängig gemacht werden.", "manage": "<PERSON><PERSON><PERSON><PERSON> verwalten", "outbound": "Ausgehend", "outboundItemAdded": "{{itemsCount}}x durch <PERSON><PERSON><PERSON><PERSON> hinzugefügt", "outboundTotal": "Ausgehender Gesamtwert", "outboundShipping": "Ausgehender Versand", "outboundShippingHint": "<PERSON><PERSON><PERSON>en Sie die Methode aus, die Si<PERSON> verwenden möchten.", "refundAmount": "Geschätzter Unterschied", "activeChangeError": "<PERSON><PERSON><PERSON> diese Bestellung liegt eine aktive Bestelländerung vor. Bitte schließen Sie die vorherige Änderung ab oder verwerfen Sie sie.", "actions": {"cancelClaim": {"successToast": "Der Anspruch wurde erfolgreich storniert."}}, "cancel": {"title": "Anspruch stornieren", "description": "Sind <PERSON> sic<PERSON>, dass Si<PERSON> den Anspruch stornieren möchten?"}, "tooltips": {"onlyReturnShippingOptions": "Diese Liste enthält nur Optionen für den Rückversand."}, "toast": {"canceledSuccessfully": "Anspruch erfolgreich storniert", "confirmedSuccessfully": "Anspruch erfolgreich bestätigt"}, "panel": {"title": "<PERSON><PERSON> e<PERSON>t", "description": "Es muss ein offener Anspruchsantrag ausgefüllt werden"}}, "exchanges": {"create": "Exchange erstellen", "manage": "Exchange verwalten", "confirm": "Bestätigen Sie den Austausch", "confirmText": "<PERSON><PERSON> sind dabei, einen Austausch zu bestätigen. Diese Aktion kann nicht rückgängig gemacht werden.", "outbound": "Ausgehend", "outboundItemAdded": "{{itemsCount}}x durch <PERSON><PERSON><PERSON><PERSON> hinzugefügt", "outboundTotal": "Ausgehender Gesamtwert", "outboundShipping": "Ausgehender Versand", "outboundShippingHint": "<PERSON><PERSON><PERSON>en Sie die Methode aus, die Si<PERSON> verwenden möchten.", "refundAmount": "Geschätzter Unterschied", "activeChangeError": "<PERSON><PERSON><PERSON> diese Bestellung liegt eine aktive Bestelländerung vor. Bitte schließen Sie die vorherige Änderung ab oder verwerfen Sie sie.", "actions": {"cancelExchange": {"successToast": "Der Umtausch wurde erfolgreich abgebrochen."}}, "cancel": {"title": "<PERSON><PERSON><PERSON><PERSON> abbrechen", "description": "Sind <PERSON> sicher, dass Sie den Umtausch abbrechen möchten?"}, "tooltips": {"onlyReturnShippingOptions": "Diese Liste enthält nur Optionen für den Rückversand."}, "toast": {"canceledSuccessfully": "Der Umtausch wurde erfolgreich abgebrochen", "confirmedSuccessfully": "Austausch erfolgreich bestätigt"}, "panel": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Es muss eine offene Austauschanfrage ausgefüllt werden"}}, "reservations": {"allocatedLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notAllocatedLabel": "<PERSON>cht zu<PERSON>"}, "allocateItems": {"action": "<PERSON><PERSON><PERSON>", "title": "Bestell<PERSON><PERSON>", "locationDescription": "<PERSON><PERSON><PERSON><PERSON> Sie den Standort aus, von dem aus Si<PERSON> zuweisen möchten.", "itemsToAllocate": "Zuzuordnende Elemente", "itemsToAllocateDesc": "<PERSON>ählen Sie die Anzahl der Elemente aus, die Si<PERSON> zuweisen möchten", "search": "Elemente suchen", "consistsOf": "Besteht aus {{num}}x Inventargegenständen", "requires": "<PERSON><PERSON><PERSON><PERSON> {{num}} pro <PERSON><PERSON>te", "toast": {"created": "Elemente erfolgreich zugewiesen"}, "error": {"quantityNotAllocated": "Es sind nicht zugeordnete Elemente vorhanden."}}, "shipment": {"title": "Als Versand mark<PERSON>en", "trackingNumber": "Tracking-<PERSON><PERSON><PERSON>", "addTracking": "Tracking-<PERSON><PERSON><PERSON>", "sendNotification": "Benachrichtigung senden", "sendNotificationHint": "Benachrichtigen Sie den Kunden über diese Lieferung.", "toastCreated": "Sendung erfolgreich erstellt."}, "fulfillment": {"cancelWarning": "Sie sind im Begriff, eine Erfüllung zu stornieren. Diese Aktion kann nicht rückgängig gemacht werden.", "markAsDeliveredWarning": "<PERSON>e sind dabei, die Erfüllung als geliefert zu markieren. Diese Aktion kann nicht rückgängig gemacht werden.", "unfulfilledItems": "Unerfüllte Artikel", "statusLabel": "Erfüllungsstatus", "statusTitle": "Erfüllungsstatus", "fulfillItems": "<PERSON><PERSON><PERSON>", "awaitingFulfillmentBadge": "Warten auf Erfüllung", "requiresShipping": "<PERSON><PERSON><PERSON><PERSON>", "number": "Erfüllung #{{number}}", "itemsToFulfill": "<PERSON>u versendende Produkte", "create": "Erfüllung erstellen", "available": "Verfügbar", "inStock": "<PERSON><PERSON>", "markAsShipped": "Als versendet markieren", "markAsDelivered": "<PERSON><PERSON> gel<PERSON> markieren", "itemsToFulfillDesc": "Wählen Sie die zu versendenden Artikel und Mengen aus", "locationDescription": "<PERSON><PERSON><PERSON><PERSON> Sie den Standort aus, von dem aus Sie Artikel versenden möchten.", "sendNotificationHint": "Benachrichtigen Sie Kunden über die erstellte Lieferung.", "methodDescription": "<PERSON>ählen Si<PERSON> eine andere Versandart als die vom Kunden ausgewählte", "error": {"wrongQuantity": "Es steht nur ein Artikel zur Erfüllung zur Verfügung", "wrongQuantity_other": "Die Menge sollte eine Zahl zwischen 1 und {{number}} sein.", "noItems": "<PERSON><PERSON> zu versendenden Produkte."}, "status": {"notFulfilled": "<PERSON><PERSON><PERSON><PERSON>", "partiallyFulfilled": "Teilweise versandbereit", "fulfilled": "Versandbereit", "partiallyShipped": "Teilweise versandt", "shipped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delivered": "<PERSON><PERSON><PERSON><PERSON>", "partiallyDelivered": "<PERSON><PERSON><PERSON><PERSON> gel<PERSON>", "partiallyReturned": "Teilweise zurückgegeben", "returned": "Zurückgegeben", "canceled": "<PERSON><PERSON><PERSON><PERSON>", "requiresAction": "<PERSON><PERSON><PERSON><PERSON>"}, "toast": {"created": "Erfüllung erfolgreich erstellt", "canceled": "Die Erfüllung wurde abgebrochen", "fulfillmentShipped": "Eine bereits versandte Erfüllung kann nicht storniert werden", "fulfillmentDelivered": "Die Erfüllung wurde als erfolgreich geliefert markiert"}, "trackingLabel": "Verfolgung", "shippingFromLabel": "<PERSON><PERSON><PERSON> ab", "itemsLabel": "Artikel"}, "refund": {"title": "Rückerstattung erstellen", "sendNotificationHint": "Benachrichtigen Sie Kunden über die erstellte Rückerstattung.", "systemPayment": "Systemzahlung", "systemPaymentDesc": "Eine oder mehrere Ihrer Zahlungen sind eine Systemzahlung. <PERSON><PERSON> Si<PERSON>, dass Medusa für solche Zahlungen keine Einziehungen und Rückerstattungen übernimmt.", "error": {"amountToLarge": "Es kann nicht mehr als der ursprüngliche Bestellbetrag erstattet werden.", "amountNegative": "Der Rückerstattungsbetrag muss eine positive Zahl sein.", "reasonRequired": "Bitte wählen Si<PERSON> einen Rückerstattungsgrund aus."}}, "customer": {"contactLabel": "Kontakt", "editEmail": "E-Mail bearbeiten", "transferOwnership": "Eigentum übertragen", "editBillingAddress": "Rechnungsadresse bearbeiten", "editShippingAddress": "Lieferadresse bearbeiten"}, "activity": {"header": "Aktivität", "showMoreActivities_one": "{{count}} weitere Aktivität anzeigen", "showMoreActivities_other": "{{count}} weitere Aktivitäten anzeigen", "comment": {"label": "Kommentar", "placeholder": "Hinterlassen Sie einen Kommentar", "addButtonText": "Kommentar hinzufügen", "deleteButtonText": "Kommentar löschen"}, "from": "<PERSON>", "to": "An", "events": {"common": {"toReturn": "Zurückkehren", "toSend": "Zum Versenden"}, "placed": {"title": "Bestellung aufgegeben", "fromSalesChannel": "von {{salesChannel}}"}, "canceled": {"title": "Bestellung storniert"}, "payment": {"awaiting": "<PERSON>ten auf Zahlung", "captured": "Zahlung erfasst", "canceled": "Zahlung storniert", "refunded": "Zahlung zurückerstattet"}, "fulfillment": {"created": "<PERSON><PERSON><PERSON>", "canceled": "Erfüllung abgesagt", "shipped": "<PERSON><PERSON><PERSON>t", "delivered": "<PERSON><PERSON><PERSON><PERSON><PERSON>ikel", "items_one": "{{count}} <PERSON><PERSON><PERSON>", "items_other": "{{count}} <PERSON><PERSON><PERSON>"}, "return": {"created": "Rückgabe #{{returnId}} an<PERSON><PERSON><PERSON>", "canceled": "Rückgabe #{{returnId}} abgebrochen", "received": "Rückgabe #{{returnId}} erhalten", "items_one": "{{count}} Artikel zurückgegeben", "items_other": "{{count}} Artikel zurückgegeben"}, "note": {"comment": "Kommentar", "byLine": "von {{author}}"}, "claim": {"created": "Anspruch #{{claimId}} an<PERSON><PERSON><PERSON>", "canceled": "Anspruch #{{claimId}} storniert", "itemsInbound": "{{count}} <PERSON><PERSON><PERSON>, der zurückgegeben werden soll", "itemsOutbound": "{{count}} <PERSON><PERSON><PERSON> zum Senden"}, "exchange": {"created": "Exchange #{{exchangeId}} ange<PERSON>ert", "canceled": "Austausch #{{exchangeId}} abgebrochen", "itemsInbound": "{{count}} <PERSON><PERSON><PERSON>, der zurückgegeben werden soll", "itemsOutbound": "{{count}} <PERSON><PERSON><PERSON> zum Senden"}, "edit": {"requested": "Bestellbearbeitung #{{editId}} ange<PERSON><PERSON>", "confirmed": "Bestellbearbeitung #{{editId}} bestätigt"}, "transfer": {"requested": "Bestellung #{{transferId}} übertragen", "confirmed": "Bestellung #{{transferId}} übertragen"}}}, "fields": {"displayId": "Anzeige-ID", "refundableAmount": "Rückerstattungsbetrag", "returnableQuantity": "Rückgabemenge"}}, "draftOrders": {"domain": "Befehlsentwürfe", "deleteWarning": "<PERSON>e sind dabei, den Bestellentwurf {{id}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "paymentLinkLabel": "Zahlungslink", "cartIdLabel": "Warenkorb-ID", "markAsPaid": {"label": "Als bezahlt markieren", "warningTitle": "Als bezahlt markieren", "warningDescription": "<PERSON><PERSON> sind dabei, den Bestellentwurf als bezahlt zu markieren. Dieser Vorgang kann nicht rückgängig gemacht werden und eine spätere Einziehung der Zahlung ist nicht mehr möglich."}, "status": {"open": "<PERSON>en", "completed": "Vollendet"}, "create": {"createDraftOrder": "Bestellentwurf erstellen", "createDraftOrderHint": "<PERSON>rstellen Si<PERSON> einen neuen Bestellentwurf, um die Details einer Bestellung zu verwalten, bevor diese aufgegeben wird.", "chooseRegionHint": "Wählen Sie eine Region", "existingItemsLabel": "<PERSON>orhand<PERSON> Artikel", "existingItemsHint": "Fügen Sie vorhandene Produkte zum Bestellentwurf hinzu.", "customItemsLabel": "Benutzerdefinierte Artikel", "customItemsHint": "Fügen Sie benutzerdefinierte Artikel zum Bestellentwurf hinzu.", "addExistingItemsAction": "Vorhandene Elemente hinzufügen", "addCustomItemAction": "Benutzerdefiniertes Element hinzufügen", "noCustomItemsAddedLabel": "Es wurden noch keine benutzerdefinierten Artikel hinzugefügt", "noExistingItemsAddedLabel": "<PERSON>s wurden noch keine vorhandenen Artikel hinzugefügt", "chooseRegionTooltip": "Wählen Sie zunächst eine Region aus", "useExistingCustomerLabel": "Bestandskunden nutzen", "addShippingMethodsAction": "Versandarten hinzufügen", "unitPriceOverrideLabel": "Überschreibung des Stückpreises", "shippingOptionLabel": "Versandoption", "shippingOptionHint": "Wählen Sie die Versandoption für den Bestellentwurf.", "shippingPriceOverrideLabel": "Versandpreisüberschreibung", "shippingPriceOverrideHint": "Überschreiben Sie den Versandpreis für den Bestellentwurf.", "sendNotificationLabel": "Benachrichtigung senden", "sendNotificationHint": "Senden Sie eine Benachrichtigung an den Kunden, wenn der Bestellentwurf erstellt wird."}, "validation": {"requiredEmailOrCustomer": "E-Mail oder Kunde ist erforderlich.", "requiredItems": "Mindestens ein Artikel ist erforderlich.", "invalidEmail": "E-Mail muss eine gültige E-Mail-Adresse sein."}}, "stockLocations": {"domain": "Standorte und Versand", "list": {"description": "Verwalten Sie die Lagerstandorte und Versandoptionen Ihres Shops."}, "create": {"header": "Lagerort erstellen", "hint": "Ein Lagerstandort ist ein physischer Standort, an dem Produkte gelagert und versendet werden.", "successToast": "Der Standort {{name}} wurde erfolgreich erstellt."}, "edit": {"header": "Lagerort bearbeiten", "viewInventory": "<PERSON><PERSON><PERSON> an<PERSON>", "successToast": "Standort {{name}} wurde erfolgreich aktualisiert."}, "delete": {"confirmation": "<PERSON>e sind dabei, den Lagerort {{name}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden."}, "fulfillmentProviders": {"header": "Fulfillment-Anbieter", "shippingOptionsTooltip": "Dieses Dropdown-<PERSON><PERSON> enthält nur Anbieter, die für diesen Standort aktiviert sind. Fügen Sie sie dem Standort hinzu, wenn das Dropdown-<PERSON><PERSON> deaktiviert ist.", "label": "Angeschlossene Fulfillment-Anbieter", "connectedTo": "Verbunden mit {{count}} von {{total}} Fulfillment-Anbietern", "noProviders": "Dieser Lagerort ist mit keinem Versanddienstleister verbunden.", "action": "Anbieter verbinden", "successToast": "Die Fulfillment-Anbieter für den Lagerstandort wurden erfolgreich aktualisiert."}, "fulfillmentSets": {"pickup": {"header": "<PERSON><PERSON><PERSON><PERSON>"}, "shipping": {"header": "<PERSON>ers<PERSON>"}, "disable": {"confirmation": "Sind <PERSON><PERSON> sicher, dass <PERSON> {{name}} deaktivieren möchten? Dadurch werden alle zugehörigen Servicezonen und Versandoptionen gelöscht. Dies kann nicht rückgängig gemacht werden.", "pickup": "Die Abholung wurde erfolgreich deaktiviert.", "shipping": "Der Versand wurde erfolgreich deaktiviert."}, "enable": {"pickup": "Die Abholung wurde erfolgreich aktiviert.", "shipping": "Der Versand wurde erfolgreich aktiviert."}}, "sidebar": {"header": "Versandkonfiguration", "shippingProfiles": {"label": "Versandprofile", "description": "Gruppieren Sie Produkte nach Versandanforderungen"}}, "salesChannels": {"header": "Vertriebskanäle", "label": "Angebundene Vertriebskanäle", "connectedTo": "Verbunden mit {{count}} von {{total}} Vertriebskanälen", "noChannels": "Der Standort ist an keine Vertriebskanäle angeschlossen.", "action": "Vertriebskanäle verbinden", "successToast": "Vertriebskanäle wurden erfolgreich aktualisiert."}, "shippingOptions": {"create": {"shipping": {"header": "Versandoption für {{zone}} erstellen", "hint": "<PERSON><PERSON><PERSON><PERSON> eine neue Versandoption, um zu definieren, wie Produkte von diesem Standort aus versendet werden.", "label": "Versandoptionen", "successToast": "Die Versandoption {{name}} wurde erfolgreich erstellt."}, "returns": {"header": "<PERSON><PERSON><PERSON>n Sie eine Rückgabeoption für {{zone}}", "hint": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> eine neue Rückgabeoption, um zu definieren, wie Produkte an diesen Standort zurückgegeben werden.", "label": "Rückgabeoptionen", "successToast": "Die Rückgabeoption {{name}} wurde erfolgreich erstellt."}, "tabs": {"details": "Einzelheiten", "prices": "<PERSON><PERSON>"}, "action": "Option erstellen"}, "delete": {"confirmation": "<PERSON>e sind dabei, die Versandoption {{name}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "successToast": "Die Versandoption {{name}} wurde erfolgreich gelöscht."}, "edit": {"header": "Versandoption bearbeiten", "action": "Option bearbeiten", "successToast": "Die Versandoption {{name}} wurde erfolgreich aktualisiert."}, "pricing": {"action": "<PERSON><PERSON> bear<PERSON>"}, "fields": {"count": {"shipping_one": "{{count}} Versandoption", "shipping_other": "{{count}} Versandoptionen", "returns_one": "{{count}} Rückgabeoption", "returns_other": "{{count}} Rückgabeoptionen"}, "priceType": {"label": "Preisart", "options": {"fixed": {"label": "Be<PERSON><PERSON>", "hint": "Der Preis der Versandoption ist fest und ändert sich nicht je nach Inhalt der Bestellung."}, "calculated": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Der Preis der Versandoption wird vom Versanddienstleister beim Bezahlvorgang berechnet."}}}, "enableInStore": {"label": "Im Store aktivieren", "hint": "<PERSON><PERSON> Kunden diese Option beim Bezahlvorgang nutzen können."}, "provider": "Fulfillment-Anbieter", "profile": "Versandprofil"}}, "serviceZones": {"create": {"headerPickup": "Servicezone für Abholung von {{location}} erstellen", "headerShipping": "Servicezone für den Versand von {{location}} erstellen", "action": "Servicezone erstellen", "successToast": "Die Servicezone {{name}} wurde erfolgreich erstellt."}, "edit": {"header": "Servicezone bearbeiten", "successToast": "Die Servicezone {{name}} wurde erfolgreich aktualisiert."}, "delete": {"confirmation": "<PERSON><PERSON> sind dabei, die Servicezone {{name}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "successToast": "Die Servicezone {{name}} wurde erfolgreich gelöscht."}, "manageAreas": {"header": "<PERSON><PERSON><PERSON> für {{name}} verwalten", "action": "<PERSON><PERSON><PERSON> verwalten", "label": "Bereiche", "hint": "<PERSON><PERSON>hlen Sie die geografischen Gebiete aus, die von der Servicezone abgedeckt werden.", "successToast": "Bereiche für {{name}} wurden erfolgreich aktualisiert."}, "fields": {"noRecords": "<PERSON>s gibt keine Servicezonen, zu denen Versandoptionen hinzugefügt werden können.", "tip": "Eine Servicezone ist eine Sammlung geografischer Zonen oder Gebiete. Es wird verwendet, um die verfügbaren Versandoptionen auf eine definierte Gruppe von Standorten zu beschränken."}}}, "shippingProfile": {"domain": "Versandprofile", "subtitle": "Gruppieren Sie Produkte mit ähnlichen Versandanforderungen in Profilen.", "create": {"header": "Versandprofil er<PERSON>", "hint": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein neues Versandprofil, um Produkte mit ähnlichen Versandanforderungen zu gruppieren.", "successToast": "Das Versandprofil {{name}} wurde erfolgreich erstellt."}, "delete": {"title": "Versandprofil löschen", "description": "<PERSON>e sind dabei, das Versandprofil {{name}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "successToast": "Das Versandprofil {{name}} wurde erfolgreich gelö<PERSON>t."}, "tooltip": {"type": "Geben Sie den Versandprofiltyp ein, zum Beispiel: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> usw."}}, "taxRegions": {"domain": "Steuerregionen", "list": {"hint": "<PERSON><PERSON><PERSON><PERSON>, was <PERSON><PERSON>hren Kunden berechnen, wenn diese in verschiedenen Ländern und Regionen einkaufen."}, "delete": {"confirmation": "<PERSON><PERSON> sind dabei, eine Steuerregion zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "successToast": "Die Steuerregion wurde erfolgreich gelöscht."}, "create": {"header": "Steuerregion erstellen", "hint": "<PERSON>rstellen Sie eine neue Steuerregion, um Steuersätze für ein bestimmtes Land zu definieren.", "errors": {"rateIsRequired": "<PERSON><PERSON> eines Standardsteuersatzes ist ein Steuersatz erforderlich.", "nameIsRequired": "Der Name ist beim Erstellen eines Standardsteuersatzes erforderlich."}, "successToast": "Die Steuerregion wurde erfolgreich erstellt."}, "province": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "Erstellen Sie eine Provinzsteuerregion", "hint": "Erstellen Sie eine neue Steuerregion, um Steuersätze für eine bestimmte Provinz zu definieren."}}, "state": {"header": "Staa<PERSON>", "create": {"header": "Erstellen Sie eine staatliche Steuerregion", "hint": "<PERSON>rstellen Sie eine neue Steuerregion, um Steuersätze für einen bestimmten Staat zu definieren."}}, "stateOrTerritory": {"header": "Staaten oder Gebiete", "create": {"header": "Erstellen Sie eine Steuerregion für Bundesstaat/Territorium", "hint": "Erstellen Sie eine neue Steuerregion, um Steuersätze für einen bestimmten Staat/ein bestimmtes Gebiet zu definieren."}}, "county": {"header": "Landkreise", "create": {"header": "Erstellen Sie eine County-Steuerregion", "hint": "<PERSON>rstellen Sie eine neue Steuerregion, um Steuersätze für einen bestimmten Landkreis zu definieren."}}, "region": {"header": "Regionen", "create": {"header": "Region-Steuerregion erstellen", "hint": "Erstellen Sie eine neue Steuerregion, um Steuersätze für eine bestimmte Region zu definieren."}}, "department": {"header": "Abteilungen", "create": {"header": "Erstellen Sie die Steuerregion der Abteilung", "hint": "Erstellen Sie eine neue Steuerregion, um Steuersätze für eine bestimmte Abteilung zu definieren."}}, "territory": {"header": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Erstellen Sie eine Gebietssteuerregion", "hint": "<PERSON>rstellen Sie eine neue Steuerregion, um Steuersätze für ein bestimmtes Gebiet zu definieren."}}, "prefecture": {"header": "Präfekturen", "create": {"header": "Erstellen Sie eine Präfektursteuerregion", "hint": "Erstellen Sie eine neue Steuerregion, um Steuersätze für eine bestimmte Präfektur zu definieren."}}, "district": {"header": "Bezirke", "create": {"header": "Erstellen Sie eine Bezirkssteuerregion", "hint": "Erstellen Sie eine neue Steuerregion, um Steuersätze für einen bestimmten Bezirk zu definieren."}}, "governorate": {"header": "Gouvernements", "create": {"header": "Erstellen Sie eine Steuerregion für ein Gouvernement", "hint": "<PERSON>rstellen Sie eine neue Steuerregion, um Steuersätze für ein bestimmtes Gouvernement zu definieren."}}, "canton": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "Erstellen Sie eine Steuerregion für den Kanton", "hint": "<PERSON>rstellen Sie eine neue Steuerregion, um Steuersätze für einen bestimmten Kanton zu definieren."}}, "emirate": {"header": "Emirate", "create": {"header": "Erstellen Sie eine Emirate-Steuerregion", "hint": "Erstellen Sie eine neue Steuerregion, um Steuersätze für ein bestimmtes Emirat zu definieren."}}, "sublevel": {"header": "Unterebenen", "create": {"header": "Erstellen Sie eine untergeordnete Steuerregion", "hint": "Erstellen Sie eine neue Steuerregion, um Steuersätze für eine bestimmte Unterebene zu definieren."}}, "taxOverrides": {"header": "Überschreibt", "create": {"header": "Überschreibung erstellen", "hint": "<PERSON><PERSON><PERSON><PERSON> einen Steuersatz, der die Standardsteuersätze für ausgewählte Bedingungen überschreibt."}, "edit": {"header": "Überschreibung bearbeiten", "hint": "Bearbeiten Sie den Steuersatz, der die Standardsteuersätze für ausgewählte Bedingungen überschreibt."}}, "taxRates": {"create": {"header": "Steuersatz erstellen", "hint": "<PERSON><PERSON><PERSON>n Si<PERSON> einen neuen Steuersatz, um den Steuersatz für eine Region zu definieren.", "successToast": "Der Steuersatz wurde erfolgreich erstellt."}, "edit": {"header": "Steuersatz bearbeiten", "hint": "Bearbeiten Sie den Steuersatz, um den Steuersatz für eine Region zu definieren.", "successToast": "Der Steuersatz wurde erfolgreich aktualisiert."}, "delete": {"confirmation": "<PERSON>e sind dabei, den Steuersatz {{name}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "successToast": "Der Steuersatz wurde erfolgreich gelöscht."}}, "fields": {"isCombinable": {"label": "Kombinierbar", "hint": "<PERSON><PERSON> dieser Steuersatz mit dem Standardsatz aus der Steuerregion kombiniert werden kann.", "true": "Kombinierbar", "false": "<PERSON><PERSON> komb<PERSON>"}, "defaultTaxRate": {"label": "Standardsteuersatz", "tooltip": "Der Standardsteuersatz für diese Region. Ein Beispiel ist der Standard-Mehrwertsteuersatz für ein Land oder eine Region.", "action": "<PERSON><PERSON>ellen Sie einen Standardsteuersatz"}, "taxRate": "Steuersatz", "taxCode": "Steuerkennzeichen", "targets": {"label": "Ziele", "hint": "<PERSON>ählen Sie die Ziele aus, für die dieser Steuersatz gelten soll.", "options": {"product": "Produkte", "productCollection": "Produktkollektionen", "productTag": "Produkt-Tags", "productType": "Produkttypen", "customerGroup": "Kundengruppen"}, "operators": {"in": "In", "on": "An", "and": "Und"}, "placeholders": {"product": "Nach Produkten suchen", "productCollection": "Suchen Sie nach Produktkollektionen", "productTag": "Suchen Sie nach Produkt-Tags", "productType": "Suchen Sie nach Produkttypen", "customerGroup": "Suche nach Kundengruppen"}, "tags": {"product": "Produkt", "productCollection": "Produktkollektion", "productTag": "Produkt-Tag", "productType": "Produkttyp", "customerGroup": "Kundengruppe"}, "modal": {"header": "Ziele hinzufügen"}, "values_one": "{{count}} Wert", "values_other": "{{count}} Werte", "numberOfTargets_one": "{{count}} <PERSON><PERSON>", "numberOfTargets_other": "{{count}} <PERSON><PERSON><PERSON>", "additionalValues_one": "und {{count}} mehr Wert", "additionalValues_other": "und {{count}} weitere Werte", "action": "<PERSON><PERSON>"}, "sublevels": {"labels": {"province": "<PERSON><PERSON><PERSON>", "state": "Zustand", "region": "Region", "stateOrTerritory": "Staat/Territorium", "department": "Abteilung", "county": "County", "territory": "<PERSON><PERSON><PERSON>", "prefecture": "Präfektur", "district": "Bezirk", "governorate": "Gouvernement", "emirate": "Emirat", "canton": "<PERSON><PERSON>", "sublevel": "Sublevel-Code"}, "placeholders": {"province": "Provinz auswählen", "state": "Bundesland auswählen", "region": "Region auswählen", "stateOrTerritory": "Wählen Sie Bundesland/Gebiet aus", "department": "Abteilung auswählen", "county": "Landkreis auswählen", "territory": "Gebiet auswählen", "prefecture": "Präfektur auswählen", "district": "Bezirk auswählen", "governorate": "<PERSON>ä<PERSON>en Sie ein Gouvernement aus", "emirate": "Emirat auswählen", "canton": "<PERSON><PERSON> auswählen"}, "tooltips": {"sublevel": "Geben Sie den ISO 3166-2-Code für die untergeordnete Steuerregion ein.", "notPartOfCountry": "{{province}} scheint nicht Teil von {{country}} zu sein. Bitte überprüfen Si<PERSON> noch einmal, ob dies korrekt ist."}, "alert": {"header": "Untergeordnete Regionen sind für diese Steuerregion deaktiviert", "description": "Untergeordnete Regionen sind für diese Region standardmäßig deaktiviert. Sie können ihnen ermöglichen, untergeordnete Regionen wie Provinzen, Bundesstaaten oder Territorien zu erstellen.", "action": "Untergeordnete Regionen aktivieren"}}, "noDefaultRate": {"label": "Keine Standardrate", "tooltip": "<PERSON><PERSON><PERSON> diese Steuerregion gibt es keinen Standardsteuersatz. Wenn es einen Standardsatz gibt, z. B. die Mehrwertsteuer eines Landes, fügen Si<PERSON> ihn bitte dieser Region hinzu."}}}, "promotions": {"domain": "Werbeaktionen", "sections": {"details": "Aktionsdetails"}, "tabs": {"template": "<PERSON><PERSON>", "details": "Einzelheiten", "campaign": "<PERSON><PERSON><PERSON><PERSON>"}, "fields": {"type": "<PERSON><PERSON>", "value_type": "Werttyp", "value": "Wert", "campaign": "<PERSON><PERSON><PERSON><PERSON>", "method": "Verfahren", "allocation": "<PERSON><PERSON><PERSON><PERSON>g", "addCondition": "Bedingung hinzufügen", "clearAll": "Alles löschen", "amount": {"tooltip": "Wählen Sie den Währungscode aus, um die Festlegung des Betrags zu ermöglichen"}, "conditions": {"rules": {"title": "Wer kann diesen Code verwenden?", "description": "Welcher Kunde darf den Aktionscode nutzen? Der Aktionscode kann von allen Kunden verwendet werden, sofern er nicht geändert wird."}, "target-rules": {"title": "Auf welche Artikel gilt die Aktion?", "description": "Die Aktion gilt für Artikel, die die folgenden Bedingungen erfüllen."}, "buy-rules": {"title": "Was muss im Warenkorb sein, um die Aktion freizuschalten?", "description": "<PERSON><PERSON> diese Bedingungen zutreffen, aktivieren wir die Aktion für die Zielartikel."}}}, "tooltips": {"campaignType": "Der Währungscode muss in der Aktion ausgewählt werden, um ein Ausgabenbudget festzulegen."}, "errors": {"requiredField": "Erforderliches Feld", "promotionTabError": "Beheben Sie Fehler im Tab „Werbung“, bevor <PERSON> fortfahren"}, "toasts": {"promotionCreateSuccess": "Aktion ({{code}}) wurde erfolgreich erstellt."}, "create": {}, "edit": {"title": "Aktionsdetails bearbeiten", "rules": {"title": "Nutzungsbedingungen bearbeiten"}, "target-rules": {"title": "Artikelbedingungen bearbeiten"}, "buy-rules": {"title": "Kaufregeln bearbeiten"}}, "campaign": {"header": "<PERSON><PERSON><PERSON><PERSON>", "edit": {"header": "<PERSON><PERSON><PERSON><PERSON>", "successToast": "Die Kampagne der Aktion wurde erfolgreich aktualisiert."}, "actions": {"goToCampaign": "Gehen Sie zur Kampagne"}}, "campaign_currency": {"tooltip": "Dies ist die Währung der Aktion. Ändern Sie es auf der Registerkarte „Details“."}, "form": {"required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "and": "UND", "selectAttribute": "Wählen Sie Attribut aus", "campaign": {"existing": {"title": "<PERSON>eh<PERSON><PERSON> Kampagne", "description": "Fügen Sie Werbung zu einer vorhandenen Kampagne hinzu.", "placeholder": {"title": "<PERSON><PERSON> v<PERSON><PERSON><PERSON>", "desc": "Sie können eine erstellen, um mehrere Werbeaktionen zu verfolgen und Budgetgrenzen festzulegen."}}, "new": {"title": "Neue Kampagne", "description": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> eine neue Kampagne für diese Aktion."}, "none": {"title": "<PERSON><PERSON>", "description": "Fahren Sie fort, ohne Werbung mit Kampagne zu verknüpfen"}}, "status": {"title": "Status"}, "method": {"label": "Verfahren", "code": {"title": "Aktionscode", "description": "Kunden müssen diesen Code beim Bezahlvorgang eingeben"}, "automatic": {"title": "Automatisch", "description": "Kunden werden diese Aktion an der Kasse sehen"}}, "max_quantity": {"title": "Maximale Menge", "description": "Maximale Anzahl an Artikeln, für die diese Aktion gilt."}, "type": {"standard": {"title": "Standard", "description": "Eine Standardaktion"}, "buyget": {"title": "<PERSON><PERSON><PERSON>", "description": "Kaufen Sie X und erhalten Sie Y-Aktion"}}, "allocation": {"each": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> den Wert auf jedes <PERSON> an"}, "across": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> den Wert auf alle Elemente an"}}, "code": {"title": "Code", "description": "Der Code, den Ihre Kunden beim Bezahlvorgang eingeben."}, "value": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "value_type": {"fixed": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Der zu rabattierende Betrag. z.B. 100"}, "percentage": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Der Prozentsatz, um den der Betrag abgezinst wird. z.B. 8 %"}}}, "deleteWarning": "<PERSON>e sind dabei, die Aktion {{code}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "createPromotionTitle": "Werbeaktion erstellen", "type": "Werbetyp", "conditions": {"add": "Bedingung hinzufügen", "list": {"noRecordsMessage": "Fügen Sie eine Bedingung hinzu, um einzuschränken, für welche Artikel die Aktion gilt."}}}, "campaigns": {"domain": "<PERSON><PERSON>ag<PERSON>", "details": "Details zur Kampagne", "status": {"active": "Aktiv", "expired": "Abgelaufen", "scheduled": "<PERSON><PERSON><PERSON>"}, "delete": {"title": "Bist du sicher?", "description": "<PERSON><PERSON> sind dabei, die Kampagne „{{name}}“ zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "successToast": "Die Kampagne „{{name}}“ wurde erfolgreich erstellt."}, "edit": {"header": "<PERSON><PERSON><PERSON><PERSON>", "description": "Bearbeiten Sie die Details der Kampagne.", "successToast": "<PERSON><PERSON><PERSON><PERSON> „{{name}}“ wurde erfolgreich aktualisiert."}, "configuration": {"header": "Konfiguration", "edit": {"header": "Kampagnenkonfiguration bearbeiten", "description": "Bearbeiten Sie die Konfiguration der Kampagne.", "successToast": "Die Kampagnenkonfiguration wurde erfolgreich aktualisiert."}}, "create": {"title": "Kampag<PERSON> er<PERSON>", "description": "<PERSON><PERSON><PERSON>n Si<PERSON> eine Werbekampagne.", "hint": "<PERSON><PERSON><PERSON>n Si<PERSON> eine Werbekampagne.", "header": "Kampag<PERSON> er<PERSON>", "successToast": "Die Kampagne „{{name}}“ wurde erfolgreich erstellt."}, "fields": {"name": "Name", "identifier": "<PERSON><PERSON><PERSON>", "start_date": "Startdatum", "end_date": "Enddatum", "total_spend": "Budget ausgegeben", "total_used": "Verwendetes Budget", "budget_limit": "Budgetlimit", "campaign_id": {"hint": "In dieser Liste werden nur Kampagnen mit demselben Währungscode wie die Aktion angezeigt."}}, "budget": {"create": {"hint": "<PERSON><PERSON><PERSON>n Sie ein Budget für die Kampagne.", "header": "Kampagnenbudget"}, "details": "Kampagnenbudget", "fields": {"type": "<PERSON><PERSON>", "currency": "Währung", "limit": "Limit", "used": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type": {"spend": {"title": "Ausgeben", "description": "Legen Sie ein Limit für den gesamten rabattierten Betrag aller Aktionsnutzungen fest."}, "usage": {"title": "Verwendung", "description": "Legen Si<PERSON> ein Limit fest, wie oft die Aktion genutzt werden kann."}}, "edit": {"header": "Kampagnenbudget bearbeiten"}}, "promotions": {"remove": {"title": "Werbung aus der Kampagne entfernen", "description": "<PERSON><PERSON> sind dabei, {{count}} Werbeaktionen aus der Kampagne zu entfernen. Diese Aktion kann nicht rückgängig gemacht werden."}, "alreadyAdded": "Diese Aktion wurde bereits zur Kampagne hinzugefügt.", "alreadyAddedDiffCampaign": "Diese Aktion wurde bereits einer anderen Kampagne ({{name}}) hinzugefügt.", "currencyMismatch": "Die Währung der Werbeaktion und der Kampagne stimmt nicht überein", "toast": {"success": "{{count}} Werbeaktion(en) erfolgreich zur Kampagne hinzugefügt"}, "add": {"list": {"noRecordsMessage": "<PERSON><PERSON><PERSON>n Si<PERSON> zunächst eine Werbeaktion."}}, "list": {"noRecordsMessage": "In der Kampagne gibt es keine Werbeaktionen."}}, "deleteCampaignWarning": "<PERSON><PERSON> sind dabei, die Kampagne {{name}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "Preislisten", "subtitle": "Erstellen Sie Sonderangebote oder überschreiben Sie Preise für bestimmte Konditionen.", "delete": {"confirmation": "<PERSON><PERSON> sind dabei, die Preisliste {{title}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "successToast": "Die Preisliste {{title}} wurde erfolgreich gelöscht."}, "create": {"header": "<PERSON><PERSON><PERSON><PERSON> er<PERSON>", "subheader": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> eine neue Preisliste, um die Preise Ihrer Produkte zu verwalten.", "tabs": {"details": "Einzelheiten", "products": "Produkte", "prices": "<PERSON><PERSON>"}, "successToast": "Die Preisliste {{title}} wurde erfolgreich erstellt.", "products": {"list": {"noRecordsMessage": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> zu<PERSON>t ein Produkt."}}}, "edit": {"header": "<PERSON><PERSON><PERSON><PERSON> bearbeiten", "successToast": "Die Preisliste {{title}} wurde erfolgreich aktualisiert."}, "configuration": {"header": "Konfiguration", "edit": {"header": "Bearbeiten Sie die Preislistenkonfiguration", "description": "Bearbeiten Sie die Konfiguration der Preisliste.", "successToast": "Die Preislistenkonfiguration wurde erfolgreich aktualisiert."}}, "products": {"header": "Produkte", "actions": {"addProducts": "Produkte hinzufügen", "editPrices": "<PERSON><PERSON> bear<PERSON>"}, "delete": {"confirmation_one": "<PERSON><PERSON> sind dabei, die Preise für {{count}} Produkt in der Preisliste zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "confirmation_other": "<PERSON><PERSON> sind dabei, die Preise für {{count}} Produkte in der Preisliste zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "successToast_one": "Preise für {{count}} Produkt erfolgreich gelöscht.", "successToast_other": "Preise für {{count}} Produkte erfolgreich gelöscht."}, "add": {"successToast": "Die Preise wurden erfolgreich zur Preisliste hinzugefügt."}, "edit": {"successToast": "Die Preise wurden erfolgreich aktualisiert."}}, "fields": {"priceOverrides": {"label": "Preisüberschreibungen", "header": "Preisüberschreibungen"}, "status": {"label": "Status", "options": {"active": "Aktiv", "draft": "<PERSON><PERSON><PERSON><PERSON>", "expired": "Abgelaufen", "scheduled": "<PERSON><PERSON><PERSON>"}}, "type": {"label": "<PERSON><PERSON>", "hint": "<PERSON><PERSON>hlen Sie die Art der Preisliste, die Si<PERSON> erstellen möchten.", "options": {"sale": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Verkaufspreise sind vorübergehende Preisänderungen für Produkte."}, "override": {"label": "Überschreiben", "description": "Overrides werden in der Regel verwendet, um kundenspezifische Preise zu erstellen."}}}, "startsAt": {"label": "Pre<PERSON><PERSON>e hat ein Start<PERSON>?", "hint": "Planen Sie die zukünftige Aktivierung der Preisliste."}, "endsAt": {"label": "Preisliste hat ein Ablaufdatum?", "hint": "<PERSON><PERSON> Sie, die Preisliste in Zukunft zu deaktivieren."}, "customerAvailability": {"header": "Wählen Sie Kundengruppen", "label": "Kundenverfügbarkeit", "hint": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> aus, für welche Kundengruppen die Preisliste gelten soll.", "placeholder": "Suche nach Kundengruppen", "attribute": "Kundengruppen"}}}, "profile": {"domain": "Profil", "manageYourProfileDetails": "Verwalten Sie Ihre Profildetails.", "fields": {"languageLabel": "<PERSON><PERSON><PERSON>", "usageInsightsLabel": "Nutzungseinblicke"}, "edit": {"header": "<PERSON><PERSON>", "languageHint": "Die Sprache, die Sie im Admin-Dashboard verwenden möchten. Die Sprache Ihres Shops ändert sich dadurch nicht.", "languagePlaceholder": "Sprache auswählen", "usageInsightsHint": "Teilen Sie Nutzungseinblicke und helfen Si<PERSON> un<PERSON>, <PERSON><PERSON><PERSON> zu verbessern. Weitere Informationen darüber, was wir sammeln und wie wir es verwenden, finden Si<PERSON> in unserer <0>Dokumentation</0>."}, "toast": {"edit": "Profiländerungen gespeichert"}}, "users": {"domain": "<PERSON><PERSON><PERSON>", "editUser": "<PERSON><PERSON><PERSON> bearbeiten", "inviteUser": "<PERSON><PERSON><PERSON> e<PERSON>n", "inviteUserHint": "Laden Si<PERSON> einen neuen Benutzer in Ihren Shop ein.", "sendInvite": "Einladung senden", "pendingInvites": "Ausstehende Einladungen", "deleteInviteWarning": "<PERSON><PERSON> sind dabei, die Einladung für {{email}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "resendInvite": "Einladung erneut senden", "copyInviteLink": "Einladungslink kopieren", "expiredOnDate": "Abgelaufen am {{date}}", "validFromUntil": "<PERSON><PERSON><PERSON><PERSON> von <0>{{von}}</0> - <1>{{bis}}</1>", "acceptedOnDate": "Ak<PERSON><PERSON>iert am {{date}}", "inviteStatus": {"accepted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pending": "<PERSON><PERSON><PERSON><PERSON>", "expired": "Abgelaufen"}, "roles": {"admin": "Admin", "developer": "<PERSON><PERSON><PERSON><PERSON>", "member": "<PERSON><PERSON><PERSON><PERSON>"}, "deleteUserWarning": "<PERSON><PERSON> sind dabei, den Benutzer {{name}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "invite": "Einladen"}, "store": {"domain": "Shop", "manageYourStoresDetails": "Verwalten Sie die Details Ihres Shops", "editStore": "Shop bearbeiten", "defaultCurrency": "Standardwährung", "defaultRegion": "Standardregion", "swapLinkTemplate": "<PERSON><PERSON><PERSON> austauschen", "paymentLinkTemplate": "Vorlage für einen Zahlungslink", "inviteLinkTemplate": "Vorlage für einen Einladungslink", "currencies": "Währungen", "addCurrencies": "Währungen hinzufügen", "enableTaxInclusivePricing": "Aktivieren Sie die Preisgestaltung inklusive Steuern", "disableTaxInclusivePricing": "Deaktivieren Sie die Preisgestaltung inklusive Steuern", "removeCurrencyWarning_one": "<PERSON><PERSON> sind dabei, {{count}} Währung aus Ihrem Shop zu entfernen. <PERSON><PERSON><PERSON>, dass Sie alle Preise in der Währung entfernt haben, bevor <PERSON> fort<PERSON>hren.", "removeCurrencyWarning_other": "<PERSON><PERSON> sind dabei, {{count}} Währungen aus Ihrem Shop zu entfernen. <PERSON><PERSON><PERSON>, dass Sie alle Preise unter Verwendung der Währungen entfernt haben, bevor <PERSON> fort<PERSON>hren.", "currencyAlreadyAdded": "Die Währung wurde Ihrem Shop bereits hinzugefügt.", "edit": {"header": "Shop bearbeiten"}, "toast": {"update": "Shop erfolgreich aktualisiert", "currenciesUpdated": "Währungen erfolgreich aktualisiert", "currenciesRemoved": "Währungen wurden erfolgreich aus dem Shop entfernt", "updatedTaxInclusivitySuccessfully": "Die Preise inklusive Steuern wurden erfolgreich aktualisiert"}}, "regions": {"domain": "Regionen", "subtitle": "Eine Region ist ein Gebiet, in dem Sie Produkte verkaufen. Sie kann mehrere Länder umfassen und hat unterschiedliche Steuersätze, Anbieter und Währungen.", "createRegion": "Region erstellen", "createRegionHint": "Verwalten Sie Steuersätze und Anbieter für eine Reihe von <PERSON>ä<PERSON>.", "addCountries": "<PERSON><PERSON><PERSON>", "editRegion": "Region bearbeiten", "countriesHint": "<PERSON>ügen Sie die in dieser Region enthaltenen Länder hinzu.", "deleteRegionWarning": "<PERSON>e sind dabei, die Region {{name}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "removeCountriesWarning_one": "<PERSON>e sind dabei, {{count}} Land aus der Region zu entfernen. Diese Aktion kann nicht rückgängig gemacht werden.", "removeCountriesWarning_other": "<PERSON><PERSON> sind dabei, {{count}} <PERSON><PERSON><PERSON> aus der Region zu entfernen. Diese Aktion kann nicht rückgängig gemacht werden.", "removeCountryWarning": "<PERSON>e sind dabei, das Land {{name}} aus der Region zu entfernen. Diese Aktion kann nicht rückgängig gemacht werden.", "automaticTaxesHint": "<PERSON><PERSON> diese Option aktiviert ist, werden die Steuern beim Bezahlvorgang nur auf Grundlage der Lieferadresse berechnet.", "taxInclusiveHint": "<PERSON><PERSON> diese Option aktiviert ist, verstehen sich die Preise in der Region inklusive Steuern.", "providersHint": "Fügen Sie hinzu, welche Zahlungsanbieter in dieser Region verfügbar sind.", "shippingOptions": "Versandoptionen", "deleteShippingOptionWarning": "<PERSON>e sind dabei, die Versandoption {{name}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "return": "Zurückkehren", "outbound": "Ausgehend", "priceType": "Preisart", "flatRate": "<PERSON><PERSON><PERSON><PERSON>", "calculated": "<PERSON><PERSON><PERSON><PERSON>", "list": {"noRecordsMessage": "Erstellen Sie eine Region für die Gebiete, in denen Sie verkaufen."}, "toast": {"delete": "Region erfolgreich gelöscht", "edit": "Regionsbearbeitung gespeichert", "create": "Region erfolgreich erstellt", "countries": "Die Länder der Region wurden erfolgreich aktualisiert"}, "shippingOption": {"createShippingOption": "Versandoption erstellen", "createShippingOptionHint": "<PERSON>rstellen Sie eine neue Versandoption für die Region.", "editShippingOption": "Versandoption bearbeiten", "fulfillmentMethod": "Erfüllungsmethode", "type": {"outbound": "Ausgehend", "outboundHint": "Verwenden Sie diese Option, wenn Sie eine Versandoption für den Versand von Produkten an den Kunden erstellen.", "return": "Zurückkehren", "returnHint": "Verwenden Sie diese Option, wenn Sie eine Versandoption erstellen, damit der Kunde Produkte an Sie zurücksenden kann."}, "priceType": {"label": "Preisart", "flatRate": "<PERSON><PERSON><PERSON><PERSON>", "calculated": "<PERSON><PERSON><PERSON><PERSON>"}, "availability": {"adminOnly": "Nur Administrator", "adminOnlyHint": "<PERSON><PERSON> diese Option aktiviert ist, ist die Versandoption nur im Admin-Dashboard und nicht im Storefront verfügbar."}, "taxInclusiveHint": "<PERSON><PERSON> diese Option aktiviert ist, versteht sich der Preis der Versandoption inklusive Steuern.", "requirements": {"label": "Anforderungen", "hint": "Geben Sie die Anforderungen für die Versandoption an."}}}, "taxes": {"domain": "Steuerregionen", "domainDescription": "Verwalten Sie Ihre Steuerregion", "countries": {"taxCountriesHint": "Die Steuereinstellungen gelten für die aufgeführten Länder."}, "settings": {"editTaxSettings": "Bearbeiten Sie die Steuereinstellungen", "taxProviderLabel": "Steueranbieter", "systemTaxProviderLabel": "Systemsteueranbieter", "calculateTaxesAutomaticallyLabel": "Steuern automatisch berechnen", "calculateTaxesAutomaticallyHint": "Wenn diese Option aktiviert ist, werden die Steuersätze automatisch berechnet und auf Warenkörbe angewendet. Wenn die Funktion deaktiviert ist, müssen die Steuern an der Kasse manuell berechnet werden. Manuelle Steuern werden für die Verwendung mit externen Steueranbietern empfohlen.", "applyTaxesOnGiftCardsLabel": "Erheben Sie Steuern auf Geschenkkarten", "applyTaxesOnGiftCardsHint": "<PERSON>n diese Option aktiviert ist, werden beim Bezahlvorgang Steuern auf Geschenkkarten erhoben. In einigen Ländern schreiben die Steuervorschriften vor, dass beim <PERSON><PERSON> von Geschenkkarten Steuern erhoben werden müssen.", "defaultTaxRateLabel": "Standardsteuersatz", "defaultTaxCodeLabel": "Standardsteuercode"}, "defaultRate": {"sectionTitle": "Standardsteuersatz"}, "taxRate": {"sectionTitle": "Steuersätze", "createTaxRate": "Steuersatz erstellen", "createTaxRateHint": "<PERSON>rstellen Sie einen neuen Steuersatz für die Region.", "deleteRateDescription": "<PERSON>e sind dabei, den Steuersatz {{name}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "editTaxRate": "Steuersatz bearbeiten", "editRateAction": "<PERSON><PERSON><PERSON>", "editOverridesAction": "Überschreibungen bearbeiten", "editOverridesTitle": "Steuersatzüberschreibungen bearbeiten", "editOverridesHint": "Geben Sie die Überschreibungen für den Steuersatz an.", "deleteTaxRateWarning": "<PERSON>e sind dabei, den Steuersatz {{name}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "productOverridesLabel": "Produktüberschreibungen", "productOverridesHint": "Geben Sie die Produktüberschreibungen für den Steuersatz an.", "addProductOverridesAction": "Fügen Sie Produktüberschreibungen hinzu", "productTypeOverridesLabel": "Produkttypüberschreibungen", "productTypeOverridesHint": "Geben Sie die Produkttypüberschreibungen für den Steuersatz an.", "addProductTypeOverridesAction": "Fügen Sie Produkttypüberschreibungen hinzu", "shippingOptionOverridesLabel": "Die Versandoption hat Vorrang", "shippingOptionOverridesHint": "Geben Sie die Versandoption an, die den Steuersatz überschreibt.", "addShippingOptionOverridesAction": "Versandoptionsüberschreibungen hinzufügen", "productOverridesHeader": "Produkte", "productTypeOverridesHeader": "Produkttypen", "shippingOptionOverridesHeader": "Versandoptionen"}}, "locations": {"domain": "<PERSON><PERSON><PERSON>", "editLocation": "<PERSON>ort bearbeiten", "addSalesChannels": "Vertriebskanäle hinzufügen", "noLocationsFound": "<PERSON><PERSON> gefunden", "selectLocations": "Wählen Sie Standorte aus, an denen der Artikel vorrätig ist.", "deleteLocationWarning": "<PERSON>e sind dabei, den Standort {{name}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "removeSalesChannelsWarning_one": "<PERSON>e sind dabei, {{count}} Vertriebskanal vom Standort zu entfernen.", "removeSalesChannelsWarning_other": "<PERSON>e sind dabei, {{count}} Vertriebskanäle vom Standort zu entfernen.", "toast": {"create": "Standort erfolgreich erstellt", "update": "Der Standort wurde erfolgreich aktualisiert", "removeChannel": "Vertriebskanal erfolgreich entfernt"}}, "reservations": {"domain": "Reservierungen", "subtitle": "Verwalten Sie die reservierte Menge an Lagerartikeln.", "deleteWarning": "<PERSON><PERSON> sind dabei, eine Reservierung zu löschen. Diese Aktion kann nicht rückgängig gemacht werden."}, "salesChannels": {"domain": "Vertriebskanäle", "subtitle": "Verwalten Sie die Online- und Offline-Kanäle, über die Sie Produkte verkaufen.", "createSalesChannel": "Erstellen Sie einen Vertriebskanal", "createSalesChannelHint": "<PERSON><PERSON><PERSON>n Si<PERSON> einen neuen Vertriebskanal, über den Sie Ihre Produkte verkaufen können.", "enabledHint": "Geben Sie an, ob der Vertriebskanal aktiviert ist.", "removeProductsWarning_one": "<PERSON><PERSON> sind dabei, {{count}} Produkt aus {{sales_channel}} zu entfernen.", "removeProductsWarning_other": "<PERSON>e sind dabei, {{count}} Produkte aus {{sales_channel}} zu entfernen.", "addProducts": "Produkte hinzufügen", "editSalesChannel": "Vertriebskanal bearbeiten", "productAlreadyAdded": "Das Produkt wurde bereits zum Vertriebskanal hinzugefügt.", "deleteSalesChannelWarning": "<PERSON>e sind dabei, den Vertriebskanal {{name}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "toast": {"create": "Vertriebskanal erfolgreich erstellt", "update": "Vertriebskanal erfolgreich aktualisiert", "delete": "Vertriebskanal erfolgreich gelöscht"}, "products": {"list": {"noRecordsMessage": "Es sind keine Produkte im Vertriebskanal vorhanden."}, "add": {"list": {"noRecordsMessage": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> zu<PERSON>t ein Produkt."}}}}, "apiKeyManagement": {"domain": {"publishable": "Veröffentlichbare API-Schlüssel", "secret": "Geheime API-Schlüssel"}, "subtitle": {"publishable": "Verwalten Sie in der Storefront verwendete API-Schlüssel, um den Umfang der Anfragen auf bestimmte Vertriebskanäle zu beschränken.", "secret": "Verwalten Sie API-<PERSON><PERSON><PERSON><PERSON>, die zur Authentifizierung von Administratorbenutzern in Administratoranwendungen verwendet werden."}, "status": {"active": "Aktiv", "revoked": "Widerrufen"}, "type": {"publishable": "Verö<PERSON>ntlichbar", "secret": "Geheimnis"}, "create": {"createPublishableHeader": "Erstellen Sie einen veröffentlichbaren API-Schlüssel", "createPublishableHint": "<PERSON>rst<PERSON>n Si<PERSON> einen neuen veröffentlichbaren API-Schlüssel, um den Umfang der Anfragen auf bestimmte Vertriebskanäle zu beschränken.", "createSecretHeader": "Erstellen Sie einen geheimen API-Schlüssel", "createSecretHint": "<PERSON>rstellen Si<PERSON> einen neuen geheimen API-Schlüssel, um als authentifizierter Admin-Benutzer auf die Medusa-API zuzugreifen.", "secretKeyCreatedHeader": "Geheimer Schlüssel erstellt", "secretKeyCreatedHint": "Ihr neuer geheimer Schlüssel wurde generiert. Jetzt kopieren und sicher aufbewahren. Dies ist das einzige Mal, dass es angezeigt wird.", "copySecretTokenSuccess": "Der geheime Schlüssel wurde in die Zwischenablage kopiert.", "copySecretTokenFailure": "Der geheime Schlüssel konnte nicht in die Zwischenablage kopiert werden.", "successToast": "Der API-Schlüssel wurde erfolgreich erstellt."}, "edit": {"header": "API-Schlüssel bearbeiten", "description": "Bearbeiten Sie den Titel des API-Schlüssels.", "successToast": "Der API-Schlüssel {{title}} wurde erfolgreich aktualisiert."}, "salesChannels": {"title": "Vertriebskanäle hinzufügen", "description": "Fügen Sie die Vertriebskanäle hinzu, auf die der API-Schlüssel beschränkt sein soll.", "successToast_one": "{{count}} Vertriebskanal wurde erfolgreich zum API-Schlüssel hinzugefügt.", "successToast_other": "{{count}} Vertriebskanäle wurden erfolgreich zum API-Schl<PERSON><PERSON> hinzugefügt.", "alreadyAddedTooltip": "Der Vertriebskanal wurde bereits zum API-Schlüssel hinzugefügt.", "list": {"noRecordsMessage": "Im Geltungsbereich des veröffentlichbaren API-Schlüssels gibt es keine Vertriebskanäle."}}, "delete": {"warning": "<PERSON><PERSON> sind dabei, den API-Schlüssel {{title}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "successToast": "Der API-Schlüssel {{title}} wurde erfolgreich <PERSON>t."}, "revoke": {"warning": "<PERSON><PERSON> sind dabei, den API-Schlüssel {{title}} zu widerrufen. Diese Aktion kann nicht rückgängig gemacht werden.", "successToast": "Der API-Schlüssel {{title}} wurde erfolgreich widerrufen."}, "addSalesChannels": {"list": {"noRecordsMessage": "<PERSON><PERSON><PERSON>n Si<PERSON> zunächst einen Vertriebskanal."}}, "removeSalesChannel": {"warning": "<PERSON>e sind dabei, den Vertriebskanal {{name}} aus dem API-Schlüssel zu entfernen. Diese Aktion kann nicht rückgängig gemacht werden.", "warningBatch_one": "<PERSON>e sind dabei, {{count}} Vertriebskanal aus dem API-Schlüssel zu entfernen. Diese Aktion kann nicht rückgängig gemacht werden.", "warningBatch_other": "<PERSON>e sind dabei, {{count}} Vertriebskanäle aus dem API-Schlüssel zu entfernen. Diese Aktion kann nicht rückgängig gemacht werden.", "successToast": "Der Vertriebskanal wurde erfolgreich aus dem API-Schlüssel entfernt.", "successToastBatch_one": "{{count}} Vertriebskanal wurde erfolgreich aus dem API-Schlüssel entfernt.", "successToastBatch_other": "{{count}} Vertriebskanäle wurden erfolgreich aus dem API-Schlüssel entfernt."}, "actions": {"revoke": "API-Schlüssel widerrufen", "copy": "API-Schlüssel kopieren", "copySuccessToast": "Der API-Schlüssel wurde in die Zwischenablage kopiert."}, "table": {"lastUsedAtHeader": "Zuletzt verwendet um", "createdAtHeader": "Widerrufen am"}, "fields": {"lastUsedAtLabel": "Zuletzt verwendet um", "revokedByLabel": "Widerrufen durch", "revokedAtLabel": "Widerrufen am", "createdByLabel": "<PERSON><PERSON><PERSON><PERSON> von"}}, "returnReasons": {"domain": "Rückgabegründe", "subtitle": "Verwalten Sie die Gründe für zurückgegebene Artikel.", "calloutHint": "Verwalten Sie die Gründe für die Kategorisierung von Retouren.", "editReason": "<PERSON><PERSON><PERSON><PERSON><PERSON> bearbeiten", "create": {"header": "Rückgabegrund hinzufügen", "subtitle": "Geben Sie die häufigsten Gründe für Rücksendungen an.", "hint": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> einen neuen Retourengrund, um Retouren zu kategorisieren.", "successToast": "Rückgabegrund {{label}} wurde erfolgreich erstellt."}, "edit": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON> bearbeiten", "subtitle": "Bearbeiten Sie den Wert des Rückgabegrunds.", "successToast": "Rückgabegrund {{label}} wurde erfolgreich aktualisiert."}, "delete": {"confirmation": "<PERSON>e sind dabei, den Rückgabegrund {{label}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "successToast": "Rückgabegrund {{label}} wurde erfolgreich gelö<PERSON>t."}, "fields": {"value": {"label": "Wert", "placeholder": "falsche_Größe", "tooltip": "Der Wert sollte eine eindeutige Kennung für den Rückgabegrund sein."}, "label": {"label": "Etikett", "placeholder": "Falsche Größe"}, "description": {"label": "Beschreibung", "placeholder": "Der Kunde hat die falsche Größe erhalten"}}}, "login": {"forgotPassword": "Passwort vergessen? - <0>Zurücksetzen</0>", "title": "Willkommen bei Medusa", "hint": "Melden Sie sich an, um auf ihr Konto zuzugreifen"}, "invite": {"title": "Willkommen bei Medusa", "hint": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> unten Ihr Konto", "backToLogin": "<PERSON><PERSON><PERSON> zum Login", "createAccount": "Benutzerkonto erstellen", "alreadyHaveAccount": "Sie haben bereits ein Konto? - <0>Anmelden</0>", "emailTooltip": "Ihre E-Mail-Adresse kann nicht geändert werden. Wenn Sie eine andere E-Mail-Adresse verwenden möchten, muss eine neue Einladung gesendet werden.", "invalidInvite": "Die Einladung ist ungültig oder abgelaufen.", "successTitle": "<PERSON><PERSON> Konto wurde registriert", "successHint": "Beginnen Sie sofort mit Me<PERSON>a Admin.", "successAction": "Starten Sie Medusa <PERSON>", "invalidTokenTitle": "Ihr Einladungstoken ist ungültig", "invalidTokenHint": "Versuchen Sie, einen neuen Einladungslink anzufordern.", "passwordMismatch": "Passwörter stimmen nicht überein", "toast": {"accepted": "Einladung erfolgreich angenommen"}}, "resetPassword": {"title": "Passwort zurücksetzen", "hint": "Geben Si<PERSON> unten Ihre E-Mail-Adresse ein und wir senden Ihnen Anweisungen zum Zurücksetzen Ihres Passworts.", "email": "E-Mail", "sendResetInstructions": "Anweisungen zum Zurücksetzen senden", "backToLogin": "<0>Zurück zur Anmeldung</0>", "newPasswordHint": "<PERSON><PERSON><PERSON>en Si<PERSON> unten ein neues Passwort.", "invalidTokenTitle": "Ihr Reset-Token ist ungültig", "invalidTokenHint": "<PERSON>ersuche<PERSON> Sie, einen neuen Link zum Zurücksetzen anzufordern.", "expiredTokenTitle": "Ihr Reset-Token ist abgelaufen", "goToResetPassword": "Gehen Sie zu Passwort zurücksetzen", "resetPassword": "Passwort zurücksetzen", "newPassword": "Neues Passwort", "repeatNewPassword": "Neues Passwort wiederholen", "tokenExpiresIn": "Token läuft in <0>{{time}}</0> Minuten ab", "successfulRequestTitle": "<PERSON><PERSON>en wurde erfolgreich eine E-Mail gesendet", "successfulRequest": "Wir haben Ihnen eine E-Mail gesendet, mit der Sie Ihr Passwort zurücksetzen können. Überprüfen Sie Ihren Spam-Ordner, wenn Sie die E-Mail nach einigen Minuten noch nicht erhalten haben.", "successfulResetTitle": "Passwort-<PERSON><PERSON> er<PERSON>ich", "successfulReset": "Bitte melden Si<PERSON> sich auf der Anmeldeseite an.", "passwordMismatch": "Passwörter stimmen nicht überein", "invalidLinkTitle": "Ihr Reset-Link ist ungültig", "invalidLinkHint": "Versuchen Sie er<PERSON>ut, Ihr Passwort zurückzusetzen."}, "workflowExecutions": {"domain": "Arbeitsabläufe", "subtitle": "<PERSON><PERSON> sich Workflow-Ausführungen in Ihrer Medusa-Anwendung an und verfolgen Si<PERSON> sie.", "transactionIdLabel": "Transaktions-ID", "workflowIdLabel": "Workflow-ID", "progressLabel": "Fort<PERSON><PERSON>t", "stepsCompletedLabel_one": "Schritt {{completed}} von {{count}}", "stepsCompletedLabel_other": " Schritt {{completed}} von {{count}}", "list": {"noRecordsMessage": "Es wurden noch keine Workflows ausgeführt."}, "history": {"sectionTitle": "Geschichte", "runningState": "Läuft...", "awaitingState": "<PERSON><PERSON>", "failedState": "Fehlgeschlagen", "skippedState": "Übersprungen", "skippedFailureState": "Übersprungen (Fehler)", "definitionLabel": "Definition", "outputLabel": "Ausgabe", "compensateInputLabel": "Eingabe kompensieren", "revertedLabel": "Zurückgesetzt", "errorLabel": "<PERSON><PERSON>"}, "state": {"done": "<PERSON><PERSON><PERSON><PERSON>", "failed": "Fehlgeschlagen", "reverted": "Zurückgesetzt", "invoking": "Aufrufen", "compensating": "Kompensierend", "notStarted": "<PERSON>cht gestartet"}, "transaction": {"state": {"waitingToCompensate": "Ich warte auf eine Entschädigung"}}, "step": {"state": {"skipped": "Übersprungen", "skippedFailure": "Übersprungen (Fehler)", "dormant": "<PERSON><PERSON><PERSON>", "timeout": "Time-out"}}}, "productTypes": {"domain": "Produkttypen", "subtitle": "Organisieren Sie Ihre Produkte in Typen.", "create": {"header": "Produkttyp erstellen", "hint": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> einen neuen Produkttyp, um Ihre Produkte zu kategorisieren.", "successToast": "Der Produkttyp {{value}} wurde erfolgreich erstellt."}, "edit": {"header": "Produkttyp bearbeiten", "successToast": "Der Produkttyp {{value}} wurde erfolgreich aktualisiert."}, "delete": {"confirmation": "<PERSON>e sind dabei, den Produkttyp {{value}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "successToast": "Der Produkttyp {{value}} wurde erfolgreich gelöscht."}, "fields": {"value": "Wert"}}, "productTags": {"domain": "Produkt-Tags", "create": {"header": "Produkt-Tag erstellen", "subtitle": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein neues Produkt-Tag, um Ihre Produkte zu kategorisieren.", "successToast": "Das Produkt-Tag {{value}} wurde erfolgreich erstellt."}, "edit": {"header": "Produkt-Tag bearbeiten", "subtitle": "Bearbeiten Sie den Wert des Produkt-Tags.", "successToast": "Das Produkt-Tag {{value}} wurde erfolgreich aktualisiert."}, "delete": {"confirmation": "<PERSON><PERSON> sind dabei, das Produkt-Tag {{value}} zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "successToast": "Das Produkt-Tag {{value}} wurde erfolgreich gelöscht."}, "fields": {"value": "Wert"}}, "notifications": {"domain": "Benachrichtigungen", "emptyState": {"title": "<PERSON><PERSON>", "description": "Sie haben im Moment keine Benachrichtigungen, aber sobald Sie dies tun, werden sie hier angezeigt."}, "accessibility": {"description": "Benachrichtigungen über Medusa-Aktivitäten werden hier aufgelistet."}}, "errors": {"serverError": "Serverfehler – Versuchen Sie es später noch einmal.", "invalidCredentials": "Falsche E-Mail oder Passwort"}, "statuses": {"scheduled": "<PERSON><PERSON><PERSON>", "expired": "Abgelaufen", "active": "Aktiv", "enabled": "Aktiviert", "disabled": "Deaktiviert"}, "labels": {"productVariant": "Produktvariante", "prices": "<PERSON><PERSON>", "available": "Verfügbar", "inStock": "<PERSON><PERSON>", "added": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "removed": "ENTFERNT"}, "fields": {"amount": "<PERSON><PERSON>", "refundAmount": "Rückerstattungsbetrag", "name": "Name", "default": "Standard", "lastName": "Nachname", "firstName": "<PERSON><PERSON><PERSON>", "title": "Titel", "customTitle": "Benutzerdefinierter Titel", "manageInventory": "<PERSON><PERSON><PERSON> verwalten", "inventoryKit": "<PERSON><PERSON>", "inventoryItems": "Inventargegenstände", "inventoryItem": "Inventargegenstand", "requiredQuantity": "Benötigt<PERSON> Menge", "description": "Beschreibung", "email": "E-Mail", "password": "Passwort", "repeatPassword": "Passwort wiederholen", "confirmPassword": "Passwort bestätigen", "newPassword": "Neues Passwort", "repeatNewPassword": "Neues Passwort wiederholen", "categories": "<PERSON><PERSON><PERSON>", "shippingMethod": "<PERSON><PERSON><PERSON><PERSON>", "configurations": "Konfigurationen", "conditions": "Bedingungen", "category": "<PERSON><PERSON><PERSON>", "collection": "<PERSON><PERSON><PERSON>", "discountable": "Rabattierbar", "handle": "<PERSON><PERSON>", "subtitle": "Untertitel", "item": "Artikel", "qty": "<PERSON><PERSON>.", "limit": "Limit", "tags": "Schlagworte", "type": "<PERSON><PERSON>", "reason": "<PERSON><PERSON><PERSON>", "none": "keiner", "all": "alle", "search": "<PERSON><PERSON>", "percentage": "Prozentsatz", "sales_channels": "Vertriebskanäle", "customer_groups": "Kundengruppen", "product_tags": "Produkt-Tags", "product_types": "Produkttypen", "product_collections": "Produktkollektionen", "status": "Status", "code": "Code", "value": "Wert", "disabled": "Deaktiviert", "dynamic": "Dynamisch", "normal": "Normal", "years": "Jahre", "months": "Monate", "days": "Tage", "hours": "Std", "minutes": "Minuten", "totalRedemptions": "Gesamteinlösungen", "countries": "<PERSON><PERSON><PERSON>", "paymentProviders": "Zahlungsanbieter", "refundReason": "Rückerstattungsgrund", "fulfillmentProviders": "Fulfillment-Anbieter", "fulfillmentProvider": "Fulfillment-Anbieter", "providers": "<PERSON><PERSON><PERSON>", "availability": "Verfügbarkeit", "inventory": "Inventar", "optional": "Optional", "note": "Notiz", "automaticTaxes": "Automatische Steuern", "taxInclusivePricing": "Preise inklusive Steuern", "currency": "Währung", "address": "<PERSON><PERSON><PERSON>", "address2": "Apartment, Suite usw.", "city": "Stadt", "postalCode": "<PERSON><PERSON><PERSON><PERSON>", "country": "Land", "state": "Zustand", "province": "<PERSON><PERSON><PERSON>", "company": "Unternehmen", "phone": "Telefon", "metadata": "<PERSON><PERSON><PERSON>", "selectCountry": "Land auswählen", "products": "Produkte", "variants": "<PERSON><PERSON><PERSON>", "orders": "Bestellungen", "account": "Ko<PERSON>", "total": "Bestellsumme", "paidTotal": "Insgesamt erfasst", "totalExclTax": "Gesamt exkl. Steuer", "subtotal": "Zwischensumme", "shipping": "<PERSON>ers<PERSON>", "outboundShipping": "Ausgehender Versand", "returnShipping": "<PERSON><PERSON><PERSON><PERSON>", "tax": "<PERSON><PERSON><PERSON>", "created": "<PERSON><PERSON><PERSON><PERSON>", "key": "Schlüssel", "customer": "Kunde", "date": "Datum", "order": "Bestellung", "fulfillment": "Erfüllung", "provider": "<PERSON><PERSON><PERSON>", "payment": "Zahlung", "items": "Artikel", "salesChannel": "Vertriebskanal", "region": "Region", "discount": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON>", "sent": "Gesendet", "salesChannels": "Vertriebskanäle", "product": "Produkt", "createdAt": "<PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON>ktual<PERSON><PERSON>", "revokedAt": "Widerrufen am", "true": "<PERSON>a", "false": "<PERSON><PERSON>", "giftCard": "Geschenkkarte", "tag": "Etikett", "dateIssued": "Ausstellungsdatum", "issuedDate": "Ausstellungsdatum", "expiryDate": "Verfallsdatum", "price": "Pre<PERSON>", "priceTemplate": "Preis {{regionOrCurrency}}", "height": "<PERSON><PERSON><PERSON>", "width": "Breite", "length": "<PERSON><PERSON><PERSON>", "weight": "Gewicht", "midCode": "MID-Code", "hsCode": "HS-Code", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "Bestandsm<PERSON><PERSON>", "barcode": "Barcode", "countryOfOrigin": "Ursprungsland", "material": "Material", "thumbnail": "Miniaturansicht", "sku": "Artikelnummer", "managedInventory": "Verwalteter Bestand", "allowBackorder": "<PERSON><PERSON><PERSON>", "inStock": "<PERSON><PERSON>", "location": "<PERSON><PERSON>", "quantity": "<PERSON><PERSON>", "variant": "<PERSON><PERSON><PERSON>", "id": "ID", "parent": "Elternteil", "minSubtotal": "<PERSON><PERSON>", "maxSubtotal": "<PERSON><PERSON>", "shippingProfile": "Versandprofil", "summary": "Zusammenfassung", "details": "Einzelheiten", "label": "Etikett", "rate": "Rate", "requiresShipping": "<PERSON><PERSON><PERSON><PERSON>", "unitPrice": "Stückpreis", "startDate": "Startdatum", "endDate": "Enddatum", "draft": "<PERSON><PERSON><PERSON><PERSON>", "values": "<PERSON><PERSON>"}, "quotes": {"domain": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "subtitle": "Kundenangebote und Vorschläge verwalten", "noQuotes": "<PERSON><PERSON> gefunden", "noQuotesDescription": "<PERSON>s gibt derzeit keine Angebote. Erstellen Sie eines im Storefront.", "table": {"id": "Angebots-ID", "customer": "Kunde", "status": "Status", "company": "Unternehmen", "amount": "Betrag", "createdAt": "<PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON>ktual<PERSON><PERSON>", "actions": "Aktionen"}, "status": {"pending_merchant": "<PERSON><PERSON><PERSON><PERSON> ausstehend", "pending_customer": "<PERSON>nde auss<PERSON>hend", "merchant_rejected": "<PERSON><PERSON><PERSON><PERSON> a<PERSON><PERSON>", "customer_rejected": "Kunde abgelehnt", "accepted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unknown": "Unbekannt"}, "actions": {"sendQuote": "<PERSON><PERSON><PERSON> senden", "rejectQuote": "<PERSON><PERSON><PERSON>", "viewOrder": "Bestellung anzeigen"}, "details": {"header": "Angebotsdetails", "quoteSummary": "Angebotszusammenfassung", "customer": "Kunde", "company": "Unternehmen", "items": "Artikel", "total": "Gesamt", "subtotal": "Zwischensumme", "shipping": "<PERSON>ers<PERSON>", "tax": "<PERSON><PERSON><PERSON>", "discounts": "Rabatte", "originalTotal": "Ursprüngliche Summe", "quoteTotal": "Angebotssumme", "messages": "Nachrichten", "actions": "Aktionen", "sendMessage": "Nachricht senden", "send": "Senden", "pickQuoteItem": "Angebotsartikel auswählen", "selectQuoteItem": "<PERSON>ählen Sie einen Angebotsartikel zum Kommentieren", "selectItem": "Artikel auswählen", "manage": "<PERSON><PERSON><PERSON><PERSON>", "phone": "Telefon", "spendingLimit": "Ausgabenlimit", "name": "Name", "manageQuote": "<PERSON><PERSON><PERSON> verwalten", "noItems": "<PERSON><PERSON> in diesem Angebot", "noMessages": "<PERSON><PERSON> Nachrichten für dieses Angebot"}, "items": {"title": "Produkt", "quantity": "<PERSON><PERSON>", "unitPrice": "Stückpreis", "total": "Gesamt"}, "messages": {"admin": "Administrator", "customer": "Kunde", "placeholder": "G<PERSON><PERSON> Si<PERSON> hier Ihre Nachricht ein..."}, "filters": {"status": "Nach Status filtern"}, "confirmations": {"sendTitle": "<PERSON><PERSON><PERSON> senden", "sendDescription": "Sind <PERSON> sic<PERSON>, dass Si<PERSON> dieses Ang<PERSON> an den Kunden senden möchten?", "rejectTitle": "<PERSON><PERSON><PERSON>", "rejectDescription": "Sind <PERSON> sicher, dass Sie dieses Angebot able<PERSON>en möchten?"}, "acceptance": {"message": "Angebot wurde ak<PERSON><PERSON>t"}, "toasts": {"sendSuccess": "Angebot erfolgreich an Kunden gesendet", "sendError": "Fehler beim Senden des Angebots", "rejectSuccess": "Kundenangebot erfolgreich abgelehnt", "rejectError": "Fehler beim Ablehnen des Angebots", "messageSuccess": "Nachricht erfolgreich an Kunden gesendet", "messageError": "Fehler beim Senden der Nachricht", "updateSuccess": "Angebot erfolgreich aktualisiert"}, "manage": {"overridePriceHint": "Den ursprünglichen Preis für diesen Artikel überschreiben", "updatePrice": "Preis aktualisieren"}}, "companies": {"domain": "Unternehmen", "title": "Unternehmen", "subtitle": "Geschäftsbeziehungen verwalten", "noCompanies": "<PERSON><PERSON> gefunden", "noCompaniesDescription": "<PERSON><PERSON><PERSON>n Sie Ihr erstes Unternehmen, um zu beginnen.", "notFound": "Unternehmen nicht gefunden", "table": {"name": "Name", "phone": "Telefon", "email": "E-Mail", "address": "<PERSON><PERSON><PERSON>", "employees": "<PERSON><PERSON><PERSON><PERSON>", "customerGroup": "Kundengruppe", "actions": "Aktionen"}, "fields": {"name": "Unternehmensname", "email": "E-Mail", "phone": "Telefon", "website": "Website", "address": "<PERSON><PERSON><PERSON>", "city": "Stadt", "state": "Bundesland", "zip": "<PERSON><PERSON><PERSON><PERSON>", "country": "Land", "currency": "Währung", "logoUrl": "Logo-URL", "description": "Beschreibung", "employees": "<PERSON><PERSON><PERSON><PERSON>", "customerGroup": "Kundengruppe", "approvalSettings": "Genehmigungseinstellungen"}, "placeholders": {"name": "Unternehmensname eingeben", "email": "E-Mail-Ad<PERSON><PERSON>", "phone": "Telefonnummer eingeben", "website": "Website-URL eingeben", "address": "<PERSON><PERSON><PERSON> e<PERSON>", "city": "Stadt eingeben", "state": "Bundesland eingeben", "zip": "<PERSON><PERSON>itzahl eingeben", "logoUrl": "Logo-URL eingeben", "description": "Unternehmensbeschreibung eingeben", "selectCountry": "Land auswählen", "selectCurrency": "Währung auswählen"}, "validation": {"nameRequired": "Unternehmensname ist erforderlich", "emailRequired": "E-Mail ist erforderlich", "emailInvalid": "Ungültige E-Mail-Adresse", "addressRequired": "<PERSON>resse ist erforderlich", "cityRequired": "Stadt ist erforderlich", "stateRequired": "Bundesland ist erforderlich", "zipRequired": "Postleitzahl ist erforderlich"}, "create": {"title": "Unternehmen erstellen", "description": "Ein neues Unternehmen erstellen, um Geschäftsbeziehungen zu verwalten.", "submit": "Unternehmen erstellen"}, "edit": {"title": "Unternehmen bearbeiten", "submit": "Unternehmen aktualisieren"}, "details": {"actions": "Aktionen"}, "approvals": {"requiresAdminApproval": "<PERSON><PERSON><PERSON><PERSON>", "requiresSalesManagerApproval": "<PERSON><PERSON>ordert <PERSON>riebsleiter-Genehmigung", "noApprovalRequired": "<PERSON><PERSON> er<PERSON>"}, "approvalSettings": {"title": "Genehmigungseinstellungen", "requiresAdminApprovalDesc": "Bestellungen von diesem Unternehmen erfordern eine Admin-Genehmigung vor der Bearbeitung", "requiresSalesManagerApprovalDesc": "Bestellungen von diesem Unternehmen erfordern eine Vertriebsleiter-Genehmigung vor der Bearbeitung", "updateSuccess": "Genehmigungseinstellungen erfolgreich aktualisiert", "updateError": "Fehler beim Aktualisieren der Genehmigungseinstellungen"}, "customerGroup": {"name": "Kundengruppen-Name", "description": "Kundengruppen für dieses Unternehmen verwalten", "noGroups": "<PERSON>ine Kundengruppen verfügbar", "addSuccess": "Unternehmen erfolgreich zur Kundengruppe hinzugefügt", "addError": "Fehler beim Hinzufügen des Unternehmens zur Kundengruppe", "removeSuccess": "Unternehmen erfolgreich aus Kundengruppe entfernt", "removeError": "Fehler beim Entfernen des Unternehmens aus der Kundengruppe"}, "actions": {"edit": "Unternehmen bearbeiten", "editDetails": "Details bearbeiten", "manageCustomerGroup": "Kundengruppe verwalten", "approvalSettings": "Genehmigungseinstellungen", "delete": "Unternehmen löschen", "confirmDelete": "Löschen bestätigen"}, "delete": {"title": "Unternehmen löschen", "description": "Sind <PERSON> sicher, dass Sie dieses Unternehmen löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden."}, "employees": {"title": "<PERSON><PERSON><PERSON><PERSON>", "noEmployees": "<PERSON><PERSON> Mitarbeiter für dieses Unternehmen gefunden", "name": "Name", "email": "E-Mail", "phone": "Telefon", "role": "<PERSON><PERSON>", "spendingLimit": "Ausgabenlimit", "admin": "Administrator", "employee": "<PERSON><PERSON><PERSON><PERSON>", "add": "Mitarbeiter hinzufügen", "create": {"title": "Mitarbeiter erstellen", "success": "Mitarbeiter erfolgreich erstellt", "error": "Fehler beim Erstellen des Mitarbeiters"}, "form": {"details": "Detaillierte Informationen", "permissions": "Berechtigungen", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Nachname", "email": "E-Mail", "phone": "Telefon", "spendingLimit": "Ausgabenlimit", "adminAccess": "Admin-<PERSON><PERSON>riff", "isAdmin": "Ist Administrator", "isAdminDesc": "Diesem Mitarbeiter Administrator-<PERSON><PERSON>e gewähren", "isAdminTooltip": "Administratoren können Unternehmenseinstellungen und andere Mitarbeiter verwalten", "firstNamePlaceholder": "<PERSON><PERSON><PERSON> e<PERSON>ben", "lastNamePlaceholder": "Nachname e<PERSON>ben", "emailPlaceholder": "E-Mail-Ad<PERSON><PERSON>", "phonePlaceholder": "Telefonnummer eingeben", "spendingLimitPlaceholder": "Ausgabenlimit eingeben", "save": "Speichern", "saving": "Speichern..."}, "delete": {"confirmation": "Sind <PERSON> sicher, dass Sie diesen Mitarbeiter löschen möchten?", "success": "Mitarbeiter erfolgreich <PERSON>"}, "edit": {"title": "<PERSON><PERSON><PERSON><PERSON> bearbeiten"}, "toasts": {"updateSuccess": "Mitarbeiter erfolgreich aktualisiert", "updateError": "Fehler beim Aktualisieren des Mitarbeiters"}}, "toasts": {"createSuccess": "Unternehmen erfolgreich erstellt", "createError": "Fehler beim Erstellen des Unternehmens", "updateSuccess": "Unternehmen erfolgreich aktualisiert", "updateError": "Fehler beim Aktualisieren des Unternehmens", "deleteSuccess": "Unternehmen erfolgreich <PERSON>", "deleteError": "Fehler beim Löschen des Unternehmens"}}, "approvals": {"domain": "Genehmigungen", "title": "Genehmigungen", "subtitle": "Genehmigungsworkflows verwalten", "noApprovals": "<PERSON><PERSON>hmigungen gefunden", "noApprovalsDescription": "<PERSON>s gibt derzeit keine Genehmigungen zu überprüfen.", "table": {"id": "ID", "type": "<PERSON><PERSON>", "company": "Unternehmen", "customer": "Kunde", "amount": "Betrag", "status": "Status", "createdAt": "<PERSON><PERSON><PERSON><PERSON>"}, "status": {"pending": "<PERSON><PERSON><PERSON><PERSON>", "approved": "<PERSON><PERSON><PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expired": "Abgelaufen", "unknown": "Unbekannt"}, "details": {"header": "Genehmigungsdetails", "summary": "Genehmigungszusammenfassung", "company": "Unternehmen", "customer": "Kunde", "order": "Bestellung", "amount": "Betrag", "updatedAt": "<PERSON>ktual<PERSON><PERSON>", "reason": "<PERSON><PERSON><PERSON>", "actions": "Aktionen"}, "actions": {"approve": "<PERSON><PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON><PERSON><PERSON>", "confirmApprove": "Genehmigung bestätigen", "confirmReject": "Ablehnung bestätigen", "reasonPlaceholder": "<PERSON><PERSON><PERSON> (optional)..."}, "filters": {"status": "Nach Status filtern"}, "toasts": {"approveSuccess": "Erfolg<PERSON><PERSON>", "approveError": "Fehler bei der Genehmigung", "rejectSuccess": "Erfolgreich abgelehnt", "rejectError": "Fehler bei der Ablehnung"}}, "dateTime": {"years_one": "<PERSON><PERSON><PERSON>", "years_other": "Jahre", "months_one": "<PERSON><PERSON>", "months_other": "Monate", "weeks_one": "<PERSON><PERSON><PERSON>", "weeks_other": "<PERSON><PERSON><PERSON>", "days_one": "Tag", "days_other": "Tage", "hours_one": "Stunde", "hours_other": "Stunden", "minutes_one": "Minute", "minutes_other": "Minuten", "seconds_one": "Zweite", "seconds_other": "Sekunden"}}