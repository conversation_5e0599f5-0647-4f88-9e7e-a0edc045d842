{"$schema": "./$schema.json", "general": {"ascending": "Vzestupně", "descending": "Sestupně", "add": "<PERSON><PERSON><PERSON><PERSON>", "start": "Začátek", "end": "Konec", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "apply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "range": "<PERSON><PERSON><PERSON><PERSON>", "search": "Hledat", "of": "z", "results": "v<PERSON><PERSON>dky", "pages": "<PERSON><PERSON><PERSON><PERSON>", "next": "Dalš<PERSON>", "prev": "Předchozí", "is": "je", "timeline": "<PERSON><PERSON><PERSON> osa", "success": "Úspěch", "warning": "Varování", "tip": "Tip", "error": "Chyba", "select": "<PERSON><PERSON><PERSON><PERSON>", "selected": "Vybrán<PERSON>", "enabled": "Povoleno", "disabled": "Zak<PERSON><PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "active": "Aktivní", "revoked": "Odvoláno", "new": "Nový", "modified": "Upraveno", "added": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "removed": "Odstraněno", "admin": "Admin", "store": "<PERSON><PERSON><PERSON><PERSON>", "details": "Detaily", "items_one": "{{count}} <PERSON><PERSON><PERSON>", "items_other": "{{count}} <PERSON><PERSON><PERSON>", "countSelected": "{{count}} v<PERSON><PERSON><PERSON><PERSON><PERSON>", "countOfTotalSelected": "{{count}} z {{total}} v<PERSON><PERSON><PERSON><PERSON><PERSON>", "plusCount": "+ {{count}}", "plusCountMore": "+ {{count}} více", "areYouSure": "Jste si jistý?", "areYouSureDescription": "<PERSON><PERSON>t<PERSON><PERSON> se smazat {{entity}} {{title}}. <PERSON><PERSON> a<PERSON> ne<PERSON> vr<PERSON>t zpět.", "noRecordsFound": "Žádné z<PERSON>znamy nenalezeny", "typeToConfirm": "Prosím napi<PERSON> {val} pro potvrzení:", "noResultsTitle": "Žádné v<PERSON>dky", "noResultsMessage": "Zkuste změnit filtry nebo hledaný dotaz", "noSearchResults": "Žádné výsledky hledání", "noSearchResultsFor": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>ky hledání pro <0>'{{query}}'</0>", "noRecordsTitle": "<PERSON><PERSON><PERSON><PERSON>", "noRecordsMessage": "Nejsou žádné záznamy k zobrazení", "unsavedChangesTitle": "J<PERSON> si jist<PERSON>, že chcete opustit tento formulář?", "unsavedChangesDescription": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> budo<PERSON>, pokud opustíte tento formulář.", "includesTaxTooltip": "Ceny v tomto sloupci jsou včetně daně.", "excludesTaxTooltip": "Ceny v tomto sloupci jsou bez daně.", "noMoreData": "Žádná další data", "actions": "Ak<PERSON>"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}} k<PERSON><PERSON><PERSON>", "numberOfKeys_other": "{{count}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "drawer": {"header_one": "JSON <0>· {{count}} kl<PERSON><PERSON></0>", "header_other": "JSON <0>· {{count}} k<PERSON><PERSON><PERSON><PERSON></0>", "description": "Zobrazit JSON data pro tento objekt."}}, "metadata": {"header": "<PERSON><PERSON><PERSON>", "numberOfKeys_one": "{{count}} k<PERSON><PERSON><PERSON>", "numberOfKeys_other": "{{count}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": {"header": "<PERSON><PERSON><PERSON><PERSON>", "description": "Upravit metadata pro tento objekt.", "successToast": "Metadata byla úspěšně aktualizována.", "actions": {"insertRowAbove": "Vložit řádek nad", "insertRowBelow": "Vložit ř<PERSON>dek pod", "deleteRow": "<PERSON><PERSON><PERSON><PERSON>"}, "labels": {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "Hodnota"}, "complexRow": {"label": "<PERSON>ě<PERSON><PERSON><PERSON> jsou zakázány", "description": "Tento objekt obsahuje neprimární metadata, jako jsou pole nebo objekty, k<PERSON><PERSON> zde nelze upravit. Chcete-li upravit zak<PERSON><PERSON><PERSON>, použijte přímo API.", "tooltip": "<PERSON><PERSON> je <PERSON>, protože obsahuje neprimární data."}}}, "validation": {"mustBeInt": "Hodnota musí být celé <PERSON>lo.", "mustBePositive": "Hodnota musí být kladn<PERSON>."}, "actions": {"save": "Uložit", "saveAsDraft": "Uložit jako koncept", "copy": "Kopírovat", "copied": "Zkopírováno", "duplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "publish": "Publikovat", "create": "Vytvořit", "delete": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Odstranit", "revoke": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Zrušit", "forceConfirm": "Vynutit potvrzení", "continueEdit": "Pokračovat v úpravách", "enable": "Povolit", "disable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "undo": "<PERSON><PERSON><PERSON><PERSON>", "complete": "Dokončit", "viewDetails": "Zobrazit detaily", "back": "<PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showMore": "Zobrazit více", "continue": "Po<PERSON><PERSON><PERSON><PERSON>", "continueWithEmail": "Pokračovat s e-mailem", "idCopiedToClipboard": "ID zkopírováno do schránky", "addReason": "Přidat d<PERSON>", "addNote": "Přidat poznámku", "reset": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Potvrdit", "edit": "<PERSON><PERSON><PERSON><PERSON>", "addItems": "<PERSON><PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON><PERSON>", "clear": "Vymazat", "clearAll": "Vymazat vše", "apply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON>", "browse": "Proch<PERSON><PERSON>t", "logout": "Odhlásit se", "hide": "Skr<PERSON><PERSON>", "export": "Exportovat", "import": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cannotUndo": "<PERSON><PERSON> a<PERSON> nel<PERSON> vrátit zpět"}, "operators": {"in": "V"}, "app": {"search": {"label": "Hledat", "title": "Hledat", "description": "Prohledávejte celý svůj o<PERSON>, v<PERSON><PERSON><PERSON><PERSON> ob<PERSON>, produ<PERSON><PERSON>, <PERSON><PERSON>azn<PERSON>ů a dalších.", "allAreas": "Všechny oblasti", "navigation": "Navigace", "openResult": "Otevřít výsledek", "showMore": "Zobrazit více", "placeholder": "Přejít na nebo najít cokoliv...", "noResultsTitle": "Nebyly nalezeny žádné výsledky", "noResultsMessage": "<PERSON><PERSON><PERSON><PERSON> jsme nic, co by o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON>.", "emptySearchTitle": "Zadejte hledaný výraz", "emptySearchMessage": "Zadejte klíčové slovo nebo frázi k prozkoumání.", "loadMore": "<PERSON><PERSON><PERSON><PERSON> {{count}}", "groups": {"all": "Všechny oblasti", "customer": "Zákazn<PERSON><PERSON>", "customerGroup": "Skupiny zákazníků", "product": "Produkty", "productVariant": "Varianty produktů", "inventory": "Inventář", "reservation": "Rezervace", "category": "<PERSON><PERSON><PERSON>", "collection": "<PERSON><PERSON><PERSON><PERSON>", "order": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "promotion": "Propagace", "campaign": "Kampaně", "priceList": "Ceníky", "user": "Uživatelé", "region": "Regiony", "taxRegion": "Daňové regiony", "returnReason": "Důvody vrácení", "salesChannel": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "productType": "Typy produktů", "productTag": "Štítky produktů", "location": "<PERSON><PERSON>", "shippingProfile": "<PERSON><PERSON> dopravy", "publishableApiKey": "Publikovatelné API klíče", "secretApiKey": "Tajné <PERSON> klíče", "command": "Příkazy", "navigation": "Navigace"}}, "keyboardShortcuts": {"pageShortcut": "Přejít na", "settingShortcut": "Nastavení", "commandShortcut": "Příkazy", "then": "pak", "navigation": {"goToOrders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goToProducts": "Produkty", "goToCollections": "<PERSON><PERSON><PERSON><PERSON>", "goToCategories": "<PERSON><PERSON><PERSON>", "goToCustomers": "Zákazn<PERSON><PERSON>", "goToCustomerGroups": "Skupiny zákazníků", "goToInventory": "Inventář", "goToReservations": "Rezervace", "goToPriceLists": "Ceníky", "goToPromotions": "Propagace", "goToCampaigns": "Kampaně"}, "settings": {"goToSettings": "Nastavení", "goToStore": "<PERSON><PERSON><PERSON><PERSON>", "goToUsers": "Uživatelé", "goToRegions": "Regiony", "goToTaxRegions": "Daňové regiony", "goToSalesChannels": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "goToProductTypes": "Typy produktů", "goToLocations": "<PERSON><PERSON>", "goToPublishableApiKeys": "Publikovatelné API klíče", "goToSecretApiKeys": "Tajné <PERSON> klíče", "goToWorkflows": "Pracovní postupy", "goToProfile": "Profil", "goToReturnReasons": "Důvody vrácení"}}, "menus": {"user": {"documentation": "Dokumentace", "changelog": "Seznam změn", "shortcuts": "Zkratky", "profileSettings": "Nastavení profilu", "theme": {"label": "<PERSON><PERSON><PERSON>", "dark": "Tmavé", "light": "Světl<PERSON>", "system": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "store": {"label": "<PERSON><PERSON><PERSON><PERSON>", "storeSettings": "Nastavení obchodu"}, "actions": {"logout": "Odhlásit se"}}, "nav": {"accessibility": {"title": "Navigace", "description": "Navigační menu pro dashboard."}, "common": {"extensions": "Rozšíření"}, "main": {"store": "<PERSON><PERSON><PERSON><PERSON>", "storeSettings": "Nastavení obchodu"}, "settings": {"header": "Nastavení", "general": "Obecné", "developer": "Vývojář", "myAccount": "<PERSON><PERSON><PERSON>"}}}, "dataGrid": {"columns": {"view": "Zobrazit", "resetToDefault": "Obnovit na výchozí", "disabled": "Změna viditelných sloupců je zakázána."}, "shortcuts": {"label": "Zkratky", "commands": {"undo": "<PERSON><PERSON><PERSON><PERSON>", "redo": "Znovu", "copy": "Kopírovat", "paste": "Vložit", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "clear": "Vymazat", "moveUp": "<PERSON><PERSON><PERSON>", "moveDown": "<PERSON><PERSON><PERSON>", "moveLeft": "<PERSON><PERSON><PERSON>", "moveRight": "<PERSON><PERSON><PERSON>", "moveTop": "Posunout na začátek", "moveBottom": "Posunout na konec", "selectDown": "<PERSON><PERSON><PERSON><PERSON>", "selectUp": "<PERSON><PERSON><PERSON><PERSON>", "selectColumnDown": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> dol<PERSON>", "selectColumnUp": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>u", "focusToolbar": "Zaměřit na panel nástrojů", "focusCancel": "Zaměřit na zrušení"}}, "errors": {"fixError": "Opravit chybu", "count_one": "{{count}} chyba", "count_other": "{{count}} chyb"}}, "filters": {"sortLabel": "Řadit", "filterLabel": "Filtr", "searchLabel": "Hledat", "date": {"today": "Dnes", "lastSevenDays": "Posledních 7 dní", "lastThirtyDays": "Posledních 30 dní", "lastNinetyDays": "Posledních 90 dní", "lastTwelveMonths": "Posledních 12 měsíců", "custom": "Vlastní", "from": "Od", "to": "Do", "starting": "Začíná", "ending": "Končí"}, "compare": {"lessThan": "<PERSON><PERSON><PERSON>", "greaterThan": "<PERSON><PERSON><PERSON>", "exact": "Přesně", "range": "<PERSON><PERSON><PERSON><PERSON>", "lessThanLabel": "m<PERSON><PERSON> než {{value}}", "greaterThanLabel": "v<PERSON><PERSON> než {{value}}", "andLabel": "a"}, "sorting": {"alphabeticallyAsc": "A až Z", "alphabeticallyDesc": "Z až A", "dateAsc": "Nejnovější první", "dateDesc": "Nejstarší první"}, "radio": {"yes": "<PERSON><PERSON>", "no": "Ne", "true": "Pravda", "false": "Nepravda"}, "addFilter": "<PERSON><PERSON><PERSON><PERSON> filtr"}, "errorBoundary": {"badRequestTitle": "400 - Špatný požadavek", "badRequestMessage": "Požadavek nemohl být pochopen serverem kvůli špatné syntaxi.", "notFoundTitle": "404 - Na této adrese není <PERSON> s<PERSON>", "notFoundMessage": "Zkontrolujte URL a zkuste to znovu, nebo použijte vyhledávací lištu k nalezení toho, co hledáte.", "internalServerErrorTitle": "500 - <PERSON><PERSON><PERSON>", "internalServerErrorMessage": "Na serveru do<PERSON> k neočekávané chybě. Zkuste to prosím později.", "defaultTitle": "Došlo k chybě", "defaultMessage": "Při vykreslování této stránky došlo k neočekávané ch<PERSON>bě.", "noMatchMessage": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, neexist<PERSON><PERSON>.", "backToDashboard": "Zpět na dashboard"}, "addresses": {"title": "Ad<PERSON>y", "shippingAddress": {"header": "Doručovací adresa", "editHeader": "Upravit doručovací adresu", "editLabel": "Doručovací adresa", "label": "Doručovací adresa"}, "billingAddress": {"header": "Fakturační adresa", "editHeader": "Upravit fakturační adresu", "editLabel": "Fakturační adresa", "label": "Fakturační adresa", "sameAsShipping": "Stejná jako do<PERSON>í adresa"}, "contactHeading": "Kontakt", "locationHeading": "<PERSON><PERSON>"}, "email": {"editHeader": "Upravit e-mail", "editLabel": "E-mail", "label": "E-mail"}, "transferOwnership": {"header": "Převod vlastnictví", "label": "Převod vlastnictví", "details": {"order": "Detaily objedn<PERSON>vky", "draft": "Detaily konceptu"}, "currentOwner": {"label": "Současný vlastník", "hint": "Současný vlastník objednávky."}, "newOwner": {"label": "Nový vlastník", "hint": "Nový vlastník, na kterého se objednávka převede."}, "validation": {"mustBeDifferent": "Nový vlastník musí být odlišný od současného vlastníka.", "required": "Nový vlastník je povinný."}}, "sales_channels": {"availableIn": "Dostupné v <0>{{x}}</0> z <1>{{y}}</1> pro<PERSON><PERSON><PERSON><PERSON> ka<PERSON>"}, "products": {"domain": "Produkty", "list": {"noRecordsMessage": "Vytvořte svůj první produkt, abyste mohli za<PERSON><PERSON>t prodávat."}, "edit": {"header": "Upravit produkt", "description": "Upravit detaily produktu.", "successToast": "Produkt {{title}} byl <PERSON><PERSON>š<PERSON> a<PERSON>ual<PERSON>."}, "create": {"title": "Vytvořit produkt", "description": "Vytvořit nový produkt.", "header": "Obecné", "tabs": {"details": "Detaily", "organize": "Organizovat", "variants": "Varianty", "inventory": "<PERSON><PERSON>"}, "errors": {"variants": "Prosím vyberte alespoň jednu variantu.", "options": "Prosím vytvořte alespoň jednu možnost.", "uniqueSku": "SKU musí být jed<PERSON>."}, "inventory": {"heading": "<PERSON><PERSON>", "label": "Přidat inventární položky do sady inventáře varianty.", "itemPlaceholder": "Vybrat inventární položku", "quantityPlaceholder": "<PERSON><PERSON> z těchto položek je potřeba pro sadu?"}, "variants": {"header": "Varianty", "subHeadingTitle": "<PERSON><PERSON>, toto je produkt s variantami", "subHeadingDescription": "Když není <PERSON>, vytvoříme pro vás výchozí variantu", "optionTitle": {"placeholder": "Velikost"}, "optionValues": {"placeholder": "Malá, Střední, Velká"}, "productVariants": {"label": "Varianty produktů", "hint": "Toto pořadí ovlivní pořadí variant ve vašem obchodě.", "alert": "Přidejte možnosti pro vytvoření variant.", "tip": "<PERSON><PERSON><PERSON>, které nej<PERSON>, nebudou vytvořeny. Můžete vždy vytvořit a upravit varianty později, ale tento seznam odpovídá variacím ve vašich produktových možnostech."}, "productOptions": {"label": "Možnosti produktů", "hint": "Definujte možnosti pro produkt, např. barvu, velikost atd."}}, "successToast": "Produkt {{title}} byl <PERSON><PERSON>."}, "export": {"header": "Exportovat seznam produktů", "description": "Exportovat seznam produktů do souboru CSV.", "success": {"title": "Zpracováváme váš export", "description": "Export dat může trvat několik minut. Oznámíme vám, a<PERSON> budeme hotovi."}, "filters": {"title": "Filtry", "description": "Použijte filtry v přehledu tabulky pro úpravu tohoto zobrazení"}, "columns": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Přizpůsobte exportovaná data tak, aby splňovala specifické <PERSON>ř<PERSON>y"}}, "import": {"header": "Importovat seznam produktů", "uploadLabel": "Importovat produkty", "uploadHint": "Přetáhněte soubor CSV nebo klikněte pro nahrání", "description": "Importujte produkty poskytnutím souboru CSV v předdefinovaném formátu", "template": {"title": "<PERSON><PERSON><PERSON> si jisti, jak uspořádat svůj seznam?", "description": "Stáhněte si níže uvedenou šablonu, abyste se ujistili, že dodržujete správný formá<PERSON>."}, "upload": {"title": "<PERSON><PERSON><PERSON><PERSON> soubor CSV", "description": "Prostřednictvím importů můžete přidávat nebo aktualizovat produkty. Pro aktualizaci existujících produktů musíte použít existující handle a ID, pro aktualizaci existujících variant musíte použít existující ID. Před importem produktů budete požádáni o potvrzení.", "preprocessing": "Předzpracování...", "productsToCreate": "Produkty budou vytvořeny", "productsToUpdate": "Produkty budou aktualizovány"}, "success": {"title": "Zpracováváme váš import", "description": "Import dat může chvíli trvat. Oznámíme vám, a<PERSON> budeme hotovi."}}, "deleteWarning": "<PERSON><PERSON>t<PERSON><PERSON> se smazat produkt {{title}}. <PERSON><PERSON> a<PERSON> ne<PERSON> vrátit zpět.", "variants": {"header": "Varianty", "empty": {"heading": "Žádné varianty", "description": "Nejsou žádné varianty k zobrazení."}, "filtered": {"heading": "Žádné v<PERSON>dky", "description": "Žádné varianty neodpovídají aktuálním kritériím filtru."}}, "attributes": "Atributy", "editAttributes": "Upravit atributy", "editOptions": "Upravit možnosti", "editPrices": "<PERSON><PERSON><PERSON><PERSON> ceny", "media": {"label": "Média", "editHint": "Přidejte média k produktu, aby se zobrazila ve vašem obchodě.", "makeThumbnail": "Vytvořit miniaturu", "uploadImagesLabel": "<PERSON><PERSON><PERSON><PERSON>", "uploadImagesHint": "Přetáhněte obrázky sem nebo klikněte pro nahrání.", "invalidFileType": "'{{name}}' ne<PERSON><PERSON>ný typ souboru. Podporované typy souborů jsou: {{types}}.", "failedToUpload": "Nepodařilo se nahrát přidaná média. Zkuste to prosím znovu.", "deleteWarning_one": "Chystá<PERSON> se smazat {{count}} obrázek. <PERSON><PERSON> a<PERSON> ne<PERSON>ze vrátit zpět.", "deleteWarning_other": "Ch<PERSON>tá<PERSON> se smazat {{count}} obr<PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON> a<PERSON> nelze vrátit zpět.", "deleteWarningWithThumbnail_one": "Chystáte se smazat {{count}} obrázek včetně miniatury. Tuto akci nelze vrátit zpět.", "deleteWarningWithThumbnail_other": "Chystáte se smazat {{count}} obrázků včetně miniatury. Tuto akci nelze vrátit zpět.", "thumbnailTooltip": "Miniatura", "galleryLabel": "Galerie", "downloadImageLabel": "Stáhnout aktuální obrázek", "deleteImageLabel": "Smazat aktuální obrázek", "emptyState": {"header": "Zatím žádná média", "description": "Přidejte média k produktu, aby se zobrazila ve vašem obchodě.", "action": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>"}, "successToast": "Média byla úspěšně aktualizována."}, "discountableHint": "<PERSON><PERSON><PERSON> není <PERSON>, slevy nebudou na tento produkt aplikovány.", "noSalesChannels": "Není dostupné v žádných prodejních kanálech", "variantCount_one": "{{count}} varianta", "variantCount_other": "{{count}} variant", "deleteVariantWarning": "<PERSON><PERSON>t<PERSON><PERSON> se smazat variantu {{title}}. <PERSON><PERSON> a<PERSON> ne<PERSON> vrátit zpět.", "productStatus": {"draft": "Koncept", "published": "Publikováno", "proposed": "Navrženo", "rejected": "Odmí<PERSON>nut<PERSON>"}, "fields": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Dejte svému produktu krátký a jasný název.<0/>50-60 znaků je doporučená délka pro vyhledávače.", "placeholder": "Zimní bunda"}, "subtitle": {"label": "Podtitul", "placeholder": "Teplé a útulné"}, "handle": {"label": "<PERSON><PERSON>", "tooltip": "Handle se používá k odkazování na produkt ve vašem obchodě. Pokud nen<PERSON> specif<PERSON>, handle bude vygenerováno z názvu produktu.", "placeholder": "zimní-bunda"}, "description": {"label": "<PERSON><PERSON>", "hint": "Dejte svému produktu krátký a jasný popis.<0/>120-160 znaků je doporučená délka pro vyhledávače.", "placeholder": "Teplá a útulná bunda"}, "discountable": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON>ž není <PERSON>, slevy nebudou na tento produkt aplikovány"}, "shipping_profile": {"label": "<PERSON>il dopra<PERSON>", "hint": "Připojte produkt k profilu dopravy"}, "type": {"label": "<PERSON><PERSON>"}, "collection": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "categories": {"label": "<PERSON><PERSON><PERSON>"}, "tags": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sales_channels": {"label": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "hint": "Tento produkt bude dostupný pouze v výchozím prodejním kanálu, pokud zůstane nedotčen."}, "countryOrigin": {"label": "Země <PERSON>"}, "material": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "width": {"label": "Šířka"}, "length": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "height": {"label": "Výška"}, "weight": {"label": "Hmotnost"}, "options": {"label": "Možnosti produktů", "hint": "Možnosti se používají k definování barvy, velikosti atd. produktu", "add": "Přidat možnost", "optionTitle": "Název možnosti", "optionTitlePlaceholder": "<PERSON><PERSON>", "variations": "V<PERSON><PERSON> (odd<PERSON><PERSON>é <PERSON>)", "variantionsPlaceholder": "Červená, Modrá, Zelená"}, "variants": {"label": "Varianty produktů", "hint": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON> ne<PERSON>, nebudou vytvoř<PERSON>y. Toto poř<PERSON>í ovlivní, jak jsou varianty řazeny ve vašem frontend."}, "mid_code": {"label": "Mid kód"}, "hs_code": {"label": "HS kód"}}, "variant": {"edit": {"header": "Upravit variantu", "success": "Varianta produktu byla úspěšně upravena"}, "create": {"header": "Detaily varianty"}, "deleteWarning": "Jste si jisti, že chcete smazat tuto variantu?", "pricesPagination": "1 - {{current}} z {{total}} cen", "tableItemAvailable": "{{availableCount}} dostupné", "tableItem_one": "{{availableCount}} dostupné na {{locationCount}} lokaci", "tableItem_other": "{{availableCount}} dostupné na {{locationCount}} lokacích", "inventory": {"notManaged": "Nespravováno", "manageItems": "Spravovat inventární položky", "notManagedDesc": "Inventář není spravován pro tuto variantu. Zapněte 'Spravovat inventář' pro sledování inventáře varianty.", "manageKit": "Spravovat sadu inventáře", "navigateToItem": "Přejít na inventární položku", "actions": {"inventoryItems": "Přejít na inventární položku", "inventoryKit": "Zobrazit inventární položky"}, "inventoryKit": "<PERSON><PERSON>", "inventoryKitHint": "Skládá se tato varianta z několika inventárních položek?", "validation": {"itemId": "Prosím vyberte inventární položku.", "quantity": "Množství je povinné. Prosím zadejte kladné <PERSON>."}, "header": "Sklad & Inventář", "editItemDetails": "Upravit detaily <PERSON>ky", "manageInventoryLabel": "Spravovat inventář", "manageInventoryHint": "<PERSON><PERSON><PERSON> je povoleno, změníme množství inventáře za vás, k<PERSON><PERSON> jsou vytvořeny objednávky a vrácení.", "allowBackordersLabel": "Povolit zpětné objednávky", "allowBackordersHint": "<PERSON><PERSON><PERSON> je povoleno, zákazníci mohou zak<PERSON>pit variantu, i k<PERSON>ž není dostupné množství.", "toast": {"levelsBatch": "Úrovn<PERSON> inventáře byly aktualizovány.", "update": "Inventární položka byla úspěšně aktualizována.", "updateLevel": "Úroveň inventáře byla úspěšně aktualizována.", "itemsManageSuccess": "Inventární <PERSON> byly úspěšně aktualizovány."}}}, "options": {"header": "<PERSON><PERSON><PERSON><PERSON>", "edit": {"header": "Upravit možnost", "successToast": "Možnost {{title}} byla úspěšně aktualizována."}, "create": {"header": "Vytvořit možnost", "successToast": "Možnost {{title}} by<PERSON> úsp<PERSON>šně vytvořena."}, "deleteWarning": "Chystáte se smazat možnost produktu: {{title}}. <PERSON><PERSON> a<PERSON> ne<PERSON>ze vrátit zpět."}, "organization": {"header": "Organizovat", "edit": {"header": "Upravit organizaci", "toasts": {"success": "Organizace {{title}} byla úsp<PERSON>šně aktualizována."}}}, "stock": {"heading": "Spravovat úrovně zásob produktů a lokace", "description": "Aktualizujte úrovně zásob pro všechny varianty produktu.", "loading": "<PERSON><PERSON><PERSON><PERSON><PERSON>, může to chvíli trvat...", "tooltips": {"alreadyManaged": "<PERSON><PERSON> inventární položka je již upravitelná pod {{title}}.", "alreadyManagedWithSku": "<PERSON><PERSON> inventární položka je již upravitelná pod {{title}} ({{sku}})."}}, "shippingProfile": {"header": "<PERSON>n<PERSON><PERSON><PERSON> dopravy", "edit": {"header": "<PERSON>n<PERSON><PERSON><PERSON> dopravy", "toasts": {"success": "Profil dopravy pro {{title}} byl úsp<PERSON>šn<PERSON> aktualiz<PERSON>."}}, "create": {"errors": {"required": "<PERSON>il dopravy je povinný"}}}, "toasts": {"delete": {"success": {"header": "Produkt byl s<PERSON>", "description": "{{title}} by<PERSON> <PERSON><PERSON>."}, "error": {"header": "Nepodařilo se smazat produkt"}}}}, "collections": {"domain": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Organizujte produkty do kolekcí.", "createCollection": "Vytvořit kolekci", "createCollectionHint": "Vytvořte novou kolekci pro organizaci vašich produktů.", "createSuccess": "<PERSON><PERSON><PERSON><PERSON> byla úspěšně vytvořena.", "editCollection": "<PERSON><PERSON><PERSON><PERSON>", "handleTooltip": "Handle se používá k odkazování na kolekci ve vašem obchodě. Pokud nen<PERSON> specif<PERSON>, handle bude vygenerováno z názvu kolekce.", "deleteWarning": "<PERSON><PERSON>t<PERSON><PERSON> se smazat kole<PERSON> {{title}}. <PERSON><PERSON> a<PERSON> ne<PERSON>ze vrátit zpět.", "removeSingleProductWarning": "Chystáte se odstranit produkt {{title}} z kolekce. <PERSON>to akci nelze vrátit zpět.", "removeProductsWarning_one": "Chystáte se odstranit {{count}} produkt z kolekce. <PERSON>to akci nelze vrátit zpět.", "removeProductsWarning_other": "Chystáte se odstranit {{count}} produkt<PERSON> z kolekce. <PERSON>to akci nelze vrátit zpět.", "products": {"list": {"noRecordsMessage": "V kolekci nejsou žádné produkty."}, "add": {"successToast_one": "Produkt byl úspěšně přidán do kolekce.", "successToast_other": "Produkty byly úspěšně přidány do kolekce."}, "remove": {"successToast_one": "Produkt byl úspěšně odstraněn z kolekce.", "successToast_other": "Produkty byly úspěšně odstraněny z kolekce."}}}, "categories": {"domain": "<PERSON><PERSON><PERSON>", "subtitle": "Organizujte produkty do kategorií a spravujte jejich pořadí a hierarchii.", "create": {"header": "Vytvořit kategorii", "hint": "Vytvořte novou kategorii pro organizaci vašich produktů.", "tabs": {"details": "Detaily", "organize": "Organizovat pořadí"}, "successToast": "<PERSON><PERSON><PERSON> {{name}} by<PERSON>."}, "edit": {"header": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "description": "Upravit kategorii pro aktualizaci jej<PERSON>ch <PERSON>.", "successToast": "Kate<PERSON><PERSON> byla úspěšně aktualizována."}, "delete": {"confirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON> se smazat kate<PERSON>ii {{name}}. <PERSON><PERSON> a<PERSON> ne<PERSON> vrátit zpět.", "successToast": "<PERSON><PERSON><PERSON> {{name}} by<PERSON>."}, "products": {"add": {"disabledTooltip": "Produkt je již v této kategorii.", "successToast_one": "<PERSON><PERSON><PERSON><PERSON> {{count}} produkt do kategorie.", "successToast_other": "Přidáno {{count}} produktů do kategorie."}, "remove": {"confirmation_one": "Chystáte se odstranit {{count}} produkt z kategorie. <PERSON>to akci nelze vrátit zpět.", "confirmation_other": "Chystáte se odstranit {{count}} produkt<PERSON> z kategorie. <PERSON>to akci nelze vrátit zpět.", "successToast_one": "Odstraněn {{count}} produkt z kategorie.", "successToast_other": "Odstraněno {{count}} produkt<PERSON> z kategorie."}, "list": {"noRecordsMessage": "V kategorii nejsou žádné produkty."}}, "organize": {"header": "Organizovat", "action": "Upravit pořadí"}, "fields": {"visibility": {"label": "Viditelnost", "internal": "Interní", "public": "Veřejné"}, "status": {"label": "Stav", "active": "Aktivní", "inactive": "Neaktivní"}, "path": {"label": "Cesta", "tooltip": "Zobrazit plnou cestu kategorie."}, "children": {"label": "<PERSON><PERSON><PERSON>"}, "new": {"label": "Nový"}}}, "inventory": {"domain": "Inventář", "subtitle": "Spravujte své inventární položky", "reserved": "Rezervován<PERSON>", "available": "Dostupné", "locationLevels": "<PERSON><PERSON>", "associatedVariants": "Přidružené varianty", "manageLocations": "Spravovat lokace", "manageLocationQuantity": "Spravovat množství na lokaci", "deleteWarning": "Chystáte se smazat inventární položku. <PERSON><PERSON> a<PERSON> nelze vrátit zpět.", "editItemDetails": "Upravit detaily <PERSON>ky", "quantityAcrossLocations": "{{quantity}} na {{locations}} loka<PERSON><PERSON>ch", "create": {"title": "Vytvořit inventární položku", "details": "Detaily", "availability": "Dostupnost", "locations": "<PERSON><PERSON>", "attributes": "Atributy", "requiresShipping": "Vyžaduje dopravu", "requiresShippingHint": "Vyžaduje inventární položka dopravu?", "successToast": "Inventární položka byla úspěšně vytvořena."}, "reservation": {"header": "Rezervace {{itemName}}", "editItemDetails": "Upravit rezer<PERSON>", "lineItemId": "ID položky", "orderID": "ID objednávky", "description": "<PERSON><PERSON>", "location": "<PERSON><PERSON>", "inStockAtLocation": "Na skladě na této lokaci", "availableAtLocation": "Dostupné na této lokaci", "reservedAtLocation": "Rezervováno na této lokaci", "reservedAmount": "Rezervované množství", "create": "Vytvořit rezervaci", "itemToReserve": "Položka k rezervaci", "quantityPlaceholder": "Kolik chcete rezervovat?", "descriptionPlaceholder": "<PERSON><PERSON><PERSON> typ rezervace to je?", "successToast": "Rezervace byla úspěšně vytvořena.", "updateSuccessToast": "Rezervace byla úspěšně aktualizována.", "deleteSuccessToast": "Rezerva<PERSON> byla <PERSON>š<PERSON> s<PERSON>.", "errors": {"noAvaliableQuantity": "Skladová lokace nemá dostupné množství.", "quantityOutOfRange": "Minimální množství je 1 a maximální množství je {{max}}"}}, "adjustInventory": {"errors": {"stockedQuantity": "Skladové množství nelze aktualizovat na méně než rezervované množství {{quantity}}."}}, "toast": {"updateLocations": "Lokace byly <PERSON>šně aktualizovány.", "updateLevel": "Úroveň inventáře byla úspěšně aktualizována.", "updateItem": "Inventární položka byla úspěšně aktualizována."}, "stock": {"title": "Aktualizovat úrov<PERSON>ě inventáře", "description": "Aktualizujte skladové úrovně pro vybrané inventární položky.", "action": "Upravit <PERSON>", "placeholder": "<PERSON><PERSON><PERSON>o", "disablePrompt_one": "Chystáte se zakázat {{count}} úroveň lokace. <PERSON>to akci nelze vrátit zpět.", "disablePrompt_other": "Chystáte se zak<PERSON>zat {{count}} úrovní lokace. <PERSON>to akci nelze vrátit zpět.", "disabledToggleTooltip": "Nelze zakázat: vymažte příchozí a/nebo rezervované množství před zakázáním.", "successToast": "Úrovn<PERSON> in<PERSON> byly úspěšně aktualizovány."}}, "giftCards": {"domain": "Dárkov<PERSON> karty", "editGiftCard": "Upravit d<PERSON>rkovou kartu", "createGiftCard": "Vytvořit dárkovou kartu", "createGiftCardHint": "R<PERSON>č<PERSON>ě vytvořte dárkovou kartu, kterou lze p<PERSON>žít jako platební metodu ve vašem obchodě.", "selectRegionFirst": "Nejprve vyberte region", "deleteGiftCardWarning": "Chystá<PERSON> se smazat dárkovou kartu {{code}}. <PERSON><PERSON> a<PERSON> ne<PERSON>ze vrátit zpět.", "balanceHigherThanValue": "Zůstatek nemůže být vyšší než původní částka.", "balanceLowerThanZero": "Zůstatek nemůže být záporný.", "expiryDateHint": "Země mají růz<PERSON>é z<PERSON>ony týkající se data vypršení platnosti dárkových karet. Ujistěte se, že jste zkontrolovali místní předpisy před nastavením data vypršení platnosti.", "regionHint": "Změna regionu dárkové karty také změní jej<PERSON> mě<PERSON>, což může ovlivnit její pen<PERSON>žní hodnotu.", "enabledHint": "<PERSON><PERSON><PERSON><PERSON>, zda je dá<PERSON> karta povolena nebo zakázána.", "balance": "Zůstate<PERSON>", "currentBalance": "Aktuální zůstatek", "initialBalance": "Počáteční zůstatek", "personalMessage": "Osobní zpráva", "recipient": "Příjemce"}, "customers": {"domain": "Zákazn<PERSON><PERSON>", "list": {"noRecordsMessage": "Vaši zákazníci se zde zobrazí."}, "create": {"header": "Vytvořit zákazníka", "hint": "Vytvořte nového zákazníka a spravujte jeho detaily.", "successToast": "Zákazník {{email}} byl <PERSON><PERSON>š<PERSON> v<PERSON>."}, "groups": {"label": "Skupiny zákazníků", "remove": "J<PERSON> si jisti, že chcete odstranit zákazníka ze skupiny zákazníků \"{{name}}\"?", "removeMany": "J<PERSON> si jisti, že chcete zákazníka odstranit z následujících skupin zákazníků: {{groups}}?", "alreadyAddedTooltip": "Zákazník je již v této skupině zákazníků.", "list": {"noRecordsMessage": "Tento zákazník nepatří do žádné skupiny."}, "add": {"success": "Zákazník přidán do: {{groups}}.", "list": {"noRecordsMessage": "Nejprve vytvořte skupinu zákazníků."}}, "removed": {"success": "Zákazník odstraněn z: {{groups}}.", "list": {"noRecordsMessage": "Nejprve vytvořte skupinu zákazníků."}}}, "edit": {"header": "Upravit zákazníka", "emailDisabledTooltip": "E-mailová adresa nemůže být změněna pro registrované zákazníky.", "successToast": "Zákazník {{email}} byl úspěšně aktualiz<PERSON>."}, "delete": {"title": "Smazat zákazníka", "description": "Chystáte se smazat zákazníka {{email}}. <PERSON><PERSON> a<PERSON> ne<PERSON> vrátit zpět.", "successToast": "Zákazník {{email}} by<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>."}, "fields": {"guest": "Host", "registered": "Registrovaný", "groups": "Skupiny"}, "registered": "Registrovaný", "guest": "Host", "hasAccount": "<PERSON><PERSON>", "addresses": {"title": "Ad<PERSON>y", "fields": {"addressName": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON>", "address1": "Adresa 1", "address2": "Adresa 2", "city": "<PERSON><PERSON><PERSON>", "province": "<PERSON><PERSON><PERSON>", "postalCode": "PSČ", "country": "Země", "phone": "Telefon", "company": "Společnost", "countryCode": "<PERSON><PERSON><PERSON>", "provinceCode": "<PERSON><PERSON><PERSON>"}, "create": {"header": "Nová adresa", "hint": "Vytvořte novou adresu pro zákazníka.", "successToast": "<PERSON><PERSON><PERSON>la úspěšně vytvořena."}}}, "customerGroups": {"domain": "Skupiny zákazníků", "subtitle": "Organizujte zákazníky do skupin. Skupiny mohou mít různé propagace a ceny.", "list": {"empty": {"heading": "Žádné skupiny zákazníků", "description": "Nejsou žádné skupiny zákazníků k zobrazení."}, "filtered": {"heading": "Žádné v<PERSON>dky", "description": "Žádné skupiny zákazníků neodpovídají aktuálním kritériím filtru."}}, "create": {"header": "Vytvořit skupinu zákazníků", "hint": "Vytvořte novou skupinu zákazníků pro segmentaci vašich zákazníků.", "successToast": "<PERSON><PERSON><PERSON> z<PERSON> {{name}} by<PERSON> v<PERSON>vo<PERSON>."}, "edit": {"header": "Upravit skupinu zákazníků", "successToast": "<PERSON><PERSON><PERSON> {{name}} by<PERSON>š<PERSON>ě aktualizová<PERSON>."}, "delete": {"title": "S<PERSON>zat skupinu z<PERSON>azníků", "description": "Chystáte se smazat skupinu zákazníků {{name}}. <PERSON><PERSON> akci nelze vrátit zpět.", "successToast": "<PERSON><PERSON><PERSON> {{name}} by<PERSON>."}, "customers": {"alreadyAddedTooltip": "Zákazník již byl přidán do skupiny.", "add": {"successToast_one": "Zákazník byl úspěšně přidán do skupiny.", "successToast_other": "Zákazníci byli úspěšně přidáni do skupiny.", "list": {"noRecordsMessage": "Nejprve vytvořte zákazníka."}}, "remove": {"title_one": "Odstranit zákazníka", "title_other": "Odstranit zákazníky", "description_one": "Chystáte se odstranit {{count}} zákazníka ze skupiny zákazníků. <PERSON><PERSON> ak<PERSON> nelze vrátit zpět.", "description_other": "Chystáte se odstranit {{count}} zákazníků ze skupiny zákazníků. <PERSON>to akci nelze vrátit zpět."}, "list": {"noRecordsMessage": "<PERSON><PERSON> sku<PERSON> nemá zákazníky."}}}, "orders": {"giftCardsStoreCreditLines": "Dárkové karty a kredity", "creditLines": {"title": "<PERSON><PERSON><PERSON>", "total": "Součet všech kreditů", "creditOrDebit": "Kredit / Debet", "createCreditLine": "Vytvořit kredit", "createCreditLineSuccess": "Kredit byl úspěšně vytvořen", "createCreditLineError": "Chyba při vytváření kreditu", "createCreditLineDescription": "Vytvořte kredit na částku {{amount}}", "operation": "Operace", "credit": "Kredit", "creditDescription": "Přidá kladnou částku k objednávce", "debit": "Debet", "debitDescription": "Odečte zápornou částku z objednávky"}, "balanceSettlement": {"title": "Vyrovnání zůstatku", "settlementType": "Typ vyrovnání", "settlementTypes": {"paymentMethod": "Platební metoda", "paymentMethodDescription": "Vrátit částku na platební metodu", "creditLine": "Kredit v obchodě", "creditLineDescription": "Vrátit částku jako kredit v obchodě"}}, "domain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "claim": "Reklamace", "exchange": "Výměna", "return": "Vrácení", "cancelWarning": "Chystáte se zrušit objednávku {{id}}. <PERSON><PERSON> a<PERSON> nelze vrátit zpět.", "orderCanceled": "Objednávka byla úspěšně zrušena", "onDateFromSalesChannel": "{{date}} z {{salesChannel}}", "list": {"noRecordsMessage": "Vaše objednávky se zde zobrazí."}, "status": {"not_paid": "Nezaplaceno", "pending": "Čeká se", "completed": "Dokončeno", "draft": "Koncept", "archived": "Archivováno", "canceled": "<PERSON><PERSON><PERSON><PERSON>", "requires_action": "Vyžaduje <PERSON>"}, "summary": {"requestReturn": "Požádat o vrácení", "allocateItems": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editOrder": "Upravit objednávku", "editOrderContinue": "Pokračovat v úpravě objednávky", "inventoryKit": "Skládá se z {{count}}x in<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemTotal": "<PERSON><PERSON><PERSON> cena <PERSON>", "shippingTotal": "<PERSON><PERSON><PERSON> cena dopravy", "discountTotal": "<PERSON><PERSON><PERSON> sleva", "taxTotalIncl": "<PERSON><PERSON><PERSON> (včetně)", "itemSubtotal": "Mezisoučet položek", "shippingSubtotal": "Mezisoučet dopravy", "discountSubtotal": "Mezisoučet slevy", "taxTotal": "<PERSON><PERSON><PERSON>"}, "transfer": {"title": "Převod vlastnictví", "requestSuccess": "Žádost o převod objednávky byla odeslána na: {{email}}.", "currentOwner": "Současný vlastník", "newOwner": "Nový vlastník", "currentOwnerDescription": "Zákazník aktuálně spojený s touto objednávkou.", "newOwnerDescription": "Zákazník, na kterého se objednávka převede."}, "payment": {"title": "<PERSON><PERSON><PERSON>", "isReadyToBeCaptured": "<PERSON><PERSON><PERSON> <0/> je připravena k zachycení.", "totalPaidByCustomer": "Celková částka zaplacená zákazníkem", "totalStoreCreditRefunds": "Celkový kredit vrácen v obchodě", "capture": "Zachytit platbu", "capture_short": "Zachytit", "refund": "Vrácení <PERSON>", "markAsPaid": "Označit jako <PERSON>", "statusLabel": "<PERSON>av platby", "statusTitle": "<PERSON>av platby", "status": {"notPaid": "Nezaplaceno", "authorized": "<PERSON><PERSON><PERSON><PERSON>", "partiallyAuthorized": "Částečně autorizováno", "awaiting": "Čeká se", "captured": "Zachyceno", "partiallyRefunded": "Částečně vráceno", "partiallyCaptured": "Částečně z<PERSON>no", "refunded": "Vráceno", "canceled": "<PERSON><PERSON><PERSON><PERSON>", "requiresAction": "Vyžaduje <PERSON>"}, "capturePayment": "<PERSON>lat<PERSON> ve výši {{amount}} bude zachy<PERSON>na.", "capturePaymentSuccess": "Platba ve výši {{amount}} by<PERSON> úspěšně z<PERSON>", "markAsPaidPayment": "Platba ve výši {{amount}} bude označena jako z<PERSON>.", "markAsPaidPaymentSuccess": "Platba ve výši {{amount}} by<PERSON> úspěšně <PERSON> jako z<PERSON>", "createRefund": "Vytvořit vrácení peněz", "refundPaymentSuccess": "Vrácení peněz ve výši {{amount}} by<PERSON>", "createRefundWrongQuantity": "Množství by m<PERSON><PERSON> b<PERSON><PERSON> mezi 1 a {{number}}", "refundAmount": "V<PERSON><PERSON><PERSON><PERSON> {{ amount }}", "paymentLink": "Zkopírovat odkaz na platbu pro {{ amount }}", "selectPaymentToRefund": "Vyberte platbu k vrácení"}, "edits": {"title": "Upravit objednávku", "confirm": "Potvrdit úpravu", "confirmText": "Chystáte se potvrdit úpravu objednávky. <PERSON><PERSON> a<PERSON> ne<PERSON> vrátit zpět.", "cancel": "Zrušit <PERSON>", "currentItems": "Aktuální <PERSON>žky", "currentItemsDescription": "Upravit množství položky nebo odstranit.", "addItemsDescription": "Můžete přidat nové položky do objednávky.", "addItems": "<PERSON><PERSON><PERSON><PERSON>", "amountPaid": "Zaplacená částka", "newTotal": "Nový celkový součet", "differenceDue": "Rozdíl k <PERSON>hradě", "create": "Upravit objednávku", "currentTotal": "Aktuální celkový součet", "noteHint": "Přidat interní poznámku k úpravě", "cancelSuccessToast": "Úprava objednávky byla zrušena", "createSuccessToast": "Žádost o úpravu objednávky byla vytvořena", "activeChangeError": "Na objednávce je již aktivní změna objednávky (vrácení, reklamace, výměna atd.). Dokončete nebo zrušte změnu před úpravou objednávky.", "panel": {"title": "Žádost o úpravu objednávky", "titlePending": "Čeká se na úpravu objednávky"}, "toast": {"canceledSuccessfully": "Úprava objednávky byla úspěšně zrušena", "confirmedSuccessfully": "Úprava objednávky byla úspěšně potvrzena"}, "validation": {"quantityLowerThanFulfillment": "Nelze nastavit množství na méně než nebo rovno splněnému množství"}}, "edit": {"email": {"title": "Upravit e-mail", "requestSuccess": "E-mail objednávky byl aktualizován na {{email}}."}, "shippingAddress": {"title": "Upravit doručovací adresu", "requestSuccess": "Doručovací adresa objednávky byla aktualizována."}, "billingAddress": {"title": "Upravit fakturační adresu", "requestSuccess": "Fakturační adresa objednávky byla aktualizována."}}, "returns": {"create": "Vytvořit vrácení", "confirm": "Potvrdit vrácení", "confirmText": "Chystáte se potvrdit vrácení. <PERSON>to a<PERSON> ne<PERSON>ze vrátit zpět.", "inbound": "Příchozí", "outbound": "Odchozí", "sendNotification": "<PERSON><PERSON><PERSON> oz<PERSON>", "sendNotificationHint": "Oznámit zákazníkovi o vrácení.", "returnTotal": "Celková částka vrácení", "inboundTotal": "Celková částka příchozí", "estDifference": "Odhado<PERSON><PERSON> rozdíl", "outstandingAmount": "Nesplacená částka", "reason": "Důvod", "reasonHint": "<PERSON><PERSON><PERSON><PERSON>, proč chce zákazník vrátit položky.", "note": "Poznámka", "noInventoryLevel": "Žádná <PERSON> inventáře", "noInventoryLevelDesc": "Vybraná lokace nemá úroveň inventáře pro vybrané položky. Vrácení může být po<PERSON><PERSON>, ale nemůže být př<PERSON>, dokud nebude vytvořena úroveň inventáře pro vybranou lokaci.", "noteHint": "Můžete volně <PERSON>, pokud chcete něco upřesnit.", "location": "<PERSON><PERSON>", "locationHint": "<PERSON><PERSON><PERSON><PERSON>, na kterou lokaci chcete vrátit položky.", "inboundShipping": "Vrácení dopravy", "inboundShippingHint": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON>u metodu ch<PERSON> p<PERSON>.", "returnableQuantityLabel": "Množství k vrácení", "refundableAmountLabel": "Částka k vrácení", "returnRequestedInfo": "{{requestedItemsCount}}x <PERSON>žek p<PERSON>žadováno k vrácení", "returnReceivedInfo": "{{requestedItemsCount}}x položek přijato k vrácení", "itemReceived": "Položky přijaty", "returnRequested": "Požadováno vrácení", "damagedItemReceived": "Poškozené položky přijaty", "damagedItemsReturned": "{{quantity}}x poškozených položek vráceno", "activeChangeError": "Na této objednávce probíhá aktivní změna objednávky. Dokončete nebo zrušte předchozí změnu.", "cancel": {"title": "Zrušit vrácení", "description": "Jste si jisti, že chcete zrušit žádost o vrácení?"}, "placeholders": {"noReturnShippingOptions": {"title": "Nebyly nalezeny žádné možnosti vrácení dopravy", "hint": "Nebyly vytvořeny žádné možnosti vrácení dopravy pro lokaci. Můžete vytvořit jednu na <LinkComponent>Lokace & Doprava</LinkComponent>."}, "outboundShippingOptions": {"title": "Nebyly nalezeny žádné možnosti odchozí dopravy", "hint": "Nebyly vytvořeny žádné možnosti odchozí dopravy pro lokaci. Můžete vytvořit jednu na <LinkComponent>Lokace & Doprava</LinkComponent>."}}, "receive": {"action": "Přij<PERSON><PERSON>", "receiveItems": "{{ returnType }} {{ id }}", "restockAll": "Doplnit všechny položky", "itemsLabel": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pro #{{returnId}}", "sendNotificationHint": "Oznámit zákazníkovi o přijatém vrácení.", "inventoryWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že automaticky upravíme úrovně inventáře na základě vašeho vstupu výše.", "writeOffInputLabel": "<PERSON>lik položek je poš<PERSON>zených?", "toast": {"success": "Vrácení bylo <PERSON>.", "errorLargeValue": "Množství je větší než požadované množství položky.", "errorNegativeValue": "Množství nemůže být záporné.", "errorLargeDamagedValue": "Množství poškozených položek + množství nepoškozených přijatých položek přesahuje celkové množství položky na vrácení. Snižte množství nepoškozených položek."}}, "toast": {"canceledSuccessfully": "Vrácení <PERSON>", "confirmedSuccessfully": "Vrácení <PERSON>lo <PERSON>"}, "panel": {"title": "Vrácení zahá<PERSON>no", "description": "Existuje otevřená žádost o vrácení, která musí být dokončena"}}, "claims": {"create": "Vytvořit reklamaci", "confirm": "Potvrdit reklamaci", "confirmText": "Chystáte se potvrdit reklamaci. Tuto a<PERSON> nelze vrátit zpět.", "manage": "Spravovat reklamaci", "outbound": "Odchozí", "outboundItemAdded": "{{itemsCount}}x přid<PERSON>o prostřednictvím reklamace", "outboundTotal": "Celková částka odchozí", "outboundShipping": "Odchozí doprava", "outboundShippingHint": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON>u metodu ch<PERSON> p<PERSON>.", "refundAmount": "Odhado<PERSON><PERSON> rozdíl", "activeChangeError": "Na této objednávce probíhá aktivní změna objednávky. Dokončete nebo zrušte předchozí změnu.", "actions": {"cancelClaim": {"successToast": "Reklamace byla úspěšně z<PERSON>."}}, "cancel": {"title": "Zrušit reklamaci", "description": "Jste si jisti, že chcete zrušit reklamaci?"}, "tooltips": {"onlyReturnShippingOptions": "Tento sez<PERSON> bude obsahovat pouze možnosti vrácení dopravy."}, "toast": {"canceledSuccessfully": "Reklamace byla úsp<PERSON>šně z<PERSON>", "confirmedSuccessfully": "Reklamace byla úsp<PERSON>šně <PERSON>"}, "panel": {"title": "Rekla<PERSON><PERSON> z<PERSON>á<PERSON>", "description": "Existuje otevřená žádost o reklamaci, která musí být dokončena"}}, "exchanges": {"create": "Vytvořit výměnu", "manage": "Spravovat výměnu", "confirm": "Potvrdit výměnu", "confirmText": "Chystáte se potvrdit výměnu. <PERSON><PERSON> a<PERSON> ne<PERSON>ze vrátit zpět.", "outbound": "Odchozí", "outboundItemAdded": "{{itemsCount}}x př<PERSON><PERSON>o prostřednictvím výměny", "outboundTotal": "Celková částka odchozí", "outboundShipping": "Odchozí doprava", "outboundShippingHint": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON>u metodu ch<PERSON> p<PERSON>.", "refundAmount": "Odhado<PERSON><PERSON> rozdíl", "activeChangeError": "Na této objednávce probíhá aktivní změna objednávky. Dokončete nebo zrušte předchozí změnu.", "actions": {"cancelExchange": {"successToast": "Výměna byla <PERSON>š<PERSON> z<PERSON>."}}, "cancel": {"title": "Zrušit výměnu", "description": "Jste si jisti, že chcete zrušit výměnu?"}, "tooltips": {"onlyReturnShippingOptions": "Tento sez<PERSON> bude obsahovat pouze možnosti vrácení dopravy."}, "toast": {"canceledSuccessfully": "Výměna byla <PERSON>", "confirmedSuccessfully": "Výměna byla <PERSON>"}, "panel": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Existuje otevřená žádost o výměnu, kter<PERSON> musí být dokončena"}}, "reservations": {"allocatedLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notAllocatedLabel": "Nepřid<PERSON>len<PERSON>"}, "allocateItems": {"action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Přidělit položky objednávky", "locationDescription": "<PERSON><PERSON><PERSON><PERSON>, z které lokace chcete přidělit <PERSON>.", "itemsToAllocate": "Položky k přidělení", "itemsToAllocateDesc": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>, k<PERSON><PERSON>te přidě<PERSON>", "search": "Hledat položky", "consistsOf": "Skládá se z {{num}}x in<PERSON><PERSON><PERSON><PERSON><PERSON>", "requires": "Vyžaduje {{num}} na variantu", "toast": {"created": "Polož<PERSON> byly ú<PERSON> přiděleny"}, "error": {"quantityNotAllocated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>."}}, "shipment": {"title": "Označit plnění jako o<PERSON>", "trackingNumber": "Sledovac<PERSON>", "addTracking": "<PERSON><PERSON><PERSON><PERSON> sledova<PERSON>í <PERSON>lo", "sendNotification": "<PERSON><PERSON><PERSON> oz<PERSON>", "sendNotificationHint": "Oznámit zákazníkovi o této zásilce.", "toastCreated": "Zásilka byla úspěšně vytvořena."}, "fulfillment": {"cancelWarning": "Chystáte se zrušit plnění. <PERSON><PERSON> a<PERSON> nelze vrátit zpět.", "markAsDeliveredWarning": "Chystáte se označit plnění jako doru<PERSON>. <PERSON>to akci nelze vrátit zpět.", "differentOptionSelected": "Vybraná doprava se liší od té, kterou vybral zákazník.", "disabledItemTooltip": "Vybraná doprava neumožňuje vyřízení této <PERSON>ž<PERSON>", "unfulfilledItems": "Nesplněn<PERSON>", "statusLabel": "Stav plnění", "statusTitle": "Stav plnění", "fulfillItems": "Splnit položky", "awaitingFulfillmentBadge": "Čeká se na plnění", "requiresShipping": "Vyžaduje dopravu", "number": "Plnění #{{number}}", "itemsToFulfill": "Položky k plnění", "create": "Vytvořit plnění", "available": "Dostupné", "inStock": "Na skladě", "markAsShipped": "Označit jako o<PERSON>", "markAsPickedUp": "Označit jako v<PERSON>", "markAsDelivered": "Označit jako <PERSON>", "itemsToFulfillDesc": "Vyberte položky a množství k plnění", "locationDescription": "<PERSON><PERSON><PERSON><PERSON>, z které lokace chcete plnit položky.", "sendNotificationHint": "Oznámit zákazníkům o vytvořeném plnění.", "methodDescription": "Vyberte jinou metodu dopravy než tu, k<PERSON><PERSON> z<PERSON>azník vybral", "error": {"wrongQuantity": "K dispozici je pouze jedna položka k plnění", "wrongQuantity_other": "Množství by m<PERSON><PERSON> b<PERSON><PERSON> mezi 1 a {{number}}", "noItems": "Žádné položky k plnění.", "noShippingOption": "Je vyžadována doprava", "noLocation": "Je vyžadována lokace"}, "status": {"notFulfilled": "Nesplněno", "partiallyFulfilled": "Částečně splněno", "fulfilled": "Splněno", "partiallyShipped": "Částečně odesláno", "shipped": "Odesláno", "delivered": "<PERSON><PERSON><PERSON><PERSON>", "partiallyDelivered": "Částeč<PERSON><PERSON>", "partiallyReturned": "Částečně vráceno", "returned": "Vráceno", "canceled": "<PERSON><PERSON><PERSON><PERSON>", "requiresAction": "Vyžaduje <PERSON>"}, "toast": {"created": "Plnění bylo <PERSON> v<PERSON>", "canceled": "Plnění <PERSON>lo <PERSON>", "fulfillmentShipped": "Nelze zrušit již o<PERSON>lané plnění", "fulfillmentDelivered": "Plnění bylo <PERSON> jako do<PERSON>", "fulfillmentPickedUp": "Plnění bylo <PERSON> jako v<PERSON>z<PERSON>"}, "trackingLabel": "Sledování", "shippingFromLabel": "Doprava z", "itemsLabel": "Polož<PERSON>"}, "refund": {"title": "Vytvořit vrácení peněz", "sendNotificationHint": "Oznámit zákazníkům o vytvořeném vrácení peněz.", "systemPayment": "Systémová platba", "systemPaymentDesc": "Jedna nebo více va<PERSON><PERSON> plateb je systémová platba. Upozor<PERSON><PERSON><PERSON><PERSON>, že zachycení a vrácení peněz nejsou zpracovávány Medusou pro takové platby.", "error": {"amountToLarge": "Nelze vrátit více než původní částku objednávky.", "amountNegative": "Částka k vrácení musí být kladné <PERSON>.", "reasonRequired": "Prosím vyberte důvod vrácení peněz."}}, "customer": {"contactLabel": "Kontakt", "editEmail": "Upravit e-mail", "transferOwnership": "Převod vlastnictví", "editBillingAddress": "Upravit fakturační adresu", "editShippingAddress": "Upravit doručovací adresu"}, "activity": {"header": "Aktivita", "showMoreActivities_one": "Zobrazit {{count}} další aktivitu", "showMoreActivities_other": "Zobrazit {{count}} dalších aktivit", "comment": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> k<PERSON>", "addButtonText": "Přidat komentář", "deleteButtonText": "Smazat komentář"}, "from": "Od", "to": "Do", "events": {"common": {"toReturn": "K vrácení", "toSend": "K odeslání"}, "placed": {"title": "Objednávka zadána", "fromSalesChannel": "z {{salesChannel}}"}, "canceled": {"title": "Objednávka zrušena"}, "payment": {"awaiting": "Čeká se na platbu", "captured": "Platba z<PERSON>na", "canceled": "Platba zrušena", "refunded": "Platba vrácena"}, "fulfillment": {"created": "Položky splněny", "canceled": "Plnění <PERSON>", "shipped": "Položky odeslány", "delivered": "Položky doručeny", "items_one": "{{count}} <PERSON><PERSON><PERSON>", "items_other": "{{count}} <PERSON><PERSON><PERSON>"}, "return": {"created": "Vrácení #{{returnId}} p<PERSON><PERSON><PERSON>váno", "canceled": "Vrácení #{{returnId}} z<PERSON><PERSON>eno", "received": "Vrácení #{{returnId}} př<PERSON>to", "items_one": "{{count}} <PERSON>ž<PERSON> vrácena", "items_other": "{{count}} <PERSON><PERSON><PERSON> vrá<PERSON>no"}, "note": {"comment": "<PERSON><PERSON><PERSON><PERSON>", "byLine": "od {{author}}"}, "claim": {"created": "Reklamace #{{claimId}} p<PERSON>ž<PERSON>vána", "canceled": "Reklamace #{{claimId}} zrušena", "itemsInbound": "{{count}} polož<PERSON> k vrácení", "itemsOutbound": "{{count}} <PERSON>ž<PERSON> k odeslání"}, "exchange": {"created": "Výměna #{{exchangeId}} p<PERSON><PERSON><PERSON>vána", "canceled": "Výměna #{{exchangeId}} zrušena", "itemsInbound": "{{count}} polož<PERSON> k vrácení", "itemsOutbound": "{{count}} <PERSON>ž<PERSON> k odeslání"}, "edit": {"requested": "Úprava objednávky #{{editId}} p<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmed": "Úprava objednávky #{{editId}} potvrzena"}, "transfer": {"requested": "P<PERSON><PERSON>d objedn<PERSON>vky #{{transferId}} p<PERSON>ž<PERSON>ván", "confirmed": "<PERSON><PERSON><PERSON><PERSON> objedn<PERSON>ky #{{transferId}} potvrzen", "declined": "P<PERSON><PERSON>d objedn<PERSON>vky #{{transferId}} odmítnut"}, "update_order": {"shipping_address": "Doručovací adresa aktualizována", "billing_address": "Fakturační adresa aktualizována", "email": "E-mail aktualizován"}}}, "fields": {"displayId": "Zobrazit ID", "refundableAmount": "Částka k vrácení", "returnableQuantity": "Množství k vrácení"}}, "draftOrders": {"domain": "Koncepty objednávek", "deleteWarning": "Chystáte se smazat koncept objednávky {{id}}. <PERSON><PERSON> akci nelze vrátit zpět.", "paymentLinkLabel": "Odkaz na platbu", "cartIdLabel": "ID košíku", "markAsPaid": {"label": "Označit jako <PERSON>", "warningTitle": "Označit jako <PERSON>", "warningDescription": "Chystáte se označit koncept objednávky jako <PERSON>. <PERSON><PERSON> akci nelze vrátit zpět a později nebude možné vybrat platbu."}, "status": {"open": "O<PERSON>v<PERSON><PERSON>", "completed": "Dokončeno"}, "create": {"createDraftOrder": "Vytvořit koncept objednávky", "createDraftOrderHint": "Vytvořte nový koncept objednávky pro správu detailů objednávky před jejím zadáním.", "chooseRegionHint": "Vyberte region", "existingItemsLabel": "Existuj<PERSON><PERSON><PERSON>", "existingItemsHint": "Přidat existující produkty do konceptu objednávky.", "customItemsLabel": "Vlastn<PERSON>", "customItemsHint": "Přidat vlastní položky do konceptu objednávky.", "addExistingItemsAction": "Přidat existující <PERSON>", "addCustomItemAction": "Přidat vlastní položku", "noCustomItemsAddedLabel": "Zatím nebyly přidány žádné vlastní <PERSON>", "noExistingItemsAddedLabel": "Zatím nebyly přidány žádné existující <PERSON>", "chooseRegionTooltip": "Nejprve vyberte region", "useExistingCustomerLabel": "Použít existujícího zákazníka", "addShippingMethodsAction": "<PERSON><PERSON><PERSON><PERSON> metody do<PERSON>", "unitPriceOverrideLabel": "Přepsat j<PERSON> cenu", "shippingOptionLabel": "Možnost dopravy", "shippingOptionHint": "Vyberte možnost dopravy pro koncept objednávky.", "shippingPriceOverrideLabel": "Přepsat cenu dopravy", "shippingPriceOverrideHint": "Přepsat cenu dopravy pro koncept objednávky.", "sendNotificationLabel": "<PERSON><PERSON><PERSON> oz<PERSON>", "sendNotificationHint": "Odeslat oznámení zákazníkovi při vytvoření konceptu objednávky."}, "validation": {"requiredEmailOrCustomer": "E-mail nebo zákazník je povinný.", "requiredItems": "Je vyžadována alespoň jedna položka.", "invalidEmail": "E-mail musí být platná e-mailová adresa."}}, "stockLocations": {"domain": "Lokace & Doprava", "list": {"description": "Spravujte skladové lokace a možnosti dopravy vašeho obchodu."}, "create": {"header": "Vytvořit skladovou lokaci", "hint": "Skladová lokace je f<PERSON>zi<PERSON> m<PERSON>, kde jsou produkty skladovány a odesílány.", "successToast": "Lokace {{name}} by<PERSON>."}, "edit": {"header": "Upravit skladovou lokaci", "viewInventory": "Zobrazit inventář", "successToast": "Lokace {{name}} by<PERSON>š<PERSON>ě aktualizová<PERSON>."}, "delete": {"confirmation": "Chystáte se smazat skladovou lokaci {{name}}. <PERSON><PERSON> a<PERSON> nelze vrátit zpět."}, "fulfillmentProviders": {"header": "Poskytovatelé plnění", "shippingOptionsTooltip": "Tento rozbalovací seznam bude obsahovat pouze poskytovatele povolené pro tuto lokaci. Přidejte je do lokace, pokud je rozbalovací seznam zakázán.", "label": "Připojení poskytovatelé plnění", "connectedTo": "Připojeno k {{count}} z {{total}} pos<PERSON><PERSON><PERSON>lů plnění", "noProviders": "Tato skladová lokace není připojena k žádným poskytovatelům plnění.", "action": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "successToast": "Poskytovatelé plnění pro skladovou lokaci byli úspěšně aktualizováni."}, "fulfillmentSets": {"pickup": {"header": "Vyzdvihnutí"}, "shipping": {"header": "<PERSON><PERSON><PERSON>"}, "disable": {"confirmation": "Jste si jisti, že ch<PERSON>te z<PERSON>t {{name}}? Tímto budou smazány všechny přidružené zóny služeb a možnosti dopravy a tuto akci nelze vrátit zpět.", "pickup": "Vyzdvihnutí bylo <PERSON>.", "shipping": "Doprava byla úspěšně z<PERSON>."}, "enable": {"pickup": "Vyzdvihnutí bylo úsp<PERSON>šně povoleno.", "shipping": "<PERSON><PERSON><PERSON> by<PERSON>š<PERSON> p<PERSON>."}}, "sidebar": {"header": "<PERSON>n<PERSON><PERSON><PERSON> dopravy", "shippingProfiles": {"label": "<PERSON><PERSON> dopravy", "description": "Skupinové produkty podle požadavků na dopravu"}}, "salesChannels": {"header": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "hint": "Správa prodejn<PERSON>ch kanálů připojených k této lokaci", "label": "Př<PERSON><PERSON><PERSON><PERSON><PERSON> pro<PERSON> ka<PERSON>", "connectedTo": "Připojeno k {{count}} z {{total}} pro<PERSON><PERSON><PERSON><PERSON>", "noChannels": "Lokace není připojena k žádným prodejním kanálům.", "action": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "successToast": "Prodejn<PERSON> kanály byly úsp<PERSON>šně aktualizovány."}, "pickupOptions": {"edit": {"header": "Upravit možnost vyzvednutí"}}, "shippingOptions": {"create": {"shipping": {"header": "Vytvořit možnost dopravy pro {{zone}}", "hint": "Vytvořte novou možnost dopravy pro definování způsobu dopravy produktů z této lokace.", "label": "Možnosti dopravy", "successToast": "Možnost dopravy {{name}} by<PERSON> úspěšně vytvoř<PERSON>."}, "pickup": {"header": "Vytvořit možnost vyzvednutí pro {{zone}}", "hint": "Vytvořte novou možnost vyzvednutí, k<PERSON><PERSON>, jak jsou produkty vyzvedávány z této lokace.", "label": "Možnosti vyzvednutí", "successToast": "Možnost vyzvednutí {{name}} by<PERSON>š<PERSON> vytvoř<PERSON>."}, "returns": {"header": "Vytvořit možnost vrácení pro {{zone}}", "hint": "Vytvořte novou možnost vrácení pro definování způsobu vrácení produktů do této lokace.", "label": "Možnosti vrácení", "successToast": "Možnost vrácení {{name}} by<PERSON>š<PERSON>ě vytvořena."}, "tabs": {"details": "Detaily", "prices": "<PERSON><PERSON>"}, "action": "Vytvořit možnost"}, "delete": {"confirmation": "Chystáte se smazat možnost dopravy {{name}}. <PERSON><PERSON> a<PERSON> nelze vrátit zpět.", "successToast": "Možnost dopravy {{name}} by<PERSON>."}, "edit": {"header": "Upravit možnost dopravy", "action": "Upravit možnost", "successToast": "Možnost dopravy {{name}} byla úspěšně aktualizována."}, "pricing": {"action": "<PERSON><PERSON><PERSON><PERSON> ceny"}, "conditionalPrices": {"header": "Podmíně<PERSON><PERSON> ceny pro {{name}}", "description": "Spravujte podmíněné ceny pro tuto možnost dopravy na základě celkové částky položek v košíku.", "attributes": {"cartItemTotal": "Celková částka položek v košíku"}, "summaries": {"range": "Pokud <0>{{attribute}}</0> je mezi <1>{{gte}}</1> a <2>{{lte}}</2>", "greaterThan": "Pokud <0>{{attribute}}</0> ≥ <1>{{gte}}</1>", "lessThan": "Pokud <0>{{attribute}}</0> ≤ <1>{{lte}}</1>"}, "actions": {"addPrice": "P<PERSON>idat cenu", "manageConditionalPrices": "Spravovat podmíněné ceny"}, "rules": {"amount": "<PERSON>na m<PERSON>žnosti dopravy", "gte": "Minimální celková částka položek v košíku", "lte": "Maximální celková částka položek v košíku"}, "customRules": {"label": "Vlastní pra<PERSON>", "tooltip": "<PERSON><PERSON> cena má pravidla, která nelze spravovat v dashboardu.", "eq": "Celková částka položek v košíku musí být rovna", "gt": "Celková částka položek v košíku musí být větší než", "lt": "Celková částka položek v košíku musí být menší než"}, "errors": {"amountRequired": "<PERSON>na možnosti dopravy je povinná", "minOrMaxRequired": "Musí být poskytnuta alespoň jedna z minimální nebo maximální celkové částky položek v košíku", "minGreaterThanMax": "Minimální celková částka položek v košíku musí být menší nebo rovna maximální celkové částce položek v košíku", "duplicateAmount": "Cena možnosti dopravy musí být jedinečná pro každou podmínku", "overlappingConditions": "Podmínky musí být jedinečné pro všechna pravidla cen"}}, "fields": {"count": {"shipping_one": "{{count}} možnost dopravy", "shipping_other": "{{count}} m<PERSON><PERSON><PERSON><PERSON> dopravy", "pickup_one": "{{count}} možnost vyzvednutí", "pickup_other": "{{count}} možností vyzvednutí", "returns_one": "{{count}} možnost vrácení", "returns_other": "{{count}} možnosti vrácení"}, "priceType": {"label": "<PERSON><PERSON> ceny", "options": {"fixed": {"label": "Pevná", "hint": "Cena možnosti dopravy je pevná a nemění se na základě obsahu objednávky."}, "calculated": {"label": "Vypočítaná", "hint": "Cena možnosti dopravy je vypočítána poskytovatelem plnění během pokladny."}}}, "enableInStore": {"label": "Povolit v obchodě", "hint": "Zda zákazníci mohou použít tuto možnost během pokladny."}, "provider": "Poskytovatel plnění", "profile": "<PERSON>il dopra<PERSON>", "fulfillmentOption": "Možnost plnění"}}, "serviceZones": {"create": {"headerPickup": "Vytvořit zónu služeb pro vyzdvihnutí z {{location}}", "headerShipping": "Vytvořit zónu služeb pro dopravu z {{location}}", "action": "Vytvořit zónu služeb", "successToast": "<PERSON><PERSON><PERSON> s<PERSON> {{name}} by<PERSON> úspěšně vytvoř<PERSON>."}, "edit": {"header": "Upravit z<PERSON>", "successToast": "<PERSON><PERSON><PERSON> s<PERSON> {{name}} by<PERSON> úspěšně aktualizována."}, "delete": {"confirmation": "Chystá<PERSON> se smazat z<PERSON> s<PERSON> {{name}}. <PERSON><PERSON> a<PERSON> ne<PERSON>ze vrátit zpět.", "successToast": "<PERSON><PERSON><PERSON> s<PERSON> {{name}} by<PERSON>."}, "manageAreas": {"header": "Spravovat oblasti pro {{name}}", "action": "Spravovat oblasti", "label": "Oblasti", "hint": "Vyberte geografické o<PERSON>i, k<PERSON><PERSON> z<PERSON>a služeb pokrývá.", "successToast": "Oblasti pro {{name}} byly <PERSON><PERSON><PERSON><PERSON><PERSON>ě aktualizová<PERSON>."}, "fields": {"noRecords": "<PERSON>ej<PERSON>u žádn<PERSON> zón<PERSON>, do kterých by by<PERSON> mo<PERSON><PERSON> přidat možnosti dopravy.", "tip": "Zóna služeb je sbírka geografických zón nebo oblastí. Používá se k omezení dostupných možností dopravy na definovanou sadu lokací."}}}, "shippingProfile": {"domain": "<PERSON><PERSON> dopravy", "subtitle": "Skupinové produkty s podobnými požadavky na dopravu do profilů.", "create": {"header": "Vytvořit profil dopravy", "hint": "Vytvořte nový profil dopravy pro skupinové produkty s podobnými požadavky na dopravu.", "successToast": "Profil dopravy {{name}} byl <PERSON><PERSON>š<PERSON> v<PERSON>."}, "delete": {"title": "<PERSON><PERSON><PERSON><PERSON> profil dopravy", "description": "<PERSON><PERSON>t<PERSON><PERSON> se smazat profil dopravy {{name}}. <PERSON><PERSON> a<PERSON> ne<PERSON> vrátit zpět.", "successToast": "Profil dopravy {{name}} by<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>."}, "tooltip": {"type": "Zadejte typ profilu dopravy, nap<PERSON><PERSON><PERSON>: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>uze nákladní doprava atd."}}, "taxRegions": {"domain": "Daňové regiony", "list": {"hint": "<PERSON><PERSON><PERSON><PERSON><PERSON>, co účtujete svým zákazníkům, <PERSON><PERSON><PERSON>ují z různých zemí a regionů."}, "delete": {"confirmation": "Chystáte se smazat daňový region. Tuto a<PERSON> nelze vrátit zpět.", "successToast": "Daňový region byl úspěšně smazán."}, "create": {"header": "Vytvořit daňový region", "hint": "Vytvořte nový daňový region pro definování daňových sazeb pro konkrétní zemi.", "errors": {"missingProvider": "Poskytovatel je povinný při vytváření daňového regionu.", "missingCountry": "Země je povinná při vytváření daňového regionu."}, "successToast": "Daňový region byl úspěšně vytvořen."}, "edit": {"header": "Upravit daňový region", "hint": "Změna <PERSON>ňového regionu.", "successToast": "Daňový region byl úspěšně aktualizován."}, "province": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "Vytvořit daňový region provincie", "hint": "Vytvořte nový daňový region pro definování daňových sazeb pro konkrétní provincii."}}, "provider": {"header": "Daňový poskytovatel"}, "state": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "Vytvořit daňový region státu", "hint": "Vytvořte nový daňový region pro definování daňových sazeb pro konkrétní stát."}}, "stateOrTerritory": {"header": "<PERSON><PERSON><PERSON> nebo teritoria", "create": {"header": "Vytvořit daňový region státu/teritoria", "hint": "Vytvořte nový daňový region pro definování daňových sazeb pro konkrétní stát/teritorium."}}, "county": {"header": "Okresy", "create": {"header": "Vytvořit daňový region okresu", "hint": "Vytvořte nový daňový region pro definování daňových sazeb pro konkrétní okres."}}, "region": {"header": "Regiony", "create": {"header": "Vytvořit daňový region regionu", "hint": "Vytvořte nový daňový region pro definování daňových sazeb pro konkrétní region."}}, "department": {"header": "Oddělení", "create": {"header": "Vytvořit daňový region oddělení", "hint": "Vytvořte nový daňový region pro definování daňových sazeb pro konkrétní oddělení."}}, "territory": {"header": "Teritoria", "create": {"header": "Vytvořit daňový region teritoria", "hint": "Vytvořte nový daňový region pro definování daňových sazeb pro konkrétní teritorium."}}, "prefecture": {"header": "Prefektury", "create": {"header": "Vytvořit daňový region prefektury", "hint": "Vytvořte nový daňový region pro definování daňových sazeb pro konkrétní prefekturu."}}, "district": {"header": "Okresy", "create": {"header": "Vytvořit daňový region okresu", "hint": "Vytvořte nový daňový region pro definování daňových sazeb pro konkrétní okres."}}, "governorate": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Vytvořit daňový region guvernorátu", "hint": "Vytvořte nový daňový region pro definování daňových sazeb pro konkrétní guvernorát."}}, "canton": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "Vytvořit daňový region kantonu", "hint": "Vytvořte nový daňový region pro definování daňových sazeb pro konkrétní kanton."}}, "emirate": {"header": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Vytvořit daňový region emirátu", "hint": "Vytvořte nový daňový region pro definování daňových sazeb pro konkrétní emirát."}}, "sublevel": {"header": "Podúrovně", "create": {"header": "Vytvořit daňový region podúrovně", "hint": "Vytvořte nový daňový region pro definování daňových sazeb pro konkrétní podúroveň."}}, "taxOverrides": {"header": "Přepsání", "create": {"header": "Vytvořit přepsání", "hint": "Vytvořte daňovou sazbu, která přepíše výchozí daňové sazby pro vybrané podmínky."}, "edit": {"header": "Upravit přepsání", "hint": "Upravte daňovou sazbu, která přepíše výchozí daňové sazby pro vybrané podmínky."}}, "taxRates": {"create": {"header": "Vytvořit daňovou sazbu", "hint": "Vytvořte novou daňovou sazbu pro definování daňové sazby pro region.", "successToast": "Daňová sazba byla úspěšně vytvořena."}, "edit": {"header": "Upravit daňovou sazbu", "hint": "Upravte daňovou sazbu pro definování daňové sazby pro region.", "successToast": "Daňová sazba byla úspěšně aktualizována."}, "delete": {"confirmation": "Chystáte se smazat daňovou sazbu {{name}}. <PERSON><PERSON> a<PERSON> ne<PERSON>ze vrátit zpět.", "successToast": "<PERSON>ňová sazba byla úspěšně s<PERSON>."}}, "fields": {"isCombinable": {"label": "Kombinovatelné", "hint": "Zda lze tuto daňovou sazbu kombinovat s výchozí sazbou z daňového regionu.", "true": "Kombinovatelné", "false": "Nekombinovatelné"}, "defaultTaxRate": {"label": "Výchozí <PERSON> sazba", "tooltip": "Výchozí daňová sazba pro tento region. Příkladem je standardní sazba DPH pro zemi nebo region.", "action": "Vytvořit výchozí daňovou sazbu"}, "taxRate": "Daňová sazba", "taxCode": "Daňový kód", "taxProvider": "Daňový poskytovatel", "targets": {"label": "<PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON><PERSON>, na které se tato da<PERSON> sazba bude vztah<PERSON>t.", "options": {"product": "Produkty", "productCollection": "<PERSON><PERSON><PERSON><PERSON> produktů", "productTag": "Štítky produktů", "productType": "Typy produktů", "customerGroup": "Skupiny zákazníků"}, "operators": {"in": "v", "on": "na", "and": "a"}, "placeholders": {"product": "Hledat produkty", "productCollection": "Hledat kolekce produktů", "productTag": "Hledat štítky produktů", "productType": "Hledat typy produktů", "customerGroup": "Hledat skupiny zákazníků"}, "tags": {"product": "Produkt", "productCollection": "<PERSON><PERSON><PERSON><PERSON> produktů", "productTag": "Štítek produktu", "productType": "Typ produktu", "customerGroup": "<PERSON><PERSON><PERSON>"}, "modal": {"header": "<PERSON><PERSON><PERSON><PERSON>"}, "values_one": "{{count}} hodnota", "values_other": "{{count}} hodnot", "numberOfTargets_one": "{{count}} c<PERSON>l", "numberOfTargets_other": "{{count}} c<PERSON><PERSON><PERSON>", "additionalValues_one": "a {{count}} da<PERSON><PERSON><PERSON> ho<PERSON>", "additionalValues_other": "a {{count}} da<PERSON><PERSON><PERSON><PERSON> hodnot", "action": "<PERSON><PERSON><PERSON><PERSON>"}, "sublevels": {"labels": {"province": "<PERSON><PERSON><PERSON>", "state": "<PERSON><PERSON><PERSON>", "region": "Region", "stateOrTerritory": "Stát/Teritorium", "department": "Oddělení", "county": "<PERSON><PERSON>", "territory": "Teritorium", "prefecture": "Prefektura", "district": "<PERSON><PERSON>", "governorate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emirate": "<PERSON><PERSON><PERSON><PERSON>", "canton": "<PERSON><PERSON>", "sublevel": "<PERSON><PERSON><PERSON>"}, "placeholders": {"province": "<PERSON><PERSON><PERSON><PERSON> provincii", "state": "<PERSON><PERSON><PERSON><PERSON>", "region": "Vyberte region", "stateOrTerritory": "Vyberte stát/teritorium", "department": "<PERSON><PERSON><PERSON><PERSON>", "county": "Vyberte ok<PERSON>", "territory": "Vyberte teritorium", "prefecture": "Vyberte prefekturu", "district": "Vyberte ok<PERSON>", "governorate": "<PERSON><PERSON><PERSON><PERSON>", "emirate": "<PERSON><PERSON><PERSON><PERSON>", "canton": "<PERSON><PERSON><PERSON><PERSON>"}, "tooltips": {"sublevel": "Zadejte kód ISO 3166-2 pro daňový region podúrovně.", "notPartOfCountry": "{{province}} se nezdá být součástí {{country}}. Prosím zkontrolujte, zda je to správné."}, "alert": {"header": "Podúrovně regionů jsou pro tento daňový region zakázány", "description": "Podúrovně regionů jsou pro tento region ve výchozím nastavení zakázány. Můžete je povolit pro vytvoření podúrovní regionů, jako jsou provincie, st<PERSON><PERSON> nebo teritoria.", "action": "Povolit podúrovně regionů"}}, "noDefaultRate": {"label": "Žádná výchozí sazba", "tooltip": "Tento daňový region nemá výchozí daňovou sazbu. Pokud existuje standardní sa<PERSON>, jako je DPH země, přidejte ji do tohoto regionu."}}}, "promotions": {"domain": "Propagace", "sections": {"details": "Detaily propagace"}, "tabs": {"template": "<PERSON><PERSON>", "details": "Detaily", "campaign": "Kampaň"}, "fields": {"type": "<PERSON><PERSON>", "value_type": "<PERSON><PERSON> hodnoty", "value": "Hodnota", "campaign": "Kampaň", "method": "Metoda", "allocation": "Přidělení", "addCondition": "Přidat podmínku", "clearAll": "Vymazat vše", "taxInclusive": "Vč<PERSON><PERSON><PERSON> daně", "amount": {"tooltip": "Vyberte kód měny pro povolení nastavení částky"}, "conditions": {"rules": {"title": "Kdo může použít tento kód?", "description": "Který zákazník může použít propagační kód? Propagační kód může být použit všemi zákazníky, pokud zůstane nedotčen."}, "target-rules": {"title": "Na které <PERSON> bude propagace aplikována?", "description": "Propagace bude aplikována na položky, které odpovídají následujícím podmínkám."}, "buy-rules": {"title": "Co musí být v košíku, aby se propagace odemkla?", "description": "Pokud tyto podmínky odpovídají, povolíme propagaci na cílové položky."}}}, "tooltips": {"campaignType": "Kód měny musí být vybrán v propagaci pro nastavení rozpočtu výdajů."}, "errors": {"requiredField": "<PERSON><PERSON><PERSON><PERSON> pole", "promotionTabError": "Opravte chyby v záložce Propagace před pokračováním"}, "toasts": {"promotionCreateSuccess": "Propagace ({{code}}) byla úspěšně vytvořena."}, "create": {}, "edit": {"title": "Upravit detaily propagace", "rules": {"title": "Upravit podmínky použití"}, "target-rules": {"title": "Upravit podmínky položek"}, "buy-rules": {"title": "<PERSON><PERSON><PERSON><PERSON> pra<PERSON> n<PERSON>"}}, "campaign": {"header": "Kampaň", "edit": {"header": "Upravit ka<PERSON>", "successToast": "Kampaň propagace byla úspěšně aktualizována."}, "actions": {"goToCampaign": "Přejít na kampaň"}}, "campaign_currency": {"tooltip": "Toto je měna propagace. Změňte ji na záložce Detaily."}, "form": {"required": "<PERSON><PERSON><PERSON><PERSON>", "and": "A", "selectAttribute": "Vyberte atribut", "campaign": {"existing": {"title": "Existující kamp<PERSON>ň", "description": "Přidat propagaci do existující kampaně.", "placeholder": {"title": "Ž<PERSON><PERSON><PERSON>jící kampaně", "desc": "Můžete vytvořit jednu pro sledování více propagací a nastavení limitů roz<PERSON>čtu."}}, "new": {"title": "Nová kampaň", "description": "Vytvořit novou kampaň pro tuto propagaci."}, "none": {"title": "Bez kampaně", "description": "Pokračovat bez přiřazení propagace ke kampani"}}, "taxInclusive": {"title": "Z<PERSON><PERSON><PERSON>je propagace daně?", "description": "Povolte toto pole pro aplikaci propagace po zdanění. Pokud je <PERSON>, propagace se aplikuje před zdaněním."}, "status": {"label": "Stav", "draft": {"title": "Koncept", "description": "Zákazníci nebudou moci použít kód zatím"}, "active": {"title": "Aktivní", "description": "Zákazníci budou moci p<PERSON>žít kód"}, "inactive": {"title": "Neaktivní", "description": "Zákazníci již nebudou moci použít kód"}}, "method": {"label": "Metoda", "code": {"title": "Propagační kód", "description": "Zákazníci musí zadat tento kód při pokladně"}, "automatic": {"title": "Automatické", "description": "Zákazníci uvidí tuto propagaci při pokladně"}}, "max_quantity": {"title": "Maximální množství", "description": "Maximální množství položek, na které se tato propagace vztahuje."}, "type": {"standard": {"title": "Standardní", "description": "Standardní propagace"}, "buyget": {"title": "<PERSON><PERSON><PERSON>", "description": "Koupit X získat Y propagace"}}, "allocation": {"each": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Aplikuje hodnotu na každou položku"}, "across": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Aplikuje hodnotu napříč <PERSON>"}}, "code": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>, který vaši zákazníci zadají při pokladně."}, "value": {"title": "Hodnota propagace"}, "value_type": {"fixed": {"title": "Hodnota propagace", "description": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> bude odečtena. např. 100"}, "percentage": {"title": "Hodnota propagace", "description": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON> bude odečteno z částky. např. 8%"}}}, "deleteWarning": "Chystáte se smazat propagaci {{code}}. <PERSON><PERSON> a<PERSON> nelze vrátit zpět.", "createPromotionTitle": "Vytvořit propagaci", "type": "Typ propagace", "conditions": {"add": "Přidat podmínku", "list": {"noRecordsMessage": "Přidejte podmínku pro omezení toho, na které položky se propagace vztahuje."}}}, "campaigns": {"domain": "Kampaně", "details": "Detaily kampaně", "status": {"active": "Aktivní", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scheduled": "<PERSON><PERSON><PERSON><PERSON>"}, "delete": {"title": "Jste si jisti?", "description": "<PERSON><PERSON>tá<PERSON> se smazat kamp<PERSON>ň '{{name}}'. <PERSON><PERSON> a<PERSON> ne<PERSON> vrátit zpět.", "successToast": "Kampaň '{{name}}' by<PERSON> v<PERSON>voř<PERSON>."}, "edit": {"header": "Upravit ka<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>.", "successToast": "Kampaň '{{name}}' by<PERSON>šně aktualizová<PERSON>."}, "configuration": {"header": "Konfigurace", "edit": {"header": "Upravit konfiguraci kamp<PERSON>ě", "description": "Upravit konfiguraci kamp<PERSON>ě.", "successToast": "Konfigurace ka<PERSON> byla úsp<PERSON>šně aktualizována."}}, "create": {"title": "Vytvořit kampaň", "description": "Vytvořit propagační kampaň.", "hint": "Vytvořit propagační kampaň.", "header": "Vytvořit kampaň", "successToast": "Kampaň '{{name}}' by<PERSON> v<PERSON>voř<PERSON>."}, "fields": {"name": "<PERSON><PERSON><PERSON><PERSON>", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start_date": "Datum z<PERSON>čá<PERSON>", "end_date": "<PERSON><PERSON> konce", "total_spend": "Utracený rozpočet", "total_used": "Použitý rozpočet", "budget_limit": "<PERSON><PERSON>", "campaign_id": {"hint": "Pouze kampaně se stejným kódem měny jako propagace jsou zobrazeny v tomto seznamu."}}, "budget": {"create": {"hint": "Vytvořit rozpočet pro kampaň.", "header": "Rozpočet kampaně"}, "details": "Rozpočet kampaně", "fields": {"type": "<PERSON><PERSON>", "currency": "Měna", "limit": "Limit", "used": "<PERSON><PERSON><PERSON><PERSON>"}, "type": {"spend": {"title": "Utratit", "description": "Nastavit limit na celkovou slevu všech použití propagace."}, "usage": {"title": "Použití", "description": "Nastavit limit na počet použití propagace."}}, "edit": {"header": "Upravit rozpočet kampaně"}}, "promotions": {"remove": {"title": "Odstranit propagaci z kampaně", "description": "Chystáte se odstranit {{count}} propagaci z kampaně. <PERSON>to akci nelze vrátit zpět."}, "alreadyAdded": "Tato propagace již byla přidána do kampaně.", "alreadyAddedDiffCampaign": "Tato propagace již byla př<PERSON>ána do jiné kampaně ({{name}}).", "currencyMismatch": "<PERSON><PERSON>na propagace a kampaně se neshoduje", "toast": {"success": "Úspěšně přidáno {{count}} propagací do kampaně"}, "add": {"list": {"noRecordsMessage": "Nejprve vytvořte propagaci."}}, "list": {"noRecordsMessage": "V kampani nejsou žádné propagace."}}, "deleteCampaignWarning": "<PERSON><PERSON>tá<PERSON> se smazat kamp<PERSON> {{name}}. <PERSON><PERSON> a<PERSON> ne<PERSON> vrátit zpět.", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "Ceníky", "subtitle": "Vytvořte prodeje nebo přepište ceny pro <PERSON><PERSON><PERSON>.", "delete": {"confirmation": "<PERSON><PERSON>t<PERSON><PERSON> se smazat ceník {{title}}. <PERSON><PERSON> a<PERSON> ne<PERSON> vrátit zpět.", "successToast": "<PERSON><PERSON><PERSON> {{title}} byl <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>."}, "create": {"header": "Vytvořit ceník", "subheader": "Vytvořte nový ceník pro správu cen va<PERSON><PERSON> produktů.", "tabs": {"details": "Detaily", "products": "Produkty", "prices": "<PERSON><PERSON>"}, "successToast": "<PERSON><PERSON><PERSON> {{title}} byl <PERSON><PERSON><PERSON><PERSON> v<PERSON>.", "products": {"list": {"noRecordsMessage": "Nejprve vytvořte produkt."}}}, "edit": {"header": "Upravit ceník", "successToast": "<PERSON><PERSON><PERSON> {{title}} byl <PERSON>sp<PERSON>š<PERSON>ě aktualiz<PERSON>."}, "configuration": {"header": "Konfigurace", "edit": {"header": "Upravit konfiguraci ceníku", "description": "Upravit konfiguraci ceníku.", "successToast": "Konfigurace ceníku byla úspěšně aktualizována."}}, "products": {"header": "Produkty", "actions": {"addProducts": "Přidat produkty", "editPrices": "<PERSON><PERSON><PERSON><PERSON> ceny"}, "delete": {"confirmation_one": "Chystáte se smazat ceny pro {{count}} produkt v ceníku. Tuto akci nelze vrátit zpět.", "confirmation_other": "Chystáte se smazat ceny pro {{count}} produktů v ceníku. Tuto akci nelze vrátit zpět.", "successToast_one": "Úspěš<PERSON><PERSON> s<PERSON>y ceny pro {{count}} produkt.", "successToast_other": "Úspěš<PERSON><PERSON> s<PERSON>y ceny pro {{count}} produktů."}, "add": {"successToast": "Ceny byly úspěšně přidány do ceníku."}, "edit": {"successToast": "<PERSON><PERSON> byly ú<PERSON>šně aktualizovány."}}, "fields": {"priceOverrides": {"label": "Přepsání cen", "header": "Přepsání cen"}, "status": {"label": "Stav", "options": {"active": "Aktivní", "draft": "Koncept", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scheduled": "<PERSON><PERSON><PERSON><PERSON>"}}, "type": {"label": "<PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON><PERSON> ty<PERSON> cen<PERSON>, k<PERSON><PERSON> chcete vytvořit.", "options": {"sale": {"label": "<PERSON><PERSON><PERSON>", "description": "Prodejní ceny jsou doč<PERSON>né změny cen produktů."}, "override": {"label": "Přepsání", "description": "Přepsání se obvykle používají k vytvoření specifických cen pro zákazníky."}}}, "startsAt": {"label": "Ceník má datum začátku?", "hint": "Naplánujte ceník, aby se aktivoval v budoucnu."}, "endsAt": {"label": "Ceník má datum vypršení?", "hint": "Naplánujte ceník, aby se deaktivoval v budoucnu."}, "customerAvailability": {"header": "Vyberte skupiny zákazníků", "label": "Dostupnost pro zákazníky", "hint": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> s<PERSON> by m<PERSON><PERSON> b<PERSON><PERSON> cen<PERSON> ovlivně<PERSON>.", "placeholder": "Hledat skupiny zákazníků", "attribute": "Skupiny zákazníků"}}}, "profile": {"domain": "Profil", "manageYourProfileDetails": "Spravujte detaily svého profilu.", "fields": {"languageLabel": "Jazyk", "usageInsightsLabel": "Přehledy používání"}, "edit": {"header": "<PERSON><PERSON><PERSON>t profil", "languageHint": "Jazyk, který chcete používat v administrativním dashboardu. To nezmění jazyk vašeho obchodu.", "languagePlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "usageInsightsHint": "Sdílejte přehledy používání a pomozte nám zlepšit Medusu. <PERSON><PERSON><PERSON> o tom, co shromažďujeme a jak to používáme, si můžete přečíst v naší <0>dokumentaci</0>."}, "toast": {"edit": "Změny profilu byly ul<PERSON>"}}, "users": {"domain": "Uživatelé", "editUser": "Upravi<PERSON>", "inviteUser": "Pozvat uživatele", "inviteUserHint": "Pozvěte nového uživatele do vašeho obchodu.", "sendInvite": "Odeslat <PERSON>vánku", "pendingInvites": "Čekající <PERSON>", "deleteInviteWarning": "Chystá<PERSON> se smazat pozvánku pro {{email}}. <PERSON><PERSON> a<PERSON> ne<PERSON> vrátit zpět.", "resendInvite": "Znovu odeslat pozvánku", "copyInviteLink": "Zkopírovat odkaz na pozvánku", "expiredOnDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dne {{date}}", "validFromUntil": "Platné od <0>{{from}}</0> - <1>{{until}}</1>", "acceptedOnDate": "<PERSON><PERSON><PERSON><PERSON> dne {{date}}", "inviteStatus": {"accepted": "<PERSON><PERSON><PERSON><PERSON>", "pending": "Čeká se", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "roles": {"admin": "Admin", "developer": "Vývojář", "member": "Č<PERSON>"}, "list": {"empty": {"heading": "Žádní uživatelé nenalezeni", "description": "<PERSON><PERSON><PERSON> bude uživatel pozván, zobrazí se zde."}, "filtered": {"heading": "Žádné v<PERSON>dky", "description": "Žádní uživatelé neodpovídají aktuálním kritériím filtru."}}, "deleteUserWarning": "<PERSON><PERSON>t<PERSON><PERSON> se smazat u<PERSON> {{name}}. <PERSON><PERSON> a<PERSON> ne<PERSON> vrátit zpět.", "deleteUserSuccess": "Uživatel {{name}} byl <PERSON><PERSON><PERSON> s<PERSON>", "invite": "Pozvat"}, "store": {"domain": "<PERSON><PERSON><PERSON><PERSON>", "manageYourStoresDetails": "Spravujte detaily svého obchodu", "editStore": "Upravit ob<PERSON>d", "defaultCurrency": "Výchozí měna", "defaultRegion": "Výchozí region", "defaultSalesChannel": "Výchozí prodejní kanál", "defaultLocation": "Výchozí lokace", "swapLinkTemplate": "Šablona odkazu na výměnu", "paymentLinkTemplate": "Šablona odkazu na platbu", "inviteLinkTemplate": "Šablona odkazu na pozvánku", "currencies": "Měny", "addCurrencies": "<PERSON><PERSON><PERSON><PERSON>", "enableTaxInclusivePricing": "Povolit ceny včetně daně", "disableTaxInclusivePricing": "Zak<PERSON>zat ceny včetně daně", "removeCurrencyWarning_one": "Chystáte se odstranit {{count}} měnu z vašeho obchodu. Ujistěte se, že jste odstranili všechny ceny používající tuto měnu před pok<PERSON>čováním.", "removeCurrencyWarning_other": "Chystáte se odstranit {{count}} měn z vašeho obchodu. Ujistěte se, že jste odstranili všechny ceny používající tyto měny před pokračováním.", "currencyAlreadyAdded": "Měna již byla přidána do vašeho obchodu.", "edit": {"header": "Upravit ob<PERSON>d"}, "toast": {"update": "<PERSON><PERSON><PERSON><PERSON> <PERSON>l úspěšně aktualizován", "currenciesUpdated": "Měny byly úsp<PERSON>šně aktualizovány", "currenciesRemoved": "Měny byly úsp<PERSON>šně odstraněny z obchodu", "updatedTaxInclusivitySuccessfully": "Ceny včetně daně byly úspěšně aktualizovány"}}, "regions": {"domain": "Regiony", "subtitle": "Region je oblast, ve které prodáváte produkty. Může pokrývat více zemí a má různé daňové sazby, poskytovatele a měnu.", "createRegion": "Vytvořit region", "createRegionHint": "Spravujte daňové sazby a poskytovatele pro sadu zemí.", "addCountries": "Př<PERSON>t země", "editRegion": "Upravit region", "countriesHint": "Přidejte země zahrnuté v tomto regionu.", "deleteRegionWarning": "Chystáte se smazat region {{name}}. <PERSON><PERSON> a<PERSON> ne<PERSON> vrátit zpět.", "removeCountriesWarning_one": "Chystáte se odstranit {{count}} zemi z regionu. <PERSON><PERSON> a<PERSON> nelze vrátit zpět.", "removeCountriesWarning_other": "Chystáte se odstranit {{count}} zemí z regionu. <PERSON><PERSON> a<PERSON> nelze vrátit zpět.", "removeCountryWarning": "Chystáte se odstranit zemi {{name}} z regionu. <PERSON>to a<PERSON> nelze vrátit zpět.", "automaticTaxesHint": "<PERSON><PERSON><PERSON> je povoleno, da<PERSON><PERSON> budou v<PERSON>čítány pouze při pokladně na základě doručovací adresy.", "taxInclusiveHint": "<PERSON><PERSON>ž je povoleno, ceny v regionu budou zahrnovat daň.", "providersHint": "<PERSON><PERSON><PERSON><PERSON><PERSON>, které platební poskytovatele jsou dostupné v tomto regionu.", "shippingOptions": "Možnosti dopravy", "deleteShippingOptionWarning": "Chystáte se smazat možnost dopravy {{name}}. <PERSON><PERSON> a<PERSON> nelze vrátit zpět.", "return": "Vrácení", "outbound": "Odchozí", "priceType": "<PERSON><PERSON> ceny", "flatRate": "Pevná sazba", "calculated": "Vypočítaná", "list": {"noRecordsMessage": "Vytvořte region pro oblasti, ve kterých prodáváte."}, "toast": {"delete": "Region byl úspěšně smazán", "edit": "Úprava regionu byla uložena", "create": "Region byl úspěšně vytvořen", "countries": "Země regionu byly úspěšně aktualizovány"}, "shippingOption": {"createShippingOption": "Vytvořit možnost dopravy", "createShippingOptionHint": "Vytvořte novou možnost dopravy pro region.", "editShippingOption": "Upravit možnost dopravy", "fulfillmentMethod": "Metoda plnění", "type": {"outbound": "Odchozí", "outboundHint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to, pokud vytváříte možnost dopravy pro odesílání produktů zákazníkovi.", "return": "Vrácení", "returnHint": "Po<PERSON><PERSON><PERSON><PERSON><PERSON> to, pokud vytváříte možnost dopravy pro vrácení produktů zákazníkem."}, "priceType": {"label": "<PERSON><PERSON> ceny", "flatRate": "Pevná sazba", "calculated": "Vypočítaná"}, "availability": {"adminOnly": "Pouze pro administrátory", "adminOnlyHint": "<PERSON><PERSON><PERSON> je povoleno, možnost dopravy bude dostupná pouze v administrativním dashboardu, a ne v obchodě."}, "taxInclusiveHint": "<PERSON><PERSON><PERSON> je povoleno, cena možnosti dopravy bude zahrnovat daň.", "requirements": {"label": "Požadavky", "hint": "Určete požadavky pro možnost dopravy."}}}, "taxes": {"domain": "Daňové regiony", "domainDescription": "Spravujte svůj daňový region", "countries": {"taxCountriesHint": "Daňová nastavení se vztahují na uvedené země."}, "settings": {"editTaxSettings": "<PERSON><PERSON><PERSON><PERSON> nastavení", "taxProviderLabel": "Poskytovatel daní", "systemTaxProviderLabel": "Systémový poskytovatel daní", "calculateTaxesAutomaticallyLabel": "Automaticky vypočítávat daně", "calculateTaxesAutomaticallyHint": "<PERSON><PERSON><PERSON> je povolen<PERSON>, da<PERSON><PERSON><PERSON> sazby budou automaticky vypočítány a aplikovány na košíky. <PERSON><PERSON><PERSON> je <PERSON>, dan<PERSON> musí být ručně vypočítány při pokladně. Ruč<PERSON><PERSON> daně jsou doporučeny pro použití s poskytovateli třetích stran.", "applyTaxesOnGiftCardsLabel": "Aplikovat daně na dárkové karty", "applyTaxesOnGiftCardsHint": "<PERSON><PERSON><PERSON>e povolen<PERSON>, da<PERSON><PERSON> budou aplikovány na dárkové karty při pokladně. V některých zemích vyžadují daňové předpisy aplikaci daní na dárkové karty při nákupu.", "defaultTaxRateLabel": "Výchozí <PERSON> sazba", "defaultTaxCodeLabel": "Výchozí daňový kód"}, "defaultRate": {"sectionTitle": "Výchozí <PERSON> sazba"}, "taxRate": {"sectionTitle": "Daňové sazby", "createTaxRate": "Vytvořit daňovou sazbu", "createTaxRateHint": "Vytvořte novou daňovou sazbu pro region.", "deleteRateDescription": "Chystáte se smazat daňovou sazbu {{name}}. <PERSON><PERSON> a<PERSON> ne<PERSON>ze vrátit zpět.", "editRateAction": "Upravit sazbu", "editOverridesAction": "Upravit přepsání", "editOverridesTitle": "Upravit přepsání daňové sazby", "editOverridesHint": "Určete přepsání pro daňovou sazbu.", "deleteTaxRateWarning": "Chystáte se smazat daňovou sazbu {{name}}. <PERSON><PERSON> a<PERSON> ne<PERSON>ze vrátit zpět.", "productOverridesLabel": "Přepsání produktů", "productOverridesHint": "Určete přepsání produktů pro daňovou sazbu.", "addProductOverridesAction": "Přidat přepsání produktů", "productTypeOverridesLabel": "Přepsání typů produktů", "productTypeOverridesHint": "Určete přepsání typů produktů pro daňovou sazbu.", "addProductTypeOverridesAction": "Přidat přepsání typů produktů", "shippingOptionOverridesLabel": "Přepsání možností dopravy", "shippingOptionOverridesHint": "Určete přepsání možností dopravy pro daňovou sazbu.", "addShippingOptionOverridesAction": "Přidat přepsání možností dopravy", "productOverridesHeader": "Produkty", "productTypeOverridesHeader": "Typy produktů", "shippingOptionOverridesHeader": "Možnosti dopravy"}}, "locations": {"domain": "<PERSON><PERSON>", "editLocation": "Upravit lo<PERSON>", "addSalesChannels": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "noLocationsFound": "Nebyly nalezeny žádné lo<PERSON>ce", "selectLocations": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> skladují položku.", "deleteLocationWarning": "<PERSON><PERSON>t<PERSON><PERSON> se smazat lokaci {{name}}. <PERSON><PERSON> a<PERSON> ne<PERSON> vrátit zpět.", "removeSalesChannelsWarning_one": "Chystáte se odstranit {{count}} prodejní kanál z lokace.", "removeSalesChannelsWarning_other": "Chystáte se odstranit {{count}} pro<PERSON><PERSON><PERSON><PERSON> ka<PERSON> z lokace.", "toast": {"create": "Lokace byla úspěšně vytvořena", "update": "Lokace byla úspěšně aktualizována", "removeChannel": "Prodejní kanál byl úspěšně odstraněn"}}, "reservations": {"domain": "Rezervace", "subtitle": "Spravujte rezervované množství inventárních <PERSON>žek.", "deleteWarning": "Chystáte se smazat rezervaci. <PERSON>to a<PERSON> nelze vrátit zpět."}, "salesChannels": {"domain": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "subtitle": "Spravujte online a offline kanály, na kterých prodáváte produkty.", "list": {"empty": {"heading": "Nebyly nalezeny žádné prodejní kanály", "description": "<PERSON><PERSON><PERSON> bude vytvoř<PERSON> pro<PERSON>, zobrazí se zde."}, "filtered": {"heading": "Žádné v<PERSON>dky", "description": "<PERSON><PERSON><PERSON><PERSON> prode<PERSON> kanály neodpovídají aktuálním filtrům."}}, "createSalesChannel": "Vytvořit prodejní kanál", "createSalesChannelHint": "Vytvořte nový prodejní kanál pro prodej va<PERSON>ich produktů.", "enabledHint": "<PERSON><PERSON><PERSON><PERSON>, zda je prodejní kanál povolen.", "removeProductsWarning_one": "Chystáte se odstranit {{count}} produkt z {{sales_channel}}.", "removeProductsWarning_other": "Chystáte se odstranit {{count}} produktů z {{sales_channel}}.", "addProducts": "Přidat produkty", "editSalesChannel": "Upravit pro<PERSON><PERSON> ka<PERSON>l", "productAlreadyAdded": "Produkt již byl přidán do prodejního kanálu.", "deleteSalesChannelWarning": "<PERSON><PERSON>t<PERSON><PERSON> se smazat prode<PERSON> ka<PERSON> {{name}}. <PERSON><PERSON> a<PERSON> ne<PERSON>ze vrátit zpět.", "toast": {"create": "Prodejní kanál byl úspěšně vytvořen", "update": "Prodejní kanál byl úspěšně aktualizován", "delete": "Prodejní kanál byl úspěšně s<PERSON>"}, "tooltip": {"cannotDeleteDefault": "Nelze smazat výchozí prodejní kanál"}, "products": {"list": {"noRecordsMessage": "V prodejním kanálu nejsou žádné produkty."}, "add": {"list": {"noRecordsMessage": "Nejprve vytvořte produkt."}}}}, "apiKeyManagement": {"domain": {"publishable": "Publikovatelné API klíče", "secret": "Tajné <PERSON> klíče"}, "subtitle": {"publishable": "Spravujte API klíče používané v obchodě pro omezení rozsahu požadavků na specifické prodejní kanály.", "secret": "Spravujte API klíče používané k autentizaci administrativních uživatelů v administrativních aplikacích."}, "status": {"active": "Aktivní", "revoked": "Odvoláno"}, "type": {"publishable": "Publikovatelné", "secret": "<PERSON><PERSON><PERSON>"}, "create": {"createPublishableHeader": "Vytvořit publikovatelný API klíč", "createPublishableHint": "Vytvořte nový publikovatelný API klíč pro omezení roz<PERSON>hu požadavků na specifické prodejní kanály.", "createSecretHeader": "Vytvořit tajný API klíč", "createSecretHint": "Vytvořte nový tajný API klíč pro přístup k Medusa API jako autentizovaný administrativní uživatel.", "secretKeyCreatedHeader": "<PERSON><PERSON><PERSON> k<PERSON>l vytvoř<PERSON>", "secretKeyCreatedHint": "Váš nový tajný klíč byl vygenerován. Zkopírujte a bezpečně uložte nyní. Toto je jedin<PERSON>, k<PERSON> bude z<PERSON>n.", "copySecretTokenSuccess": "Tajný klíč byl zkopírován do schránky.", "copySecretTokenFailure": "Nepodařilo se zkopírovat tajný klíč do schránky.", "successToast": "API klíč byl úspěšně vytvořen."}, "edit": {"header": "Upravit API klíč", "description": "Upravit název API klíče.", "successToast": "API klíč {{title}} byl úspěšně aktualizován."}, "salesChannels": {"title": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "description": "Přidat prodejní ka<PERSON>, na které by m<PERSON><PERSON> b<PERSON>t API klíč omezen.", "successToast_one": "{{count}} pro<PERSON><PERSON><PERSON> kanál byl úspěšně přidán do API klíče.", "successToast_other": "{{count}} pro<PERSON><PERSON><PERSON><PERSON> ka<PERSON> bylo <PERSON>š<PERSON> přidáno do API klíče.", "alreadyAddedTooltip": "Prodejní kanál již byl přidán do API klíče.", "list": {"noRecordsMessage": "V rozsahu publikovatelného API klíče nejsou žádné prodejní ka<PERSON>."}}, "delete": {"warning": "Chystáte se smazat API klíč {{title}}. <PERSON><PERSON> a<PERSON> ne<PERSON>ze vrátit zpět.", "successToast": "API klíč {{title}} byl ú<PERSON><PERSON>š<PERSON><PERSON> s<PERSON>."}, "revoke": {"warning": "Chystáte se odvolat API klíč {{title}}. <PERSON><PERSON> a<PERSON> ne<PERSON> vrátit zpět.", "successToast": "API klíč {{title}} byl úspěšně odvolán."}, "addSalesChannels": {"list": {"noRecordsMessage": "Nejprve vytvořte prodejní kanál."}}, "removeSalesChannel": {"warning": "Chystáte se odstranit prodejní kanál {{name}} z API klíče. Tuto akci nelze vrátit zpět.", "warningBatch_one": "Chystáte se odstranit {{count}} prodejní kanál z API klíče. Tuto akci nelze vrátit zpět.", "warningBatch_other": "Chystáte se odstranit {{count}} pro<PERSON><PERSON><PERSON><PERSON> ka<PERSON> z API klíče. <PERSON>to akci nelze vrátit zpět.", "successToast": "Prodejní kanál byl úspěšně odstraněn z API klíče.", "successToastBatch_one": "{{count}} pro<PERSON><PERSON><PERSON> kanál byl úspěšně odstraněn z API klíče.", "successToastBatch_other": "{{count}} pro<PERSON><PERSON><PERSON><PERSON> ka<PERSON> bylo <PERSON>sp<PERSON>šně odstraněno z API klíče."}, "actions": {"revoke": "Odvolat API klíč", "copy": "Zkopírovat API klíč", "copySuccessToast": "API klíč byl zkopírován do schránky."}, "table": {"lastUsedAtHeader": "Poslední použití", "createdAtHeader": "Odvoláno dne"}, "fields": {"lastUsedAtLabel": "Poslední použití", "revokedByLabel": "Odvol<PERSON><PERSON> k<PERSON>", "revokedAtLabel": "Odvoláno dne", "createdByLabel": "Vytvořeno kým"}}, "returnReasons": {"domain": "Důvody vrácení", "subtitle": "Spravujte důvody pro vrácené položky.", "calloutHint": "Spravujte důvody pro kategorizaci vrácení.", "editReason": "Upravit důvod vrácení", "create": {"header": "Přidat důvod vrácení", "subtitle": "Určete nejčastější důvody pro vrácení.", "hint": "Vytvořte nový důvod vrácení pro kategorizaci vrácení.", "successToast": "Důvod vrácení {{label}} byl <PERSON><PERSON> v<PERSON>."}, "edit": {"header": "Upravit důvod vrácení", "subtitle": "Upravit hodnotu důvodu vrácení.", "successToast": "Důvod vrácení {{label}} byl úspěšně aktualizová<PERSON>."}, "delete": {"confirmation": "Chystáte se smazat důvod vrácení {{label}}. <PERSON><PERSON> akci nelze vrátit zpět.", "successToast": "Důvod vrácení {{label}} byl <PERSON><PERSON><PERSON>."}, "fields": {"value": {"label": "Hodnota", "placeholder": "špatná_velikost", "tooltip": "Hodnota by m<PERSON><PERSON> b<PERSON><PERSON> j<PERSON>ý identifikátor pro důvod vrácení."}, "label": {"label": "Štítek", "placeholder": "Špatná velikost"}, "description": {"label": "<PERSON><PERSON>", "placeholder": "Zákazník obdržel špatnou velikost"}}}, "login": {"forgotPassword": "Zapomněli jste he<PERSON>lo? - <0>O<PERSON><PERSON><PERSON></0>", "title": "Vítejte v Meduse", "hint": "Přihlaste se pro přístup do oblasti účtu"}, "invite": {"title": "Vítejte v Meduse", "hint": "Vytvořte si svůj účet níže", "backToLogin": "Zpět na přihlášení", "createAccount": "Vytvořit účet", "alreadyHaveAccount": "<PERSON><PERSON> máte účet? - <0>Přihlásit se</0>", "emailTooltip": "Váš e-mail nelze změnit. Pokud byste chtěli p<PERSON>ít jiný e-mail, musí být odeslána nová pozvánka.", "invalidInvite": "Pozvánka je neplatná nebo vypršela.", "successTitle": "<PERSON><PERSON><PERSON>l zaregistro<PERSON>", "successHint": "Začněte s <PERSON><PERSON>a <PERSON>min hned teď.", "successAction": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invalidTokenTitle": "<PERSON><PERSON><PERSON> je nep<PERSON>", "invalidTokenHint": "Zkuste požádat o nový odkaz na pozvánku.", "passwordMismatch": "<PERSON><PERSON> se neshoduj<PERSON>", "toast": {"accepted": "Pozvánka byla úspěšně přijata"}}, "resetPassword": {"title": "<PERSON><PERSON><PERSON><PERSON> heslo", "hint": "Zadejte svůj e-mail níže a my vám zašleme pokyny, jak obnovit heslo.", "email": "E-mail", "sendResetInstructions": "Odeslat pokyny k obnovení", "backToLogin": "<0>Zpět na přihlášení</0>", "newPasswordHint": "Vyberte nové he<PERSON>.", "invalidTokenTitle": "<PERSON><PERSON><PERSON> resetovac<PERSON> token je nep<PERSON>", "invalidTokenHint": "Zkuste požádat o nový odkaz na reset.", "expiredTokenTitle": "<PERSON><PERSON><PERSON> reset<PERSON><PERSON><PERSON> token vypr<PERSON><PERSON>", "goToResetPassword": "Přejít na obnovení hesla", "resetPassword": "<PERSON><PERSON><PERSON><PERSON> heslo", "newPassword": "<PERSON><PERSON>", "repeatNewPassword": "Zopakovat nové he<PERSON>lo", "tokenExpiresIn": "Token vyprší za <0>{{time}}</0> minut", "successfulRequestTitle": "Úspěšně jsme vám zaslali e-mail", "successfulRequest": "Poslali jsme vám e-mail, který můžete použít k obnovení hesla. Zkontrolujte složku se spamem, pokud jste jej <PERSON>dr<PERSON> po několika minutách.", "successfulResetTitle": "Obnoven<PERSON> hesla <PERSON>", "successfulReset": "Přihlaste se na přihlašovací stránce.", "passwordMismatch": "<PERSON><PERSON> se neshoduj<PERSON>", "invalidLinkTitle": "Váš odkaz na reset je neplatný", "invalidLinkHint": "Zkuste znovu obnovit své he<PERSON>lo."}, "workflowExecutions": {"domain": "Pracovní postupy", "subtitle": "Zobrazit a sledovat provádění pracovních postupů ve vaší aplikaci Medusa.", "transactionIdLabel": "ID transakce", "workflowIdLabel": "ID pracovního postupu", "progressLabel": "Pokrok", "stepsCompletedLabel_one": "{{completed}} z {{count}} kroku", "stepsCompletedLabel_other": "{{completed}} z {{count}} kroků", "list": {"noRecordsMessage": "Zatím nebyly provedeny žádné pracovní postupy."}, "history": {"sectionTitle": "Historie", "runningState": "Probíhá...", "awaitingState": "Čeká se", "failedState": "<PERSON><PERSON><PERSON><PERSON>", "skippedState": "Přeskočeno", "skippedFailureState": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Selhání)", "definitionLabel": "Definice", "outputLabel": "<PERSON><PERSON><PERSON><PERSON>", "compensateInputLabel": "Kompenzovat vstup", "revertedLabel": "Vráceno", "errorLabel": "Chyba"}, "state": {"done": "Hotovo", "failed": "<PERSON><PERSON><PERSON><PERSON>", "reverted": "Vráceno", "invoking": "Vyvolávání", "compensating": "Kompenzování", "notStarted": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "transaction": {"state": {"waitingToCompensate": "Čeká se na kompenzaci"}}, "step": {"state": {"skipped": "Přeskočeno", "skippedFailure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Selhání)", "dormant": "Nečinné", "timeout": "Časový limit"}}}, "productTypes": {"domain": "Typy produktů", "subtitle": "Organizujte své produkty do typů.", "create": {"header": "Vytvořit typ produktu", "hint": "Vytvořte nový typ produktu pro kategorizaci vašich produktů.", "successToast": "Typ produktu {{value}} byl <PERSON><PERSON>š<PERSON> v<PERSON>."}, "edit": {"header": "Upravit typ produktu", "successToast": "Typ produktu {{value}} byl úsp<PERSON>šn<PERSON> aktualiz<PERSON>."}, "delete": {"confirmation": "Chystáte se smazat typ produktu {{value}}. <PERSON><PERSON> a<PERSON> ne<PERSON>ze vrátit zpět.", "successToast": "Typ produktu {{value}} byl <PERSON><PERSON><PERSON><PERSON><PERSON>."}, "fields": {"value": "Hodnota"}}, "productTags": {"domain": "Štítky produktů", "create": {"header": "Vytvořit štítek produktu", "subtitle": "Vytvořte nový štítek produktu pro kategorizaci vašich produktů.", "successToast": "Štítek produktu {{value}} byl <PERSON><PERSON><PERSON><PERSON><PERSON>."}, "edit": {"header": "Upravit štítek produktu", "subtitle": "Upravit hodnotu štítku produktu.", "successToast": "Štítek produktu {{value}} byl <PERSON><PERSON>š<PERSON> aktual<PERSON>."}, "delete": {"confirmation": "Chystáte se smazat štítek produktu {{value}}. <PERSON><PERSON> a<PERSON> ne<PERSON>ze vrátit zpět.", "successToast": "Štítek produktu {{value}} byl <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>."}, "fields": {"value": "Hodnota"}}, "notifications": {"domain": "Oznámení", "emptyState": {"title": "Žádná oznámení", "description": "Moment<PERSON>ln<PERSON> nem<PERSON>te <PERSON>, ale jak<PERSON> n<PERSON> budete mít, budou z<PERSON>."}, "accessibility": {"description": "oznámení o aktivitách Medusy budou zde uvedena."}}, "errors": {"serverError": "Chyba serveru - Zkuste to později.", "invalidCredentials": "Špatný e-mail nebo heslo"}, "statuses": {"scheduled": "<PERSON><PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "active": "Aktivní", "inactive": "Neaktivní", "draft": "Koncept", "enabled": "Povoleno", "disabled": "Zak<PERSON><PERSON><PERSON><PERSON>"}, "labels": {"productVariant": "Varianta produktu", "prices": "<PERSON><PERSON>", "available": "Dostupné", "inStock": "Na skladě", "added": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "removed": "Odstraněno", "from": "Od", "to": "Do", "beaware": "Buďte opatrní", "loading": "Načítání"}, "fields": {"amount": "Částka", "reference": "Reference", "reference_id": "ID reference", "refundAmount": "Částka k vrácení", "name": "<PERSON><PERSON><PERSON><PERSON>", "default": "Výchozí", "lastName": "Příjmení", "firstName": "Jméno", "title": "<PERSON><PERSON><PERSON><PERSON>", "customTitle": "Vlast<PERSON><PERSON>", "manageInventory": "Spravovat inventář", "inventoryKit": "<PERSON><PERSON> sadu in<PERSON>", "inventoryItems": "Inventární <PERSON>", "inventoryItem": "Inventární položka", "requiredQuantity": "Požadované množství", "description": "<PERSON><PERSON>", "email": "E-mail", "password": "He<PERSON><PERSON>", "repeatPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON>", "confirmPassword": "Potvrdit heslo", "newPassword": "<PERSON><PERSON>", "repeatNewPassword": "Zopakovat nové he<PERSON>lo", "categories": "<PERSON><PERSON><PERSON>", "shippingMethod": "Metoda dopravy", "configurations": "Konfigurace", "conditions": "Podm<PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "collection": "<PERSON><PERSON><PERSON><PERSON>", "discountable": "<PERSON><PERSON><PERSON><PERSON>", "handle": "<PERSON><PERSON>", "subtitle": "Podtitul", "by": "Od", "item": "Položka", "qty": "množství", "limit": "Limit", "tags": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON>", "reason": "Důvod", "none": "<PERSON><PERSON><PERSON><PERSON>", "all": "vše", "search": "Hledat", "percentage": "Procento", "sales_channels": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "customer_groups": "Skupiny zákazníků", "product_tags": "Štítky produktů", "product_types": "Typy produktů", "product_collections": "<PERSON><PERSON><PERSON><PERSON> produktů", "status": "Stav", "code": "<PERSON><PERSON><PERSON>", "value": "Hodnota", "disabled": "Zak<PERSON><PERSON><PERSON><PERSON>", "dynamic": "Dynamický", "normal": "Normální", "years": "Roky", "months": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "days": "Dny", "hours": "<PERSON><PERSON><PERSON>", "minutes": "<PERSON><PERSON><PERSON>", "totalRedemptions": "Celkový počet uplatnění", "countries": "Země", "paymentProviders": "Poskytovatelé plateb", "refundReason": "Důvod vrácení peněz", "fulfillmentProviders": "Poskytovatelé plnění", "fulfillmentProvider": "Poskytovatel plnění", "providers": "Poskytovatelé", "availability": "Dostupnost", "inventory": "Inventář", "optional": "Volitelné", "note": "Poznámka", "automaticTaxes": "<PERSON><PERSON><PERSON>", "taxInclusivePricing": "Ceny včetně daně", "currency": "Měna", "address": "<PERSON><PERSON><PERSON>", "address2": "Byt, apartmán, atd.", "city": "<PERSON><PERSON><PERSON>", "postalCode": "PSČ", "country": "Země", "state": "<PERSON><PERSON><PERSON>", "province": "<PERSON><PERSON><PERSON>", "company": "Společnost", "phone": "Telefon", "metadata": "<PERSON><PERSON><PERSON>", "selectCountry": "<PERSON><PERSON><PERSON><PERSON>i", "products": "Produkty", "variants": "Varianty", "orders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "account": "Účet", "total": "Ce<PERSON>ová částka objednávky", "paidTotal": "Celková zaplacená částka", "creditTotal": "<PERSON><PERSON>ová částka kreditu", "totalExclTax": "<PERSON><PERSON>ová částka bez daně", "subtotal": "Mezisoučet", "shipping": "<PERSON><PERSON><PERSON>", "outboundShipping": "Odchozí doprava", "returnShipping": "Vrácení dopravy", "tax": "Daň", "created": "Vytvořeno", "key": "<PERSON><PERSON><PERSON><PERSON>", "customer": "Zákazník", "date": "Datum", "order": "Objednávka", "fulfillment": "Plnění", "provider": "Poskytovatel", "payment": "Platba", "items": "Polož<PERSON>", "salesChannel": "Prodejní ka<PERSON>l", "region": "Region", "discount": "<PERSON><PERSON><PERSON>", "role": "Role", "sent": "Odesláno", "salesChannels": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "product": "Produkt", "createdAt": "Vytvořeno", "updatedAt": "Aktualizováno", "revokedAt": "Odvoláno dne", "true": "Pravda", "false": "Nepravda", "giftCard": "<PERSON><PERSON><PERSON><PERSON> karta", "tag": "Štítek", "dateIssued": "<PERSON><PERSON>", "issuedDate": "<PERSON><PERSON>", "expiryDate": "Datum vypršení", "price": "<PERSON><PERSON>", "priceTemplate": "Cena {{regionOrCurrency}}", "height": "Výška", "width": "Šířka", "length": "<PERSON><PERSON><PERSON><PERSON>", "weight": "Hmotnost", "midCode": "MID kód", "hsCode": "HS kód", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "Množství inventáře", "barcode": "Čárový kód", "countryOfOrigin": "Země <PERSON>", "material": "<PERSON><PERSON><PERSON><PERSON>", "thumbnail": "Miniatura", "sku": "SKU", "managedInventory": "S<PERSON>vovaný inventář", "allowBackorder": "Povolit zpětnou objednávku", "inStock": "Na skladě", "location": "<PERSON><PERSON>", "quantity": "Množství", "variant": "<PERSON><PERSON><PERSON>", "id": "ID", "parent": "<PERSON><PERSON><PERSON>", "minSubtotal": "<PERSON><PERSON>", "maxSubtotal": "<PERSON><PERSON>", "shippingProfile": "<PERSON>il dopra<PERSON>", "summary": "Souhrn", "details": "Detaily", "label": "Štítek", "rate": "Sazba", "requiresShipping": "Vyžaduje dopravu", "unitPrice": "<PERSON><PERSON><PERSON><PERSON> cena", "startDate": "Datum z<PERSON>čá<PERSON>", "endDate": "<PERSON><PERSON> konce", "draft": "Koncept", "values": "Hodnoty"}, "quotes": {"domain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "Spravujte nabídky a návrhy zákazníků", "noQuotes": "Nebyly nalezeny žádné na<PERSON>", "noQuotesDescription": "Momentálně nejsou žádné nabídky. Vytvořte jednu z obchodu.", "table": {"id": "<PERSON> nabídky", "customer": "Zákazník", "status": "Stav", "company": "Společnost", "amount": "Částka", "createdAt": "Vytvořeno", "updatedAt": "Aktualizováno", "actions": "Ak<PERSON>"}, "status": {"pending_merchant": "Čeká na obchodníka", "pending_customer": "Čeká na zákazníka", "merchant_rejected": "Odmít<PERSON><PERSON>", "customer_rejected": "Odmítnut<PERSON>", "accepted": "<PERSON><PERSON><PERSON><PERSON>", "unknown": "<PERSON><PERSON><PERSON><PERSON>"}, "actions": {"sendQuote": "Odeslat nabídku", "rejectQuote": "Odmítnout nabídku", "viewOrder": "Zobrazit objednávku"}, "details": {"header": "<PERSON><PERSON><PERSON>", "quoteSummary": "<PERSON><PERSON><PERSON>", "customer": "Zákazník", "company": "Společnost", "items": "Polož<PERSON>", "total": "<PERSON><PERSON><PERSON>", "subtotal": "Mezisoučet", "shipping": "<PERSON><PERSON><PERSON>", "tax": "Daň", "discounts": "<PERSON><PERSON><PERSON>", "originalTotal": "Původní celkem", "quoteTotal": "<PERSON><PERSON><PERSON>", "messages": "Zprávy", "actions": "Ak<PERSON>", "sendMessage": "Odeslat zprávu", "send": "<PERSON><PERSON><PERSON>", "pickQuoteItem": "Vybrat položku nabídky", "selectQuoteItem": "Vyberte položku nabídky pro komentář", "selectItem": "Vybrat <PERSON>", "manage": "Spravovat", "phone": "Telefon", "spendingLimit": "<PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "manageQuote": "Spravovat nabídku", "noItems": "V této nabídce nejsou žádné <PERSON>ž<PERSON>", "noMessages": "Pro tuto nabídku nejsou žádné zprávy"}, "items": {"title": "Produkt", "quantity": "Množství", "unitPrice": "<PERSON><PERSON><PERSON><PERSON> cena", "total": "<PERSON><PERSON><PERSON>"}, "messages": {"admin": "Admini<PERSON><PERSON><PERSON><PERSON>", "customer": "Zákazník", "placeholder": "Napište svou zprávu zde..."}, "filters": {"status": "Filtrovat podle stavu"}, "confirmations": {"sendTitle": "Odeslat nabídku", "sendDescription": "J<PERSON> si jisti, že chcete o<PERSON> tuto nabídku zákazníkovi?", "rejectTitle": "Odmítnout nabídku", "rejectDescription": "Jste si jisti, že chcete odmítnout tuto nabídku?"}, "acceptance": {"message": "Nabíd<PERSON> byla <PERSON>"}, "toasts": {"sendSuccess": "Nabídka byla úspěšně odeslána zákazníkovi", "sendError": "Odeslání nabídky se nezdařilo", "rejectSuccess": "Nabídka zákazníka byla úspěšně odmítnuta", "rejectError": "Odmítnutí nabídky se nezdařilo", "messageSuccess": "Zpráva byla úsp<PERSON>šně odeslána zákazníkovi", "messageError": "Odeslání zprávy se nezdařilo", "updateSuccess": "Nabíd<PERSON> byla úspěšně aktualizována"}, "manage": {"overridePriceHint": "Přepsat původní cenu pro tuto položku", "updatePrice": "Aktualizovat cenu"}}, "companies": {"domain": "Společnosti", "title": "Společnosti", "subtitle": "Spravujte obchodní vztahy", "noCompanies": "Nebyly nalezeny žádné společnosti", "noCompaniesDescription": "Vytvořte svou první společnost pro začátek.", "notFound": "Společnost nebyla nalezena", "table": {"name": "<PERSON><PERSON><PERSON><PERSON>", "phone": "Telefon", "email": "E-mail", "address": "<PERSON><PERSON><PERSON>", "employees": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customerGroup": "<PERSON><PERSON><PERSON>", "actions": "Ak<PERSON>"}, "fields": {"name": "Název společnosti", "email": "E-mail", "phone": "Telefon", "website": "<PERSON><PERSON> str<PERSON>", "address": "<PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON>", "state": "<PERSON><PERSON><PERSON>", "zip": "PSČ", "zipCode": "PSČ", "country": "Země", "currency": "Měna", "logoUrl": "URL loga", "description": "<PERSON><PERSON>", "employees": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customerGroup": "<PERSON><PERSON><PERSON>", "approvalSettings": "Nastavení s<PERSON>ování"}, "placeholders": {"name": "Zadejte název společnosti", "email": "Zadejte e-mailovou adresu", "phone": "Zadejte telefonní číslo", "website": "Zadejte URL webové str<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON>", "city": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "state": "<PERSON>adej<PERSON> st<PERSON>", "zip": "Zadejte PSČ", "logoUrl": "Zadejte URL loga", "description": "Zadejte popis společnosti", "selectCountry": "<PERSON><PERSON><PERSON><PERSON>i", "selectCurrency": "<PERSON><PERSON><PERSON><PERSON>"}, "validation": {"nameRequired": "Název společnosti je povinný", "emailRequired": "E-mail je povinný", "emailInvalid": "Neplatná e-mailová adresa", "addressRequired": "<PERSON><PERSON><PERSON> je povinná", "cityRequired": "<PERSON><PERSON><PERSON> je povinné", "stateRequired": "<PERSON><PERSON><PERSON> je povinný", "zipRequired": "PSČ je povinné"}, "create": {"title": "Vytvořit společnost", "description": "Vytvořte novou společnost pro správu obchodních vztahů.", "submit": "Vytvořit společnost"}, "edit": {"title": "Upravit společnost", "submit": "Aktualizovat společnost"}, "details": {"actions": "Ak<PERSON>"}, "approvals": {"requiresAdminApproval": "Vyžaduje schválení administrátora", "requiresSalesManagerApproval": "Vyžaduje schválení manažera prodeje", "noApprovalRequired": "Nevyžaduje schválení"}, "deleteWarning": "Tím se trvale smaže společnost a všechna související data.", "approvalSettings": {"title": "Nastavení s<PERSON>ování", "requiresAdminApproval": "Vyžaduje schválení administrátora", "requiresSalesManagerApproval": "Vyžaduje schválení manažera prodeje", "requiresAdminApprovalDesc": "Objednávky od této společnosti vyžadují schválení administrátora před zpracováním", "requiresSalesManagerApprovalDesc": "Objednávky od této společnosti vyžadují schválení manažera prodeje před zpracováním", "updateSuccess": "Nastavení schvalování bylo úspěšně aktualizováno", "updateError": "Aktualizace nastavení schvalování se nezdařila"}, "customerGroup": {"title": "Spravovat skupinu zákazníků", "hint": "Přiřaďte tuto společnost ke skupině zákazníků pro použití skupinových cen a oprávnění.", "name": "Název skupiny zákazníků", "groupName": "<PERSON><PERSON><PERSON>", "actions": "Ak<PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON><PERSON>", "description": "Spravujte skupiny zákazníků pro tuto společnost", "noGroups": "Nejsou k dispozici žádné skupiny zákazníků", "addSuccess": "Společnost byla úspěšně přidána do skupiny zákazníků", "addError": "Přidání společnosti do skupiny zákazníků se nezdařilo", "removeSuccess": "Společnost byla úspěšně odebrána ze skupiny zákazníků", "removeError": "Odebrání společnosti ze skupiny zákazníků se nezdařilo"}, "actions": {"edit": "Upravit společnost", "editDetails": "Up<PERSON>vit podrobnosti", "manageCustomerGroup": "Spravovat skupinu zákazníků", "approvalSettings": "Nastavení s<PERSON>ování", "delete": "Smazat společnost", "confirmDelete": "Potvrdit smazání"}, "delete": {"title": "Smazat společnost", "description": "Jste si jisti, že chcete smazat tuto s<PERSON>čnost? <PERSON>to akci nelze vrátit zpět."}, "employees": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noEmployees": "Pro tuto společnost nebyli nalezeni žádní zaměstnanci", "name": "Jméno", "email": "E-mail", "phone": "Telefon", "role": "Role", "spendingLimit": "<PERSON><PERSON>", "admin": "Admini<PERSON><PERSON><PERSON><PERSON>", "employee": "Zaměstnanec", "add": "Přida<PERSON>", "create": {"title": "Vytvořit zaměstnance", "success": "Zaměstnanec byl úspěšně vytvořen", "error": "Vytvoření zaměstnance se nezdařilo"}, "form": {"details": "Podrobné informace", "permissions": "Oprávnění", "firstName": "Jméno", "lastName": "Příjmení", "email": "E-mail", "phone": "Telefon", "spendingLimit": "<PERSON><PERSON>", "adminAccess": "Přístup administrátora", "isAdmin": "<PERSON> administrátor", "isAdminDesc": "<PERSON><PERSON><PERSON><PERSON> to<PERSON><PERSON> oprávnění administrátora", "isAdminTooltip": "Administrá<PERSON><PERSON><PERSON> mohou spravovat nastavení společnosti a ostatní zaměstnance", "firstNamePlaceholder": "Zadej<PERSON> j<PERSON>no", "lastNamePlaceholder": "Zadejte příjmení", "emailPlaceholder": "Zadejte e-mailovou adresu", "phonePlaceholder": "Zadejte telefonní číslo", "spendingLimitPlaceholder": "Zadejte limit vý<PERSON><PERSON>ů", "save": "Uložit", "saving": "Ukládání..."}, "delete": {"confirmation": "Jste si jisti, že chcete smazat tohoto zaměstnance?", "success": "Zaměstnanec byl úspěšně s<PERSON>"}, "edit": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "toasts": {"updateSuccess": "Zaměstnanec byl úspěšně aktualizován", "updateError": "Aktualizace zaměstnance se nezdařila"}}, "toasts": {"createSuccess": "Společnost byla úspěšně vytvořena", "createError": "Vytvoření společnosti se nezdařilo", "updateSuccess": "Společnost byla úspěšně aktualizována", "updateError": "Aktualizace společnosti se nezdařila", "deleteSuccess": "Společnost byla úspěšně s<PERSON>ána", "deleteError": "Smazání společnosti se nezdařilo"}}, "approvals": {"domain": "Schválení", "title": "Schválení", "subtitle": "Spravujte pracovní postupy schvalování", "noApprovals": "Nebyla nalezena žádná schválení", "noApprovalsDescription": "Momentálně nejsou žádná schválení k posouzení.", "table": {"id": "ID", "type": "<PERSON><PERSON>", "company": "Společnost", "customer": "Zákazník", "amount": "Částka", "status": "Stav", "createdAt": "Vytvořeno"}, "status": {"pending": "Čekající", "approved": "Schv<PERSON>len<PERSON>", "rejected": "Odmí<PERSON>nut<PERSON>", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unknown": "<PERSON><PERSON><PERSON><PERSON>"}, "details": {"header": "Podrobnosti schválení", "summary": "So<PERSON>rn schválení", "company": "Společnost", "customer": "Zákazník", "order": "Objednávka", "amount": "Částka", "updatedAt": "Aktualizováno", "reason": "Důvod", "actions": "Ak<PERSON>"}, "actions": {"approve": "Schv<PERSON><PERSON>", "reject": "Odmítnout", "confirmApprove": "Potvrdit schválení", "confirmReject": "Potvrdit odmítnutí", "reasonPlaceholder": "<PERSON><PERSON><PERSON><PERSON> (volitelné)..."}, "filters": {"status": "Filtrovat podle stavu"}, "toasts": {"approveSuccess": "Úspěšně schv<PERSON>o", "approveError": "Schválení se nezdařilo", "rejectSuccess": "Úspěšně odmítnuto", "rejectError": "Odmítnutí se nezdařilo"}}, "dateTime": {"years_one": "Rok", "years_other": "Roky", "months_one": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "months_other": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weeks_one": "<PERSON><PERSON><PERSON>", "weeks_other": "Týdny", "days_one": "Den", "days_other": "Dny", "hours_one": "Hodina", "hours_other": "<PERSON><PERSON><PERSON>", "minutes_one": "<PERSON>uta", "minutes_other": "<PERSON><PERSON><PERSON>", "seconds_one": "<PERSON><PERSON><PERSON>", "seconds_other": "Sekundy"}}