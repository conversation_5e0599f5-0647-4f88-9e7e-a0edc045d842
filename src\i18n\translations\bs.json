{"$schema": "./$schema.json", "general": {"ascending": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "descending": "Opadajuće", "add": "<PERSON><PERSON><PERSON>", "start": "Započni", "end": "<PERSON><PERSON>", "open": "Otvori", "close": "Zatvori", "apply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "range": "<PERSON><PERSON>", "search": "Pretraga", "of": "od", "results": "re<PERSON><PERSON><PERSON>", "pages": "stranice", "next": "Sljedeće", "prev": "Prethodno", "is": "je", "timeline": "Timeline", "success": "Us<PERSON><PERSON>šno", "warning": "Upozorenje", "tip": "Tip", "error": "Greška", "select": "<PERSON><PERSON><PERSON>", "selected": "Odabrano", "enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON><PERSON>", "active": "Aktivno", "revoked": "Opozvano", "new": "Novo", "modified": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "added": "Dodano", "removed": "Uklonjeno", "admin": "Admin", "store": "Trgovina", "details": "<PERSON><PERSON><PERSON>", "items_one": "{{count}} stavka", "items_other": "{{count}} stavke", "countSelected": "{{count}} odabrano", "countOfTotalSelected": "{{count}} of {{total}} odabranih", "plusCount": "+ {{count}}", "plusCountMore": "+ {{count}} vi<PERSON>e", "areYouSure": "Da li ste sigurni?", "areYouSureDescription": "Ž<PERSON>te li obrisati {{entity}} {{title}}. <PERSON>va radnja se ne može poništiti.", "noRecordsFound": "<PERSON><PERSON> pro<PERSON> z<PERSON>", "typeToConfirm": "Unesite {val} za potvrdu:", "noResultsTitle": "<PERSON><PERSON> rezultata", "noResultsMessage": "Pokušajte promijeniti filtere ili upit za pretraživanje", "noSearchResults": "Nema rezultata pretrage", "noSearchResultsFor": "Nema rezultata pretrage za <0>'{{query}}'</0>", "noRecordsTitle": "<PERSON><PERSON> rezultata", "noRecordsMessage": "Nema zapisa za prikaz", "unsavedChangesTitle": "Jeste li sigurni da želite napustiti ovaj obrazac?", "unsavedChangesDescription": "I<PERSON> ne<PERSON>vane promjene koje će biti izgubljene ako izađete iz ovog obrasca.", "includesTaxTooltip": "Cijene u ovoj koloni uključuju porez.", "excludesTaxTooltip": "Cijene u ovoj koloni ne uključuju porez.", "noMoreData": "Nema više podataka", "actions": "<PERSON><PERSON><PERSON><PERSON>"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}} k<PERSON><PERSON><PERSON>", "numberOfKeys_other": "{{count}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "drawer": {"header_one": "JSON <0>· {{count}} ključ</0>", "header_other": "JSON <0>· {{count}} k<PERSON><PERSON><PERSON><PERSON></0>", "description": "Pogledajte JSON podatke za ovaj objekt."}}, "metadata": {"header": "<PERSON><PERSON><PERSON><PERSON>", "numberOfKeys_one": "{{count}} k<PERSON><PERSON><PERSON>", "numberOfKeys_other": "{{count}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": {"header": "<PERSON><PERSON><PERSON>po<PERSON>", "description": "Uredite metapodatke za ovaj objekt.", "successToast": "Metapodaci su uspješno ažurirani.", "actions": {"insertRowAbove": "Umetni red iznad", "insertRowBelow": "Umetni red ispod", "deleteRow": "Obriši red"}, "labels": {"key": "K<PERSON><PERSON>č", "value": "Vrijednost"}, "complexRow": {"label": "Neki redovi su onemogućeni", "description": "Ovaj objekt sadrži neprimitivne metapodatke, kao što su nizovi ili objekti, koji se ne mogu uređivati ovdje. Da biste uredili onemogućene redove, koristite API direktno.", "tooltip": "Ovaj red je onemogućen jer sadrži neprimitivne podatke."}}}, "validation": {"mustBeInt": "Vrijednost mora biti cijeli broj.", "mustBePositive": "Vrijednost mora biti pozitivan broj."}, "actions": {"save": "Sačuvaj", "saveAsDraft": "Sačuvaj kao nacrt", "copy": "<PERSON><PERSON><PERSON>", "copied": "<PERSON><PERSON><PERSON>", "duplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "publish": "<PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Izbriši", "remove": "Ukloni", "revoke": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Ot<PERSON>ži", "forceConfirm": "<PERSON><PERSON><PERSON><PERSON>", "continueEdit": "Nastavi uređivanje", "enable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "undo": "<PERSON><PERSON><PERSON><PERSON>", "complete": "Zav<PERSON>š<PERSON>", "viewDetails": "<PERSON><PERSON><PERSON>", "back": "<PERSON><PERSON>", "close": "Zatvori", "showMore": "Prikaži više", "continue": "<PERSON><PERSON><PERSON>", "continueWithEmail": "Nastavi s e-poštom", "idCopiedToClipboard": "ID je kopiran u međuspremnik", "addReason": "<PERSON><PERSON><PERSON> raz<PERSON>", "addNote": "Dodaj <PERSON>", "reset": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Potvrdi", "edit": "<PERSON><PERSON><PERSON>", "addItems": "<PERSON><PERSON><PERSON> stav<PERSON>", "download": "<PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON><PERSON>", "clearAll": "Očisti sve", "apply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON>", "browse": "<PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON><PERSON> se", "hide": "<PERSON><PERSON><PERSON><PERSON>", "export": "Izvezi", "import": "<PERSON><PERSON><PERSON>", "cannotUndo": "Ova akcija se ne može poništiti"}, "operators": {"in": "U"}, "app": {"search": {"label": "Pretraga", "title": "Pretraga", "description": "Pretražite cijelu vašu trgovinu, uključujući narudžbe, proizvode, kupce i više.", "allAreas": "Sva područja", "navigation": "Navigacija", "openResult": "Otvori rezultat", "showMore": "Prikaži više", "placeholder": "Skočite na ili pronađite bilo šta...", "noResultsTitle": "<PERSON>ema prona<PERSON><PERSON> rezultata", "noResultsMessage": "Nismo pronašli ništa š<PERSON> odgovara vaš<PERSON>j pretrazi.", "emptySearchTitle": "Upišite za pretragu", "emptySearchMessage": "Unesite ključnu riječ ili frazu za istraživanje.", "loadMore": "<PERSON><PERSON><PERSON><PERSON> još {{count}}", "groups": {"all": "Sva područja", "customer": "<PERSON><PERSON><PERSON>", "customerGroup": "Grupe kupaca", "product": "Proizvodi", "productVariant": "Varijante proizvoda", "inventory": "Inventar", "reservation": "Rezervacije", "category": "Kategorije", "collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "order": "<PERSON><PERSON>ž<PERSON>", "promotion": "Promocije", "campaign": "Kampanje", "priceList": "Cjenovnici", "user": "<PERSON><PERSON><PERSON><PERSON>", "region": "Regije", "taxRegion": "Poreske regije", "returnReason": "Razlozi za povrat", "salesChannel": "<PERSON><PERSON><PERSON><PERSON> kanali", "productType": "Tipovi proizvoda", "productTag": "Oznake proizvoda", "location": "Lok<PERSON><PERSON><PERSON>", "shippingProfile": "<PERSON><PERSON>", "publishableApiKey": "Ključevi za objavljivanje API-a", "secretApiKey": "Tajni API ključevi", "command": "<PERSON><PERSON><PERSON>", "navigation": "Navigacija"}}, "keyboardShortcuts": {"pageShortcut": "Skoči na", "settingShortcut": "Postavke", "commandShortcut": "<PERSON><PERSON><PERSON>", "then": "zatim", "navigation": {"goToOrders": "<PERSON><PERSON>ž<PERSON>", "goToProducts": "Proizvodi", "goToCollections": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goToCategories": "Kategorije", "goToCustomers": "<PERSON><PERSON><PERSON>", "goToCustomerGroups": "Grupe kupaca", "goToInventory": "Inventar", "goToReservations": "Rezervacije", "goToPriceLists": "Cjenovnici", "goToPromotions": "Promocije", "goToCampaigns": "Kampanje"}, "settings": {"goToSettings": "Postavke", "goToStore": "Trgovina", "goToUsers": "<PERSON><PERSON><PERSON><PERSON>", "goToRegions": "Regije", "goToTaxRegions": "Poreske regije", "goToSalesChannels": "<PERSON><PERSON><PERSON><PERSON> kanali", "goToProductTypes": "Tipovi proizvoda", "goToLocations": "Lok<PERSON><PERSON><PERSON>", "goToPublishableApiKeys": "Ključevi za objavljivanje API-a", "goToSecretApiKeys": "Tajni API ključevi", "goToWorkflows": "Tokovi rada", "goToProfile": "Profil", "goToReturnReasons": "Razlozi za povrat"}}, "menus": {"user": {"documentation": "Dokumentacija", "changelog": "Dnevnik promjena", "shortcuts": "Prečice", "profileSettings": "Postavke profila", "theme": {"label": "<PERSON><PERSON>", "dark": "Tam<PERSON>", "light": "<PERSON><PERSON><PERSON><PERSON>", "system": "Sistem"}}, "store": {"label": "Trgovina", "storeSettings": "Postavke trgovine"}, "actions": {"logout": "<PERSON><PERSON><PERSON><PERSON> se"}}, "nav": {"accessibility": {"title": "Navigacija", "description": "Navigacijski meni za nadzornu ploču."}, "common": {"extensions": "Ekstenzije"}, "main": {"store": "Trgovina", "storeSettings": "Postavke trgovine"}, "settings": {"header": "Postavke", "general": "<PERSON><PERSON><PERSON>", "developer": "Razvoj", "myAccount": "<PERSON><PERSON>"}}}, "dataGrid": {"columns": {"view": "Prikaz", "resetToDefault": "Vrati na podrazumijevano", "disabled": "Promjena vidljivih kolona je onemogućena."}, "shortcuts": {"label": "Prečice", "commands": {"undo": "<PERSON><PERSON><PERSON><PERSON>", "redo": "<PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON>", "paste": "Zalijepi", "edit": "<PERSON><PERSON><PERSON>", "delete": "Izbriši", "clear": "<PERSON><PERSON><PERSON><PERSON>", "moveUp": "Pomjeri gore", "moveDown": "Pomjeri dolje", "moveLeft": "Pomjeri lijevo", "moveRight": "<PERSON><PERSON><PERSON><PERSON>", "moveTop": "Pomjeri na vrh", "moveBottom": "Pomjeri na dno", "selectDown": "Odaberi dolje", "selectUp": "Oda<PERSON>i gore", "selectColumnDown": "Odaberi kolonu dolje", "selectColumnUp": "<PERSON><PERSON><PERSON><PERSON> kolonu gore", "focusToolbar": "<PERSON><PERSON><PERSON><PERSON> al<PERSON> traku", "focusCancel": "Fokus ot<PERSON>ži"}}, "errors": {"fixError": "Popravi grešku", "count_one": "{{count}} g<PERSON><PERSON><PERSON>", "count_other": "{{count}} g<PERSON><PERSON><PERSON>"}}, "filters": {"sortLabel": "<PERSON><PERSON><PERSON><PERSON>", "filterLabel": "<PERSON><PERSON><PERSON><PERSON>", "searchLabel": "Pretraži", "date": {"today": "<PERSON><PERSON>", "lastSevenDays": "Posljednjih 7 dana", "lastThirtyDays": "Posljednjih 30 dana", "lastNinetyDays": "Posljednjih 90 dana", "lastTwelveMonths": "Posljednjih 12 mjeseci", "custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "from": "Od", "to": "Do", "starting": "Početak", "ending": "<PERSON><PERSON>"}, "compare": {"lessThan": "<PERSON>je od", "greaterThan": "Veće od", "exact": "Tačno", "range": "Opseg", "lessThanLabel": "manje od {{value}}", "greaterThanLabel": "veće od {{value}}", "andLabel": "i"}, "sorting": {"alphabeticallyAsc": "A do Z", "alphabeticallyDesc": "Z do A", "dateAsc": "Najnovije prvo", "dateDesc": "Najstarije prvo"}, "radio": {"yes": "Da", "no": "Ne", "true": "Tačno", "false": "Netačno"}, "addFilter": "<PERSON><PERSON><PERSON> filter"}, "errorBoundary": {"badRequestTitle": "400 - <PERSON><PERSON><PERSON><PERSON><PERSON>", "badRequestMessage": "Zahtjev nije mogao biti razumljen od strane servera zbog neispravne sintakse.", "notFoundTitle": "404 - Stranica na ovoj adresi ne postoji", "notFoundMessage": "Provjerite URL i pokušajte ponovo ili koristite traku za pretragu da pronađete ono što tražite.", "internalServerErrorTitle": "500 - Interna greška servera", "internalServerErrorMessage": "Došlo je do neočekivane greške na serveru. Molimo pokušajte ponovo kasnije.", "defaultTitle": "<PERSON><PERSON><PERSON> je do greške", "defaultMessage": "Došlo je do neočekivane greške prilikom prikazivanja ove stranice.", "noMatchMessage": "Stranica koju tražite ne postoji.", "backToDashboard": "Nazad na nadzornu ploču"}, "addresses": {"title": "<PERSON><PERSON><PERSON>", "shippingAddress": {"header": "<PERSON><PERSON><PERSON> za <PERSON>", "editHeader": "<PERSON><PERSON><PERSON> adresu za dos<PERSON>", "editLabel": "<PERSON><PERSON><PERSON> za <PERSON>", "label": "<PERSON><PERSON><PERSON> za <PERSON>"}, "billingAddress": {"header": "<PERSON><PERSON><PERSON> za naplatu", "editHeader": "Uredi adresu za naplatu", "editLabel": "<PERSON><PERSON><PERSON> za naplatu", "label": "<PERSON><PERSON><PERSON> za naplatu", "sameAsShipping": "<PERSON><PERSON> kao adresa za dostavu"}, "contactHeading": "Kontakt", "locationHeading": "Lokacija"}, "email": {"editHeader": "Uredi email", "editLabel": "Email", "label": "Email"}, "transferOwnership": {"header": "Prenos vlasništva", "label": "Prenos vlasništva", "details": {"order": "Detalji na<PERSON>ž<PERSON>", "draft": "Detalji nacrta"}, "currentOwner": {"label": "Trenutni vlasnik", "hint": "Trenutni vlasnik narudžbe."}, "newOwner": {"label": "Novi vlasnik", "hint": "Novi vlasnik na kojeg se prenosi narudžba."}, "validation": {"mustBeDifferent": "Novi vlasnik mora biti različit od trenutnog vlasnika.", "required": "Novi vlasnik je obavezan."}}, "sales_channels": {"availableIn": "Dostupno u <0>{{x}}</0> od <1>{{y}}</1> prodajn<PERSON> kanala"}, "products": {"domain": "Proizvodi", "list": {"noRecordsMessage": "Kreirajte svoj prvi proizvod da biste počeli prodavati."}, "edit": {"header": "Izmjena proizvoda", "description": "Izmijenite detalje proizvoda.", "successToast": "Proizvod {{title}} je us<PERSON><PERSON><PERSON><PERSON>."}, "create": {"title": "Kreiraj proizvod", "description": "Kreirajte novi proizvod.", "header": "Opšte", "tabs": {"details": "<PERSON><PERSON><PERSON>", "organize": "Organizujte", "variants": "<PERSON><PERSON><PERSON><PERSON>", "inventory": "Kits inventara"}, "errors": {"variants": "Molimo odaberite barem jednu varijantu.", "options": "<PERSON><PERSON><PERSON> barem jednu opciju.", "uniqueSku": "SKU mora biti jedinstven."}, "inventory": {"heading": "Kits inventara", "label": "Dodajte inventarne stavke u kit inventara varijante.", "itemPlaceholder": "Odaberite inventarnu stavku", "quantityPlaceholder": "<PERSON><PERSON><PERSON> vam je ovih potrebnih za kit?"}, "variants": {"header": "<PERSON><PERSON><PERSON><PERSON>", "subHeadingTitle": "<PERSON>, ovo je proizvod sa varijantama", "subHeadingDescription": "<PERSON>da je <PERSON>, k<PERSON><PERSON><PERSON>emo zadanu varijantu za vas", "optionTitle": {"placeholder": "Veličina"}, "optionValues": {"placeholder": "Mala, Srednja, Velika"}, "productVariants": {"label": "Varijante proizvoda", "hint": "<PERSON><PERSON><PERSON> rang će uticati na redoslijed varijanti na vašoj prodavnici.", "alert": "Dodajte opcije da biste kreirali varijante.", "tip": "Varijante koje nisu označene neće biti kreirane. Uvijek možete kreirati i uređivati varijante kasnije, ali ovaj popis odgovara varijacijama u vašim opcijama proizvoda."}, "productOptions": {"label": "Opcije proizvoda", "hint": "Definišite opcije za proizvod, npr. boja, veličina, itd."}}, "successToast": "Proizvod {{title}} je usp<PERSON><PERSON><PERSON> k<PERSON>."}, "export": {"header": "Izvoz liste proizvoda", "description": "Izvezite listu proizvoda u CSV fajl.", "success": {"title": "Obrađujemo vaš izvoz", "description": "Izvoz podataka može potrajati nekoliko minuta. Obavijestićemo vas kada završimo."}, "filters": {"title": "<PERSON><PERSON><PERSON>", "description": "Primijenite filtere u preglednoj tabli da prilagodite ovaj prikaz"}, "columns": {"title": "<PERSON><PERSON>", "description": "Prilagodite izvezene podatke prema specifičnim potrebama"}}, "import": {"header": "Uvoz liste proizvoda", "uploadLabel": "Uvoz proizvoda", "uploadHint": "Prevucite i ispustite CSV fajl ili kliknite da biste otpremili", "description": "Uvezite proizvode pružanjem CSV fajla u unaprijed definisanom formatu", "template": {"title": "Niste sigurni kako da rasporedite svoju listu?", "description": "Preuzmite šablon u nastavku da biste bili sigurni da pratite ispravan format."}, "upload": {"title": "Otpremite CSV fajl", "description": "Kroz uvoz možete dodati ili ažurirati proizvode. Za ažuriranje postojećih proizvoda morate koristiti postojeći handle i ID, za ažuriranje postojećih varijanti morate koristiti postojeći ID. Bit ćete zatraženi za potvrdu prije nego što uvezemo proizvode.", "preprocessing": "Obrada...", "productsToCreate": "Proizvodi će biti kreirani", "productsToUpdate": "Proizvodi će biti ažurirani"}, "success": {"title": "Obrađujemo vaš uvoz", "description": "Uvoz podataka može potrajati neko vrijeme. Obavijestićemo vas kada završimo."}}, "deleteWarning": "Uskoro ćete obrisati proizvod {{title}}. Ova radnja se ne može poništiti.", "variants": {"header": "<PERSON><PERSON><PERSON><PERSON>", "empty": {"heading": "<PERSON><PERSON> varija<PERSON>", "description": "Nema varijanti za prikazivanje."}, "filtered": {"heading": "<PERSON><PERSON> rezultata", "description": "Nema varijanti koje odgovaraju trenutnim filtrima."}}, "attributes": "Atributi", "editAttributes": "Izmjena atributa", "editOptions": "Izmjena opcija", "editPrices": "Izmjena cijena", "media": {"label": "<PERSON><PERSON><PERSON>", "editHint": "Dodajte medije u proizvod kako biste ga prikazali u vašoj prodavnici.", "makeThumbnail": "Napravite thumbnail", "uploadImagesLabel": "Otpremite slike", "uploadImagesHint": "Prevucite i ispustite slike ovde ili kliknite da biste otpremili.", "invalidFileType": "'{{name}}' nije <PERSON> tip fajla. Podržani tipovi fajlova su: {{types}}.", "failedToUpload": "<PERSON><PERSON> uspjelo otpremanje dodanih medija. Molimo pokušajte ponovo.", "deleteWarning_one": "Uskoro ćete obrisati {{count}} sliku. Ova radnja se ne može poništiti.", "deleteWarning_other": "Uskoro ćete obrisati {{count}} slika. Ova radnja se ne može poništiti.", "deleteWarningWithThumbnail_one": "Uskoro ćete obrisati {{count}} sliku uključujući thumbnail. Ova radnja se ne može poništiti.", "deleteWarningWithThumbnail_other": "Uskoro ćete obrisati {{count}} slika uključujući thumbnail. Ova radnja se ne može poništiti.", "thumbnailTooltip": "<PERSON><PERSON><PERSON><PERSON>", "galleryLabel": "<PERSON><PERSON><PERSON>", "downloadImageLabel": "Preuzmite trenutnu sliku", "deleteImageLabel": "Obrišite trenutnu sliku", "emptyState": {"header": "Nema medija još", "description": "Dodajte medije u proizvod kako biste ga prikazali u vašoj prodavnici.", "action": "Dodajte medije"}, "successToast": "Mediji su uspješno ažurirani."}, "discountableHint": "<PERSON><PERSON> je <PERSON>, popusti neće biti primijenjeni na ovaj proizvod.", "noSalesChannels": "<PERSON>ema dostupnosti u prodajnim kanalima", "variantCount_one": "{{count}} varijanta", "variantCount_other": "{{count}} varijante", "deleteVariantWarning": "Uskoro ćete obrisati varijantu {{title}}. Ova radnja se ne može poništiti.", "productStatus": {"draft": "Nacrt", "published": "Objavljeno", "proposed": "Predloženo", "rejected": "Odbijeno"}, "fields": {"title": {"label": "<PERSON><PERSON>", "hint": "Dajte svom proizvodu kratak i jasan naziv.<0/>Preporučena dužina za pretraživače je 50-60 karaktera.", "placeholder": "Zimska jakna"}, "subtitle": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "Topla i udobna"}, "handle": {"label": "<PERSON><PERSON>", "tooltip": "Handle se koristi za referenciranje proizvoda u vašoj prodavnici. <PERSON><PERSON>, handle će biti generisan iz naziva proizvoda.", "placeholder": "zimska-jakna"}, "description": {"label": "Opis", "hint": "Dajte svom proizvodu kratak i jasan opis.<0/>Preporučena dužina za pretraživače je 120-160 karaktera.", "placeholder": "Topla i udobna jakna"}, "discountable": {"label": "Može biti sa popustom", "hint": "<PERSON><PERSON> je <PERSON>, popusti neće biti primijenjeni na ovaj proizvod"}, "shipping_profile": {"label": "<PERSON>il za dos<PERSON>", "hint": "Povežite proizvod sa profilom za dostavu"}, "type": {"label": "Vrsta"}, "collection": {"label": "Kolekcija"}, "categories": {"label": "Kategorije"}, "tags": {"label": "Tagovi"}, "sales_channels": {"label": "<PERSON><PERSON><PERSON><PERSON> kanali", "hint": "Ovaj proizvod će biti dostupan samo u zadatom prodajnom kanalu ako se ne promijeni."}, "countryOrigin": {"label": "Zemlja porijekla"}, "material": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "width": {"label": "<PERSON><PERSON><PERSON>"}, "length": {"label": "Dužina"}, "height": {"label": "<PERSON><PERSON><PERSON>"}, "weight": {"label": "Težina"}, "options": {"label": "Opcije proizvoda", "hint": "Opcije se koriste za definisanje boje, veli<PERSON>ine itd. proizvoda", "add": "<PERSON><PERSON><PERSON>", "optionTitle": "Naziv opcije", "optionTitlePlaceholder": "<PERSON><PERSON>", "variations": "Varijacije (odvojene zarezom)", "variantionsPlaceholder": "Crvena, Plava, Zelena"}, "variants": {"label": "Varijante proizvoda", "hint": "Varijante koje nisu označene neće biti kreirane. Ovo rangiranje će uticati na redoslijed varijanti na vašem frontend-u."}, "mid_code": {"label": "Srednji kod"}, "hs_code": {"label": "HS kod"}}, "variant": {"edit": {"header": "<PERSON><PERSON><PERSON> varija<PERSON>u", "success": "Varijanta proizvoda je uspješno uređena"}, "create": {"header": "<PERSON><PERSON><PERSON> var<PERSON>"}, "deleteWarning": "Jeste li sigurni da želite izbrisati ovu varijantu?", "pricesPagination": "1 - {{current}} od {{total}} cijena", "tableItemAvailable": "{{availableCount}} dostupno", "tableItem_one": "{{availableCount}} dostupno na {{locationCount}} lokaciji", "tableItem_other": "{{availableCount}} dostupno na {{locationCount}} lokacija", "inventory": {"notManaged": "<PERSON><PERSON>", "manageItems": "Upravljaj stavkama inventara", "notManagedDesc": "Inventar nije upravljan za ovu varijantu. Uključite ‘Upravljanje inventarom’ da pratite inventar ove varijante.", "manageKit": "Upravljaj inventarnim kompletom", "navigateToItem": "Idi na stavku inventara", "actions": {"inventoryItems": "Idi na stavku inventara", "inventoryKit": "Prikaži stavke inventara"}, "inventoryKit": "Inventarni komplet", "inventoryKitHint": "Da li ova varijanta sadrži više stavki inventara?", "validation": {"itemId": "Molimo odaberite stavku inventara.", "quantity": "<PERSON><PERSON><PERSON><PERSON> je obavezna. Unesite pozitivan broj."}, "header": "Zalihe i Inventar", "editItemDetails": "<PERSON><PERSON><PERSON> detal<PERSON> stavke", "manageInventoryLabel": "Upravljaj inventarom", "manageInventoryHint": "<PERSON>da je <PERSON>, mi<PERSON><PERSON><PERSON>emo količinu inventara kada se kreiraju narudžbe i povrati.", "allowBackordersLabel": "Dozvoli unaprijed naručivanje", "allowBackordersHint": "<PERSON><PERSON> je <PERSON>, kupci mogu kupiti varijantu čak i ako nema dostupne količine.", "toast": {"levelsBatch": "Nivoi inventara ažurirani.", "update": "Stavka inventara uspješno ažurirana.", "updateLevel": "Nivo inventara uspješno ažuriran.", "itemsManageSuccess": "Stavke inventara uspješno ažurirane."}}}, "options": {"header": "Opcije", "edit": {"header": "<PERSON><PERSON><PERSON> opci<PERSON>", "successToast": "Opcija {{title}} je us<PERSON><PERSON><PERSON><PERSON>."}, "create": {"header": "<PERSON><PERSON><PERSON><PERSON>", "successToast": "Opcija {{title}} je usp<PERSON><PERSON> kre<PERSON>."}, "deleteWarning": "Upravo ćete obrisati opciju proizvoda: {{title}}. Ova akcija se ne može poništiti."}, "organization": {"header": "Organizacija", "edit": {"header": "Uredi organizaciju", "toasts": {"success": "Organizacija {{title}} je us<PERSON><PERSON><PERSON><PERSON>."}}}, "stock": {"heading": "Upravljanje zalihama proizvoda i lokacijama", "description": "Ažurirajte nivoe zaliha za sve varijante proizvoda.", "loading": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ovo može potrajati...", "tooltips": {"alreadyManaged": "<PERSON><PERSON> stavka inventara je već uređiva pod {{title}}.", "alreadyManagedWithSku": "<PERSON><PERSON> stavka inventara je već uređiva pod {{title}} ({{sku}})."}}, "shippingProfile": {"header": "Konfiguracija <PERSON>", "edit": {"header": "Konfiguracija <PERSON>", "toasts": {"success": "Profil dostave za {{title}} je us<PERSON><PERSON><PERSON><PERSON>."}}, "create": {"errors": {"required": "<PERSON>il dostave je o<PERSON>."}}}, "toasts": {"delete": {"success": {"header": "Proizvod je obrisan", "description": "{{title}} je us<PERSON><PERSON><PERSON><PERSON> o<PERSON>."}, "error": {"header": "Brisanje proizvoda nije us<PERSON>lo"}}}}, "collections": {"domain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "Organizujte proizvode u kolekcije.", "createCollection": "K<PERSON>iraj k<PERSON>", "createCollectionHint": "Kreirajte novu kolekciju kako biste organizovali svoje proizvode.", "createSuccess": "Kolekcija je uspješno kreirana.", "editCollection": "<PERSON><PERSON>i k<PERSON>", "handleTooltip": "Handle se koristi za upućivanje na kolekciju na vašoj prodavnici. <PERSON><PERSON>, handle će biti generisan iz naziva kolekcije.", "deleteWarning": "Upravo ćete obrisati kolekciju {{title}}. Ova akcija se ne može poništiti.", "removeSingleProductWarning": "Upravo ćete ukloniti proizvod {{title}} iz kolekcije. Ova akcija se ne može poništiti.", "removeProductsWarning_one": "Upravo ćete ukloniti {{count}} proizvod iz kolekcije. Ova akcija se ne može poništiti.", "removeProductsWarning_other": "Upravo ćete ukloniti {{count}} proizvoda iz kolekcije. Ova akcija se ne može poništiti.", "products": {"list": {"noRecordsMessage": "U kolekciji nema proizvoda."}, "add": {"successToast_one": "Proizvod je uspješno dodat u kolekciju.", "successToast_other": "Proizvodi su uspješno dodati u kolekciju."}, "remove": {"successToast_one": "Proizvod je uspješno uklonjen iz kolekcije.", "successToast_other": "Proizvodi su uspješno uklonjeni iz kolekcije."}}}, "categories": {"domain": "Kategorije", "subtitle": "Organizujte proizvode u kategorije i upravljajte rangiranjem i hijerarhijom tih kategorija.", "create": {"header": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Kreirajte novu kategoriju kako biste organizovali svoje proizvode.", "tabs": {"details": "<PERSON><PERSON><PERSON>", "organize": "<PERSON><PERSON><PERSON><PERSON>"}, "successToast": "<PERSON><PERSON><PERSON> {{name}} je usp<PERSON><PERSON><PERSON> k<PERSON>."}, "edit": {"header": "<PERSON><PERSON><PERSON> ka<PERSON>", "description": "Izmijenite kategoriju kako biste ažurirali njene de<PERSON>je.", "successToast": "Kategorija je usp<PERSON>š<PERSON>."}, "delete": {"confirmation": "Uskoro ćete obrisati kategoriju {{name}}. Ova radnja se ne može poništiti.", "successToast": "<PERSON><PERSON><PERSON> {{name}} je us<PERSON><PERSON><PERSON><PERSON> o<PERSON>."}, "products": {"add": {"disabledTooltip": "Proizvod je već u ovoj kategoriji.", "successToast_one": "Dodan<PERSON> {{count}} proizvod u kategoriju.", "successToast_other": "Dodano {{count}} proizvoda u kategoriju."}, "remove": {"confirmation_one": "Uskoro ćete ukloniti {{count}} proizvod iz kategorije. Ova radnja se ne može poništiti.", "confirmation_other": "Uskoro ćete ukloniti {{count}} proizvoda iz kategorije. Ova radnja se ne može poništiti.", "successToast_one": "Uklonjen {{count}} proizvod iz kategorije.", "successToast_other": "Uklonjeno {{count}} proizvoda iz kategorije."}, "list": {"noRecordsMessage": "U kategoriji nema proizvoda."}}, "organize": {"header": "Organizuj", "action": "<PERSON><PERSON><PERSON>"}, "fields": {"visibility": {"label": "Vidljivost", "internal": "Interno", "public": "Javno"}, "status": {"label": "Status", "active": "Aktivno", "inactive": "Neaktivno"}, "path": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "Prikazivanje pune putanje kategorije."}, "children": {"label": "Djeca"}, "new": {"label": "Novo"}}}, "inventory": {"domain": "Inventar", "subtitle": "Upravljajte svojim inventarskim stavkama", "reserved": "Rezervisano", "available": "Do<PERSON><PERSON><PERSON>", "locationLevels": "Lok<PERSON><PERSON><PERSON>", "associatedVariants": "Povezane varijante", "manageLocations": "Upravljajte lokacijama", "manageLocationQuantity": "Upravljajte količinom na lokaciji", "deleteWarning": "Uskoro ćete obrisati inventarsku stavku. Ova akcija se ne može poništiti.", "editItemDetails": "<PERSON><PERSON><PERSON> detal<PERSON> stavke", "quantityAcrossLocations": "{{quantity}} na {{locations}} lokacija", "create": {"title": "Kreiraj inventarsku stavku", "details": "<PERSON><PERSON><PERSON>", "availability": "Dostupnost", "locations": "Lok<PERSON><PERSON><PERSON>", "attributes": "Atributi", "requiresShipping": "<PERSON><PERSON><PERSON><PERSON>", "requiresShippingHint": "Da li inventarska stavka zahtijeva dostavu?", "successToast": "Inventarska stavka je uspješno kreirana."}, "reservation": {"header": "Rezervacija za {{itemName}}", "editItemDetails": "Uredi rezervaciju", "lineItemId": "ID stavke", "orderID": "ID narudžbe", "description": "Opis", "location": "Lokacija", "inStockAtLocation": "Dostupno na ovoj lokaciji", "availableAtLocation": "Dostupno na ovoj lokaciji", "reservedAtLocation": "Rezervisano na ovoj lokaciji", "reservedAmount": "Količina za rezervaciju", "create": "Kreiraj rezervaciju", "itemToReserve": "Stavka za rezervaciju", "quantityPlaceholder": "Koliko želite rezervisati?", "descriptionPlaceholder": "Kakav tip rezervacije je ovo?", "successToast": "Rezervacija je uspješno kreirana.", "updateSuccessToast": "Rezervacija je uspješno ažurirana.", "deleteSuccessToast": "Rezervacija je uspješno obrisana.", "errors": {"noAvaliableQuantity": "Lokacija skladišta nema dostupnu količinu.", "quantityOutOfRange": "Minimalna količina je 1, a maksimalna količina je {{max}}"}}, "adjustInventory": {"errors": {"stockedQuantity": "Količina na skladištu ne može biti ažurirana na manje od rezervisane količine od {{quantity}}."}}, "toast": {"updateLocations": "Lokacije su uspješno ažurirane.", "updateLevel": "Nivo inventara je uspješno ažuriran.", "updateItem": "Stavka inventara je uspješno ažurirana."}, "stock": {"title": "Ažuriraj nivoe inventara", "description": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ine inventara za odabrane stavke inventara.", "action": "Uredi nivoe skladišta", "placeholder": "<PERSON><PERSON>", "disablePrompt_one": "Spremate se onemogućiti {{count}} nivo lokacije. Ova akcija se ne može poništiti.", "disablePrompt_other": "Spremate se onemogućiti {{count}} nivoa lokacije. Ova akcija se ne može poništiti.", "disabledToggleTooltip": "<PERSON><PERSON> mogu<PERSON>e onemogućiti: očistite dolaznu i/ili rezervisanu količinu prije onemogućavanja.", "successToast": "Nivoi inventara su uspješno ažurirani."}}, "giftCards": {"domain": "Poklon kartice", "editGiftCard": "<PERSON>redi poklon karticu", "createGiftCard": "<PERSON><PERSON><PERSON>j poklon karticu", "createGiftCardHint": "Ručno kreiraj poklon karticu koja se može koristiti kao metoda plačanja u tvojoj prodavnici.", "selectRegionFirst": "Prvo odaberi region", "deleteGiftCardWarning": "Spremate se izbrisati poklon karticu {{code}}. <PERSON>va akcija se ne može poništiti.", "balanceHigherThanValue": "Stan<PERSON> ne može biti veće od početne vrijednosti.", "balanceLowerThanZero": "Stanje ne može biti negativno.", "expiryDateHint": "Zemlje imaju različite zakone o datumima isteka poklon kartica. Provjerite lokalne propise prije postavljanja datuma isteka.", "regionHint": "Promjena regiona poklon kartice također će promijeniti njenu valutu, što može uticati na njen novčani iznos.", "enabledHint": "Odredi da li je poklon kartica omogućena ili onemogućena.", "balance": "<PERSON><PERSON>", "currentBalance": "Trenutno stanje", "initialBalance": "Početno stanje", "personalMessage": "<PERSON><PERSON><PERSON><PERSON> poruka", "recipient": "Primaoc"}, "customers": {"domain": "<PERSON><PERSON><PERSON>", "list": {"noRecordsMessage": "Vaši kupci će se prikazati ovdje."}, "create": {"header": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "hint": "Kreiraj novog kupca i upravljaj njegovim podacima.", "successToast": "<PERSON><PERSON><PERSON> {{email}} je us<PERSON><PERSON><PERSON><PERSON> k<PERSON>."}, "groups": {"label": "Grupe kupaca", "remove": "Jeste li sigurni da želite ukloniti kupca iz grupe \"{{name}}\"?", "removeMany": "Jeste li sigurni da želite ukloniti kupca iz sljedećih grupa kupaca: {{groups}}?", "alreadyAddedTooltip": "Kupac je već u ovoj grupi kupaca.", "list": {"noRecordsMessage": "<PERSON><PERSON><PERSON> kupac ne pripada nijednoj grupi."}, "add": {"success": "<PERSON><PERSON><PERSON> je dodan u: {{groups}}.", "list": {"noRecordsMessage": "Prvo kreirajte grupu kupaca."}}, "removed": {"success": "Kupa<PERSON> je uklonjen iz: {{groups}}.", "list": {"noRecordsMessage": "Prvo kreirajte grupu kupaca."}}}, "edit": {"header": "<PERSON><PERSON><PERSON> k<PERSON>", "emailDisabledTooltip": "Email adresa se ne može promijeniti za registrovane kupce.", "successToast": "<PERSON><PERSON><PERSON> {{email}} je us<PERSON><PERSON><PERSON><PERSON>."}, "delete": {"title": "Obriši kup<PERSON>", "description": "Uskoro ćete obrisati kupca {{email}}. Ova radnja se ne može poništiti.", "successToast": "<PERSON><PERSON><PERSON> {{email}} je us<PERSON><PERSON><PERSON><PERSON> o<PERSON>."}, "fields": {"guest": "Gost", "registered": "Registrovan", "groups": "Grupe"}, "registered": "Registrovan", "guest": "Gost", "hasAccount": "<PERSON><PERSON>", "addresses": {"title": "<PERSON><PERSON><PERSON>", "fields": {"addressName": "<PERSON><PERSON> adrese", "address1": "Adresa 1", "address2": "Adresa 2", "city": "Grad", "province": "<PERSON><PERSON><PERSON><PERSON>", "postalCode": "Poštanski broj", "country": "Država", "phone": "Telefon", "company": "Kompanija", "countryCode": "<PERSON><PERSON>", "provinceCode": "<PERSON><PERSON>"}, "create": {"header": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Kreiraj novu adresu za kupca.", "successToast": "Adresa je uspješno kreirana."}}}, "customerGroups": {"domain": "Grupacije k<PERSON>", "subtitle": "Organizujte kupce u grupe. Grupe mogu imati različite promocije i cijene.", "list": {"empty": {"heading": "Nema grupacija k<PERSON>", "description": "Trenutno nema grupacija kupaca za prikazivanje."}, "filtered": {"heading": "<PERSON><PERSON> rezultata", "description": "Nijedna grupacija kupaca ne odgovara trenutnim kriterijima filtera."}}, "create": {"header": "Kreiraj grupu kupaca", "hint": "Kreirajte novu grupu kupaca kako biste segmentirali svoje kupce.", "successToast": "Grupa kupaca {{name}} je uspješ<PERSON> kre<PERSON>."}, "edit": {"header": "Uredi grupu kupaca", "successToast": "Grupa k<PERSON>aca {{name}} je us<PERSON><PERSON><PERSON><PERSON>."}, "delete": {"title": "Izbriši grupu kupaca", "description": "Uskoro ćete izbrisati grupu kupaca {{name}}. Ova akcija se ne može poništiti.", "successToast": "Grupa kupaca {{name}} je uspješno izbrisana."}, "customers": {"alreadyAddedTooltip": "Kupac je već dodan u grupu.", "add": {"successToast_one": "Kupac je uspješno dodan u grupu.", "successToast_other": "Kupci su uspješno dodani u grupu.", "list": {"noRecordsMessage": "Prvo kreirajte kupca."}}, "remove": {"title_one": "Ukloni kupca", "title_other": "Ukloni kupce", "description_one": "Uskoro ćete ukloniti {{count}} kupca iz grupe kupaca. Ova akcija se ne može poništiti.", "description_other": "Uskoro ćete ukloniti {{count}} kupaca iz grupe kupaca. Ova akcija se ne može poništiti."}, "list": {"noRecordsMessage": "<PERSON>va grupa nema kupaca."}}}, "orders": {"domain": "<PERSON><PERSON>ž<PERSON>", "claim": "Reklamacija", "exchange": "<PERSON><PERSON><PERSON><PERSON>", "return": "<PERSON><PERSON><PERSON>", "cancelWarning": "Uskoro ćete otkazati narudžbu {{id}}. Ova akcija se ne može poništiti.", "orderCanceled": "Narudžba je uspješno otkazana", "onDateFromSalesChannel": "{{date}} iz {{salesChannel}}", "list": {"noRecordsMessage": "Vaše narudžbe će se prikazati ovdje."}, "status": {"not_paid": "<PERSON><PERSON>", "pending": "Na čekanju", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "draft": "Nacrt", "archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "canceled": "<PERSON><PERSON><PERSON><PERSON>", "requires_action": "Zahtijeva akciju"}, "summary": {"requestReturn": "Zatraži povrat", "allocateItems": "<PERSON><PERSON><PERSON><PERSON> artik<PERSON>", "editOrder": "<PERSON>redi na<PERSON>", "editOrderContinue": "Nastavi uređivanje narudžbe", "inventoryKit": "Sa<PERSON>ji se od {{count}}x inventarnih artikala", "itemTotal": "U<PERSON><PERSON><PERSON> artika<PERSON>", "shippingTotal": "<PERSON><PERSON><PERSON><PERSON>", "discountTotal": "Ukupno popust", "taxTotalIncl": "Ukupno porez (uključeno)", "itemSubtotal": "<PERSON><PERSON><PERSON><PERSON><PERSON> artika<PERSON>", "shippingSubtotal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "discountSubtotal": "Međuz<PERSON>j popusta", "taxTotal": "Ukupno porez"}, "transfer": {"title": "Prenos vlasništva", "requestSuccess": "Zahtjev za prenos narudžbe poslan na: {{email}}.", "currentOwner": "Trenutni vlasnik", "newOwner": "Novi vlasnik", "currentOwnerDescription": "Kupac trenutno povezan s ovom narudžbom.", "newOwnerDescription": "Kupac na kojeg se prenosi ova narudžba."}, "payment": {"title": "Uplate", "isReadyToBeCaptured": "<PERSON><PERSON><PERSON> <0/> je spremna za preuzimanje.", "totalPaidByCustomer": "Ukupno plaćeno od strane kupca", "capture": "<PERSON><PERSON><PERSON>lat<PERSON>", "capture_short": "<PERSON><PERSON><PERSON>", "refund": "<PERSON><PERSON><PERSON>", "markAsPaid": "Označi kao pla<PERSON>", "statusLabel": "Status uplate", "statusTitle": "Status uplate", "status": {"notPaid": "<PERSON><PERSON>", "authorized": "Autorizovano", "partiallyAuthorized": "Djelomično autorizovano", "awaiting": "Čekanje", "captured": "<PERSON>uze<PERSON>", "partiallyRefunded": "D<PERSON><PERSON><PERSON><PERSON><PERSON> refundirano", "partiallyCaptured": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "refunded": "Refundirano", "canceled": "<PERSON><PERSON><PERSON><PERSON>", "requiresAction": "Zah<PERSON>jeva radnju"}, "capturePayment": "<PERSON><PERSON>a od {{amount}} će biti preuzeta.", "capturePaymentSuccess": "<PERSON>lata od {{amount}} us<PERSON><PERSON><PERSON><PERSON>ta", "markAsPaidPayment": "<PERSON>lata od {{amount}} će biti označena kao plaćena.", "markAsPaidPaymentSuccess": "Uplata od {{amount}} uspješno označena kao plaćena", "createRefund": "<PERSON><PERSON>iraj povrat", "refundPaymentSuccess": "Pov<PERSON> iz<PERSON> {{amount}} uspješan", "createRefundWrongQuantity": "Količina treba biti broj između 1 i {{number}}", "refundAmount": "Povrat {{ amount }}", "paymentLink": "<PERSON><PERSON><PERSON> link za uplatu za {{ amount }}", "selectPaymentToRefund": "Odaberite uplatu za povrat"}, "edits": {"title": "<PERSON>redi na<PERSON>", "confirm": "Potvrdi uređivanje", "confirmText": "Upravo ćete potvrditi uređivanje narudžbe. Ova radnja se ne može poništiti.", "cancel": "Otkaži uređivanje", "currentItems": "Trenutne stavke", "currentItemsDescription": "Prilagodite količinu stavke ili uklonite.", "addItemsDescription": "Možete dodati nove stavke u narudžbu.", "addItems": "<PERSON><PERSON><PERSON> stav<PERSON>", "amountPaid": "Plaćeni iznos", "newTotal": "Novi ukupni iznos", "differenceDue": "Razlika za plaćanje", "create": "<PERSON>redi na<PERSON>", "currentTotal": "Trenutni ukupni iznos", "noteHint": "Dodajte internu napomenu za uređivanje", "cancelSuccessToast": "Uređivanje narudžbe otkazano", "createSuccessToast": "Zahtjev za uređivanje narudžbe kreiran", "activeChangeError": "Već postoji aktivna izmjena narudžbe (povrat, zahtjev, zamjena itd.). Završite ili otkažite izmjenu prije uređivanja narudžbe.", "panel": {"title": "Zahtjev za uređivanje narudžbe", "titlePending": "Uređivanje narudžbe na čekanju"}, "toast": {"canceledSuccessfully": "Uređivanje narudžbe uspješno otkazano", "confirmedSuccessfully": "Uređivanje narudžbe uspješno potvrđeno"}, "validation": {"quantityLowerThanFulfillment": "Ne može se postaviti količina manja ili jednaka ispunjenoj količini"}}, "edit": {"email": {"title": "Uredi e-mail", "requestSuccess": "E-mail narudžbe je a<PERSON> na {{email}}."}, "shippingAddress": {"title": "<PERSON><PERSON><PERSON> adresu za dos<PERSON>", "requestSuccess": "Adresa za dostavu narudžbe je ažurirana."}, "billingAddress": {"title": "Uredi adresu za naplatu", "requestSuccess": "Adresa za naplatu narudžbe je ažurirana."}}, "returns": {"create": "<PERSON><PERSON>iraj povrat", "confirm": "Potvrdi povrat", "confirmText": "Spremate se potvrditi povrat. Ova radnja se ne može poništiti.", "inbound": "Ulazno", "outbound": "Izlazno", "sendNotification": "Pošalji o<PERSON>", "sendNotificationHint": "Obavijesti kupca o povratu.", "returnTotal": "Ukupno za povrat", "inboundTotal": "Ukupno ulazno", "estDifference": "Procijenjena raz<PERSON>a", "outstandingAmount": "Neplaćeni iznos", "reason": "Razlog", "reasonHint": "Odaberite razlog zbog kojeg kupac želi vratiti artikle.", "note": "Na<PERSON>men<PERSON>", "noInventoryLevel": "Nema inventarskog nivoa", "noInventoryLevelDesc": "Odabrana lokacija nema inventarski nivo za odabrane artikle. Povrat se može zatražiti, ali ne može biti primljen dok se ne kreira inventarski nivo za odabranu lokaciju.", "noteHint": "Možete slobodno pisati ako želite nešto precizirati.", "location": "Lokacija", "locationHint": "Odaberite lokaciju na koju želite vratiti artikle.", "inboundShipping": "Povratna poštarina", "inboundShippingHint": "Odaberite metodu koju ž<PERSON> koristiti.", "returnableQuantityLabel": "Količina za povrat", "refundableAmountLabel": "Iznos za povrat", "returnRequestedInfo": "{{requestedItemsCount}}x art<PERSON><PERSON> z<PERSON><PERSON> za povrat", "returnReceivedInfo": "{{requestedItemsCount}}x artikala primljeno na povrat", "itemReceived": "Art<PERSON><PERSON>", "returnRequested": "<PERSON><PERSON><PERSON>", "damagedItemReceived": "Primljeni oštećeni artikli", "damagedItemsReturned": "{{quantity}}x oš<PERSON>ć<PERSON>h artikala vraćeno", "activeChangeError": "Postoji aktivna promjena narudžbe na ovoj narudžbi. Završite ili odbacite promjenu prije nastavka.", "cancel": {"title": "Otkaži povrat", "description": "Jeste li sigurni da želite otkazati zahtjev za povrat?"}, "placeholders": {"noReturnShippingOptions": {"title": "Nisu pronađene opcije povratne poštarine", "hint": "Nisu kreirane opcije povratne poštarine za ovu lokaciju. Možete ih kreirati na <LinkComponent>Lokacija i Poštarina</LinkComponent>."}, "outboundShippingOptions": {"title": "Nisu pronađene opcije odlazne poštarine", "hint": "Nisu kreirane opcije odlazne poštarine za ovu lokaciju. Možete ih kreirati na <LinkComponent>Lokacija i Poštarina</LinkComponent>."}}, "receive": {"action": "<PERSON><PERSON><PERSON> artikle", "receiveItems": "{{ returnType }} {{ id }}", "restockAll": "Vrati sve artikle na zalihu", "itemsLabel": "Primljeni artik<PERSON>", "title": "P<PERSON>jem artikala za #{{returnId}}", "sendNotificationHint": "Obavijestite kupca o primljenom povratu.", "inventoryWarning": "Napomena: Automatski ćemo prilagoditi nivoe zaliha na osnovu vašeg unosa iznad.", "writeOffInputLabel": "<PERSON><PERSON><PERSON> je artika<PERSON>?", "toast": {"success": "Povrat usp<PERSON> primljen.", "errorLargeValue": "<PERSON><PERSON><PERSON><PERSON> je veća od količine naručenih artikala.", "errorNegativeValue": "Količina ne može biti negativna vrijednost.", "errorLargeDamagedValue": "<PERSON><PERSON>čina oštećenih artikala + količina neoštećenih primljenih artikala premašuje ukupnu količinu artikala u povratu. Molimo smanjite količinu neoštećenih artikala."}}, "toast": {"canceledSuccessfully": "Povrat uspješ<PERSON> otkazan", "confirmedSuccessfully": "Povrat <PERSON><PERSON> potvrđ<PERSON>"}, "panel": {"title": "<PERSON>v<PERSON> pok<PERSON>", "description": "Postoji otvoreni zahtjev za povrat koji treba biti dovršen"}}, "claims": {"create": "Kreiraj reklamaciju", "confirm": "Potvrdi reklamaciju", "confirmText": "Uskoro ćete potvrditi reklamaciju. Ova radnja se ne može poništiti.", "manage": "Upravljaj <PERSON>", "outbound": "Izlazno", "outboundItemAdded": "{{itemsCount}}x do<PERSON>o kroz <PERSON>kla<PERSON>ju", "outboundTotal": "Ukupno izlazno", "outboundShipping": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outboundShippingHint": "Izaberite koju metodu želi<PERSON> k<PERSON>ti.", "refundAmount": "Procijenjena raz<PERSON>a", "activeChangeError": "Postoji aktivna promjena narudžbe na ovoj narudžbi. Molimo završite ili odbacite prethodnu promjenu.", "actions": {"cancelClaim": {"successToast": "Reklamacija je uspješno otkazana."}}, "cancel": {"title": "Otkazivanje reklamacije", "description": "Da li ste sigurni da želite otkazati reklamaciju?"}, "tooltips": {"onlyReturnShippingOptions": "Ova lista će sadržavati samo opcije za povratnu dostavu."}, "toast": {"canceledSuccessfully": "Reklamacija uspješno otkazana", "confirmedSuccessfully": "Reklamacija uspješno potvrđena"}, "panel": {"title": "Reklamacija pokrenuta", "description": "Postoji otvoreni zahtjev za reklamaciju koji treba biti dovršen"}}, "exchanges": {"create": "<PERSON><PERSON><PERSON><PERSON>", "manage": "Upravljaj zamjenom", "confirm": "Potvrdi zamjenu", "confirmText": "Uskoro ćete potvrditi zamjenu. Ova radnja se ne može poništiti.", "outbound": "Izlazno", "outboundItemAdded": "{{itemsCount}}x do<PERSON>o k<PERSON><PERSON>", "outboundTotal": "Ukupno izlazno", "outboundShipping": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outboundShippingHint": "Izaberite koju metodu želi<PERSON> k<PERSON>ti.", "refundAmount": "Procijenjena raz<PERSON>a", "activeChangeError": "Postoji aktivna promjena narudžbe na ovoj narudžbi. Molimo završite ili odbacite prethodnu promjenu.", "actions": {"cancelExchange": {"successToast": "Zamjena je uspješno otkazana."}}, "cancel": {"title": "Otkazivanje zamjene", "description": "Da li ste sigurni da ž<PERSON>te otkazati zamjenu?"}, "tooltips": {"onlyReturnShippingOptions": "Ova lista će sadržavati samo opcije za povratnu dostavu."}, "toast": {"canceledSuccessfully": "Zamjena uspješno otkazana", "confirmedSuccessfully": "Zamjena uspješ<PERSON> potvrđena"}, "panel": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Postoji otvoreni zahtjev za zamjenu koji treba biti dovršen"}}, "reservations": {"allocatedLabel": "Dodijeljeno", "notAllocatedLabel": "<PERSON><PERSON>"}, "allocateItems": {"action": "Do<PERSON><PERSON>li stavke", "title": "Dodijeli stavke narudžbe", "locationDescription": "Izaberite koju lokaciju želite dodijeliti.", "itemsToAllocate": "Stavke za dodjelu", "itemsToAllocateDesc": "Izaberite broj stavki koje želite dodijeliti", "search": "Pretraži stavke", "consistsOf": "Sastoji se od {{num}}x inventarnih stavki", "requires": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{num}} po varijanti", "toast": {"created": "Stavke uspješno dodijeljene"}, "error": {"quantityNotAllocated": "Post<PERSON>je nedodijeljene stavke."}}, "shipment": {"title": "Označi ispunjavanje kao poslano", "trackingNumber": "Broj za praćenje", "addTracking": "Dodaj broj za praćenje", "sendNotification": "Pošaljit<PERSON> obavi<PERSON>", "sendNotificationHint": "Obavijestite kupca o ovoj pošiljci.", "toastCreated": "Pošiljka uspješno kreirana."}, "fulfillment": {"cancelWarning": "Spremate se otkazati ispunjenje. Ova radnja se ne može poništiti.", "markAsDeliveredWarning": "Spremate se označiti ispunjenje kao isporučeno. Ova radnja se ne može poništiti.", "differentOptionSelected": "Odabrana opcija dostave različita je od one koju je odabrao kupac.", "disabledItemTooltip": "Odabrana opcija dostave ne omogućava ispunjenje ovog artikla.", "unfulfilledItems": "Neispunjeni artikli", "statusLabel": "Status ispunjenja", "statusTitle": "Status ispunjenja", "fulfillItems": "Ispuniti artikle", "awaitingFulfillmentBadge": "Čeka na ispunjenje", "requiresShipping": "<PERSON><PERSON><PERSON><PERSON>", "number": "Ispunjenje #{{number}}", "itemsToFulfill": "Artikli za ispunjenje", "create": "<PERSON><PERSON><PERSON><PERSON>", "available": "Do<PERSON><PERSON><PERSON>", "inStock": "Na skladištu", "markAsShipped": "Označi kao otpremljeno", "markAsPickedUp": "<PERSON><PERSON><PERSON><PERSON> kao pre<PERSON>to", "markAsDelivered": "Označi kao is<PERSON>", "itemsToFulfillDesc": "Odaberite artikle i količine za ispunjenje", "locationDescription": "Odaberite lokaciju s koje želite ispuniti artikle.", "sendNotificationHint": "Obavijestite kupce o kreiranom ispunjenju.", "methodDescription": "Odaberite drugu opciju dostave od one koju je odabrao kupac", "error": {"wrongQuantity": "Dostupan je samo jedan artikl za ispunjenje", "wrongQuantity_other": "Količina treba biti broj između 1 i {{number}}", "noItems": "Nema artikala za ispunjenje.", "noShippingOption": "Opcija dostave je obavezna", "noLocation": "Lokacija je obavezna"}, "status": {"notFulfilled": "<PERSON><PERSON>", "partiallyFulfilled": "D<PERSON><PERSON><PERSON><PERSON><PERSON> is<PERSON>n<PERSON>no", "fulfilled": "Ispunjeno", "partiallyShipped": "Djelomično otpremljeno", "shipped": "Otpremljeno", "delivered": "Isporučeno", "partiallyDelivered": "Djelomič<PERSON> isporučeno", "partiallyReturned": "Djelomično vraćeno", "returned": "Vraćeno", "canceled": "<PERSON><PERSON><PERSON><PERSON>", "requiresAction": "Zahtijeva akciju"}, "toast": {"created": "Ispunjenje uspješno kreirano", "canceled": "Ispunjenje uspješno otkazano", "fulfillmentShipped": "Nemoguće otkazati već otpremljeno ispunjenje", "fulfillmentDelivered": "Ispunjenje oz<PERSON>čeno kao isporučeno", "fulfillmentPickedUp": "Ispunjenje <PERSON> kao pre<PERSON>to"}, "trackingLabel": "Praćenje", "shippingFromLabel": "<PERSON><PERSON><PERSON> s", "itemsLabel": "<PERSON><PERSON><PERSON>"}, "refund": {"title": "<PERSON><PERSON>iraj povrat", "sendNotificationHint": "Obavijestite kupce o kreiranom povratu.", "systemPayment": "Sistemska uplata", "systemPaymentDesc": "Jedna ili više vaših uplata je sistenska uplata. Budite svjesni da Medusa ne upravlja obradom uplata i povrata za takve uplate.", "error": {"amountToLarge": "<PERSON><PERSON> mogu<PERSON>e vratiti više od originalnog iznosa narudžbe.", "amountNegative": "<PERSON><PERSON><PERSON> povrata mora biti pozitivan broj.", "reasonRequired": "Molimo odaberite razlog za povrat."}}, "customer": {"contactLabel": "Kontakt", "editEmail": "Uredi email", "transferOwnership": "Prenesi vlasništvo", "editBillingAddress": "Uredi adresu za naplatu", "editShippingAddress": "<PERSON><PERSON><PERSON> adresu za dos<PERSON>"}, "activity": {"header": "Aktivnost", "showMoreActivities_one": "Prikazivanje {{count}} više aktivnosti", "showMoreActivities_other": "Prikazivanje {{count}} više aktivnosti", "comment": {"label": "Komentar", "placeholder": "Ostavite komentar", "addButtonText": "<PERSON><PERSON><PERSON> k<PERSON>", "deleteButtonText": "Izbriši komentar"}, "from": "Od", "to": "Do", "events": {"common": {"toReturn": "Za vraćanje", "toSend": "<PERSON>a slanje"}, "placed": {"title": "Po<PERSON><PERSON><PERSON>avl<PERSON>", "fromSalesChannel": "sa {{salesChannel}}"}, "canceled": {"title": "Porudžbina otkazana"}, "payment": {"awaiting": "Čeka se uplata", "captured": "Uplata p<PERSON>", "canceled": "<PERSON><PERSON><PERSON>", "refunded": "<PERSON><PERSON><PERSON> refundirana"}, "fulfillment": {"created": "<PERSON><PERSON><PERSON>", "canceled": "Ispunjenje <PERSON>zano", "shipped": "Art<PERSON><PERSON> p<PERSON>i", "delivered": "<PERSON><PERSON><PERSON>", "items_one": "{{count}} artikal", "items_other": "{{count}} artikala"}, "return": {"created": "Vraćanje #{{returnId}} zatraženo", "canceled": "Vraćanje #{{returnId}} otkazano", "received": "Vraćanje #{{returnId}} primljeno", "items_one": "{{count}} artikal vraćen", "items_other": "{{count}} art<PERSON><PERSON>"}, "note": {"comment": "Komentar", "byLine": "od {{author}}"}, "claim": {"created": "Zahtjev za reklamaciju #{{claimId}} podnesen", "canceled": "Reklamacija #{{claimId}} otkazana", "itemsInbound": "{{count}} artikal za vraćanje", "itemsOutbound": "{{count}} artikal za slanje"}, "exchange": {"created": "<PERSON><PERSON><PERSON><PERSON>v za zamjenu #{{exchangeId}} podnesen", "canceled": "Zamjena #{{exchangeId}} otkazana", "itemsInbound": "{{count}} artikal za vraćanje", "itemsOutbound": "{{count}} artikal za slanje"}, "edit": {"requested": "Zahtjev za izmjenu narudžbe #{{editId}} podnesen", "confirmed": "<PERSON><PERSON><PERSON><PERSON><PERSON> #{{editId}} potvrđena"}, "transfer": {"requested": "Zahtjev za prijenos narudžbe #{{transferId}} podnesen", "confirmed": "P<PERSON><PERSON><PERSON> #{{transferId}} potvrđen", "declined": "Prijenos narudž<PERSON> #{{transferId}} odbijen"}, "update_order": {"shipping_address": "<PERSON><PERSON><PERSON> za <PERSON>vu <PERSON>", "billing_address": "Adresa za naplatu a<PERSON>", "email": "<PERSON><PERSON>"}}}, "fields": {"displayId": "ID za prikaz", "refundableAmount": "<PERSON>z<PERSON> koji se može refundirati", "returnableQuantity": "Ko<PERSON>čina koja se može vratiti"}}, "draftOrders": {"domain": "Nacrt narudžbi", "deleteWarning": "Uskoro ćete izbrisati nacrt narudžbe {{id}}. Ova radnja se ne može poništiti.", "paymentLinkLabel": "Link za plaćanje", "cartIdLabel": "ID korpe", "markAsPaid": {"label": "Označi kao pla<PERSON>", "warningTitle": "Označi kao pla<PERSON>", "warningDescription": "Uskoro ćete označiti nacrt narudžbe kao plaćen. Ova radnja se ne može poništiti, a kasnije neće biti moguće prikupiti uplatu."}, "status": {"open": "<PERSON><PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "create": {"createDraftOrder": "Kreiraj nacrt narudžbe", "createDraftOrderHint": "Kreirajte novi nacrt narudžbe kako biste upravljali detaljima narudžbe prije nego što bude postavljena.", "chooseRegionHint": "Odaberite region", "existingItemsLabel": "Postojeći artikli", "existingItemsHint": "Dodajte postojeće proizvode u nacrt narudžbe.", "customItemsLabel": "Prilagođeni artik<PERSON>", "customItemsHint": "Dodajte prilagođene artikle u nacrt narudžbe.", "addExistingItemsAction": "Dodajte posto<PERSON>e artikle", "addCustomItemAction": "Dodajte prilagođeni artikl", "noCustomItemsAddedLabel": "Nema još dodanih prilagođenih artikala", "noExistingItemsAddedLabel": "Nema još dodanih postojećih artikala", "chooseRegionTooltip": "Prvo odaberite region", "useExistingCustomerLabel": "Koristite <PERSON> k<PERSON>", "addShippingMethodsAction": "Dodajte metode dostave", "unitPriceOverrideLabel": "Prekoračenje cijene po jedinici", "shippingOptionLabel": "Opcija dostave", "shippingOptionHint": "Odaberite opciju dostave za nacrt narudžbe.", "shippingPriceOverrideLabel": "Prekoračenje cijene dostave", "shippingPriceOverrideHint": "Prekoračite cijenu dostave za nacrt narudžbe.", "sendNotificationLabel": "Pošaljit<PERSON> obavi<PERSON>", "sendNotificationHint": "Pošaljite obavijest kupcu kada je nacrt narudžbe kreiran."}, "validation": {"requiredEmailOrCustomer": "<PERSON>ail ili kupac su obavezni.", "requiredItems": "Potrebno je najmanje jedan artikl.", "invalidEmail": "Email mora biti važeća email adresa."}}, "stockLocations": {"domain": "Lokacije i Dostava", "list": {"description": "Upravljajte lokacijama skladišta i opcijama dostave vašeg dućana."}, "create": {"header": "Kreiraj lokaciju skladišta", "hint": "Lokacija skladišta je fizičko mjesto gdje se proizvodi skladište i odakle se šalju.", "successToast": "<PERSON><PERSON><PERSON> {{name}} je usp<PERSON><PERSON><PERSON> k<PERSON>."}, "edit": {"header": "Uredi lokaciju skladišta", "viewInventory": "<PERSON><PERSON><PERSON> inventar", "successToast": "<PERSON><PERSON><PERSON> {{name}} je us<PERSON><PERSON><PERSON><PERSON>."}, "delete": {"confirmation": "Upravo ćete obrisati lokaciju skladišta \"{{name}}\". Ova radnja se ne može poništiti."}, "fulfillmentProviders": {"header": "Pružatelji usluga ispunjenja", "shippingOptionsTooltip": "Ovaj padajući izbornik će sadržavati samo pružatelje usluga omogućene za ovu lokaciju. Dodajte ih na lokaciju ako je padajući izbornik onemogućen.", "label": "Povezani pružatelji usluga ispunjenja", "connectedTo": "Povezano sa {{count}} od {{total}} pružatelja usluga ispunjenja", "noProviders": "Ova lokacija skladišta nije povezana sa niti jednim pružateljem usluga ispunjenja.", "action": "Poveži pružatelje", "successToast": "Pružatelji usluga ispunjenja za lokaciju skladišta su uspješno ažurirani."}, "fulfillmentSets": {"pickup": {"header": "<PERSON>uzimanje"}, "shipping": {"header": "<PERSON><PERSON><PERSON>"}, "disable": {"confirmation": "Da li ste sigurni da želite onemogućiti \"{{name}}\"? Ovo će obrisati sve povezane zone usluga i opcije dostave, i ne može se poništiti.", "pickup": "Preuzimanje je uspješ<PERSON>.", "shipping": "Dostava je uspješ<PERSON>."}, "enable": {"pickup": "Preuzimanje je uspješno omogućeno.", "shipping": "Dostava je uspješno omogućena."}}, "sidebar": {"header": "Konfiguracija <PERSON>", "shippingProfiles": {"label": "<PERSON><PERSON>", "description": "Grupirajte proizvode prema zahtjevima za dostavu"}}, "salesChannels": {"header": "<PERSON><PERSON><PERSON><PERSON> kanali", "hint": "Upravljajte prodajnim kanalima koji su povezani sa ovom lokacijom.", "label": "<PERSON><PERSON><PERSON> prodajni kanali", "connectedTo": "<PERSON><PERSON><PERSON> sa {{count}} od {{total}} prodajnih kanala", "noChannels": "Lokacija nije povezana s nijednim prodajnim kanalom.", "action": "Poveži prodajne kanale", "successToast": "Prodajni kanali su uspješno ažurirani."}, "pickupOptions": {"edit": {"header": "Uredi opciju <PERSON>iman<PERSON>"}}, "shippingOptions": {"create": {"shipping": {"header": "K<PERSON>iraj opciju dostave za {{zone}}", "hint": "Kreirajte novu opciju dostave kako biste definirali kako se proizvodi šalju s ove lokacije.", "label": "<PERSON><PERSON><PERSON>", "successToast": "<PERSON><PERSON><PERSON> dostave {{name}} je usp<PERSON><PERSON><PERSON> kre<PERSON>."}, "pickup": {"header": "Kreiraj opciju preuzimanja za {{zone}}", "hint": "Kreirajte novu opciju preuzimanja kako biste definirali kako se proizvodi preuzimaju s ove lokacije.", "label": "Opcije <PERSON>", "successToast": "<PERSON><PERSON><PERSON> preuzimanja {{name}} je uspje<PERSON> kre<PERSON>na."}, "returns": {"header": "Kreiraj opciju povrata za {{zone}}", "hint": "Kreirajte novu opciju povrata kako biste definirali kako se proizvodi vraćaju na ovu lokaciju.", "label": "Opcije povrata", "successToast": "Opcija povrata {{name}} je uspješno kre<PERSON>na."}, "tabs": {"details": "<PERSON><PERSON><PERSON>", "prices": "Cijene"}, "action": "<PERSON><PERSON><PERSON><PERSON>"}, "delete": {"confirmation": "Spremni ste za brisanje opcije dostave \"{{name}}\". Ova radnja se ne može poništiti.", "successToast": "<PERSON><PERSON><PERSON> dostave {{name}} je usp<PERSON><PERSON><PERSON> o<PERSON>a."}, "edit": {"header": "Uredi opciju dostave", "action": "<PERSON><PERSON><PERSON> opci<PERSON>", "successToast": "<PERSON><PERSON><PERSON> dostave {{name}} je us<PERSON><PERSON><PERSON><PERSON>."}, "pricing": {"action": "<PERSON>redi cijene"}, "conditionalPrices": {"header": "<PERSON>vjetne cijene za {{name}}", "description": "Upravljajte uvjetnim cijenama za ovu opciju dostave na temelju ukupne cijene artikala u korpi.", "attributes": {"cartItemTotal": "Ukupna cijena artikala u korpi"}, "summaries": {"range": "<PERSON><PERSON> je <0>{{attribute}}</0> iz<PERSON><PERSON><PERSON> <1>{{gte}}</1> i <2>{{lte}}</2>", "greaterThan": "<PERSON><PERSON> je <0>{{attribute}}</0> ≥ <1>{{gte}}</1>", "lessThan": "<PERSON><PERSON> je <0>{{attribute}}</0> ≤ <1>{{lte}}</1>"}, "actions": {"addPrice": "<PERSON><PERSON><PERSON>", "manageConditionalPrices": "Upravljaj uvjetnim cijenama"}, "rules": {"amount": "Cijena opcije dosta<PERSON>", "gte": "Minimalna ukupna cijena artikala u korpi", "lte": "Maksimalna ukupna cijena artikala u korpi"}, "customRules": {"label": "Pravila po mjeri", "tooltip": "<PERSON>va uvjetna cijena ima pravila koja se ne mogu upravljati putem nadzorne ploče.", "eq": "Ukupna cijena artikala u korpi mora biti jednaka", "gt": "Ukupna cijena artikala u korpi mora biti veća od", "lt": "Ukupna cijena artikala u korpi mora biti manja od"}, "errors": {"amountRequired": "Cijena opcije dostave je obavezna", "minOrMaxRequired": "Mora biti navedena barem jedna od minimalne ili maksimalne ukupne cijene artikala u korpi", "minGreaterThanMax": "Minimalna ukupna cijena artikala u korpi mora biti manja ili jednaka maksimalnoj ukupnoj cijeni artikala u korpi", "duplicateAmount": "Cijena opcije dostave mora biti jedinstvena za svaku uvjetnu situaciju", "overlappingConditions": "Uvjeti moraju biti jedinstveni za sva pravila cijena"}}, "fields": {"count": {"shipping_one": "{{count}} op<PERSON><PERSON> dostave", "shipping_other": "{{count}} op<PERSON><PERSON> dostave", "pickup_one": "{{count}} op<PERSON><PERSON>", "pickup_other": "{{count}} op<PERSON><PERSON>", "returns_one": "{{count}} opcija povrata", "returns_other": "{{count}} opci<PERSON> povrata"}, "priceType": {"label": "Vrsta cijene", "options": {"fixed": {"label": "Fiksno", "hint": "Cijena opcije dostave je fiksna i ne mijenja se na temelju sadržaja narudžbe."}, "calculated": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "Cijena opcije dostave izračunava se od strane pružatelja usluga ispunjenja prilikom plaćanja."}}}, "enableInStore": {"label": "Omogući u prodavnici", "hint": "Da li kupci mogu koristiti ovu opciju prilikom naplate."}, "provider": "Pružatelj usluga ispunjenja", "profile": "<PERSON><PERSON> dos<PERSON>", "fulfillmentOption": "Opcija ispunjenja"}}, "serviceZones": {"create": {"headerPickup": "<PERSON><PERSON><PERSON><PERSON> servisnu zonu za preuzimanje sa {{location}}", "headerShipping": "<PERSON><PERSON><PERSON><PERSON> servis<PERSON> zonu za dostavu sa {{location}}", "action": "<PERSON><PERSON><PERSON><PERSON> ser<PERSON> zonu", "successToast": "<PERSON><PERSON>na zona {{name}} je uspješno kre<PERSON>na."}, "edit": {"header": "<PERSON><PERSON><PERSON> servis<PERSON> zonu", "successToast": "<PERSON><PERSON><PERSON> zona {{name}} je us<PERSON><PERSON><PERSON><PERSON>."}, "delete": {"confirmation": "Spremate se da obrišete servisnu zonu \"{{name}}\". Ova akcija se ne može poništiti.", "successToast": "<PERSON><PERSON><PERSON> zona {{name}} je usp<PERSON><PERSON> obri<PERSON>a."}, "manageAreas": {"header": "Upravljaj <PERSON> za {{name}}", "action": "Upravljaj <PERSON>", "label": "Podru<PERSON><PERSON>", "hint": "Odaberite geografska područja koja pokriva servisna zona.", "successToast": "Podru<PERSON><PERSON> za {{name}} su uspješno ažurirana."}, "fields": {"noRecords": "<PERSON>ema servisnih zona kojima možete dodati opcije dostave.", "tip": "Servisna zona je zbirka geografski definiranih područja. Koristi se za ograničavanje dostupnih opcija dostave na definisane lokacije."}}}, "shippingProfile": {"domain": "Shipping Profiles", "subtitle": "Grupirajte proizvode sa sličnim zahtjevima za dostavu u profile.", "create": {"header": "Kreiraj Shipping Profil", "hint": "Kreirajte novi shipping profil da grupišete proizvode sa sličnim zahtjevima za dostavu.", "successToast": "Shipping profil {{name}} je usp<PERSON><PERSON><PERSON> k<PERSON>n."}, "delete": {"title": "Obriši Shipping Profil", "description": "Spremate se da obrišete shipping profil {{name}}. Ova ak<PERSON>ja se ne može poništiti.", "successToast": "Shipping profil {{name}} je us<PERSON><PERSON><PERSON><PERSON> o<PERSON>."}, "tooltip": {"type": "Unesite tip shipping profila, na primjer: Teški, Preveliki, Samo za teret, itd."}}, "taxRegions": {"domain": "Poreske Regije", "list": {"hint": "Upravljajte time šta naplaćujete svojim kupcima kada kupuju iz različitih zemalja i regija."}, "delete": {"confirmation": "Spremate se da obrišete poresku regiju. Ova akcija se ne može poništiti.", "successToast": "Poreska regija je uspješno obrisana."}, "create": {"header": "Kreiraj Poresku Regiju", "hint": "Kreirajte novu poresku regiju kako biste definirali poreske stope za određenu zemlju.", "errors": {"rateIsRequired": "Poreska stopa je obavezna pri kreiranju osnovne poreske stope.", "nameIsRequired": "<PERSON>me je obavezno pri kreiranju osnovne poreske stope."}, "successToast": "Poreska regija je uspješno kreirana."}, "edit": {"successToast": "Poreska regija je uspješno ažurirana."}, "province": {"header": "Provin<PERSON>je", "create": {"header": "Kreiraj Poresku Regiju za Provinciju", "hint": "Kreirajte novu poresku regiju kako biste definirali poreske stope za određenu provinciju."}}, "provider": {"header": "Poreski Pružatelj"}, "state": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "Kreiraj Poresku Regiju za Državu", "hint": "Kreirajte novu poresku regiju kako biste definirali poreske stope za određenu državu."}}, "stateOrTerritory": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "Kreiraj Poresku Regiju za Državu/Teritorij", "hint": "Kreirajte novu poresku regiju kako biste definirali poreske stope za određenu državu/teritorij."}}, "county": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "Kreiraj Poresku Regiju za Okrug", "hint": "Kreirajte novu poresku regiju kako biste definirali poreske stope za određeni okrug."}}, "region": {"header": "Regije", "create": {"header": "Kreiraj Poresku Regiju za Regiju", "hint": "Kreirajte novu poresku regiju kako biste definirali poreske stope za određenu regiju."}}, "department": {"header": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Kreiraj Poresku Regiju za Odjel", "hint": "Kreirajte novu poresku regiju kako biste definirali poreske stope za određeni odjel."}}, "territory": {"header": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Kreiraj Poresku Regiju za Teritorij", "hint": "Kreirajte novu poresku regiju kako biste definirali poreske stope za određeni teritorij."}}, "prefecture": {"header": "Prefekture", "create": {"header": "Kreiraj Poresku Regiju za Prefekturu", "hint": "Kreirajte novu poresku regiju kako biste definirali poreske stope za određenu prefekturu."}}, "district": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "Kreiraj Poresku Regiju za Okrug", "hint": "Kreirajte novu poresku regiju kako biste definirali poreske stope za određeni okrug."}}, "governorate": {"header": "<PERSON><PERSON><PERSON>ati", "create": {"header": "Kreiraj Poresku Regiju za Guvernerat", "hint": "Kreirajte novu poresku regiju kako biste definirali poreske stope za određeni guvernerat."}}, "canton": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "Kreiraj Poresku Regiju za Kanton", "hint": "Kreirajte novu poresku regiju kako biste definirali poreske stope za određeni kanton."}}, "emirate": {"header": "Emirati", "create": {"header": "Kreiraj Poresku Regiju za Emirat", "hint": "Kreirajte novu poresku regiju kako biste definirali poreske stope za određeni emirat."}}, "sublevel": {"header": "Podnivo", "create": {"header": "Kreiraj Poresku Regiju za Podnivo", "hint": "Kreirajte novu poresku regiju kako biste definirali poreske stope za određeni podnivo."}}, "taxOverrides": {"header": "Zamjene", "create": {"header": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Kreirajte poresku stopu koja mijenja zadate poreske stope za odabrane uvjete."}, "edit": {"header": "<PERSON>z<PERSON><PERSON><PERSON>", "hint": "Izmijenite poresku stopu koja mijenja zadate poreske stope za odabrane uvjete."}}, "taxRates": {"create": {"header": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Kreirajte novu poresku stopu kako biste definirali poresku stopu za regiju.", "successToast": "Poreska stopa je uspješno kreirana."}, "edit": {"header": "Izmijeni Poresku Stopu", "hint": "Izmijenite poresku stopu kako biste definirali poresku stopu za regiju.", "successToast": "Poreska stopa je uspješ<PERSON> ažurirana."}, "delete": {"confirmation": "Spremni ste za brisanje poreske stope \"{{name}}\". Ova akcija se ne može poništiti.", "successToast": "Poreska stopa je uspješno izbrisana."}}, "fields": {"isCombinable": {"label": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "hint": "Da li se ova poreska stopa može kombinirati sa standardnom stopom iz poreske regije.", "true": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "false": "<PERSON><PERSON> m<PERSON> komb<PERSON>"}, "defaultTaxRate": {"label": "Standardna poreska stopa", "tooltip": "Standardna poreska stopa za ovu regiju. Primjer je standardna stopa PDV-a za zemlju ili regiju.", "action": "<PERSON><PERSON>iraj standardnu poresku stopu"}, "taxRate": "Poreska stopa", "taxCode": "Poreski kod", "taxProvider": "Poreski pružatelj", "targets": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Odaberite ciljeve na koje će se primjenjivati ova poreska stopa.", "options": {"product": "Proizvodi", "productCollection": "Kolekcije proizvoda", "productTag": "Oznake proizvoda", "productType": "Vrste proizvoda", "customerGroup": "Grupe kupaca"}, "operators": {"in": "u", "on": "na", "and": "i"}, "placeholders": {"product": "Pretraži proizvode", "productCollection": "Pretraži kolekcije proizvoda", "productTag": "Pretraži oznake proizvoda", "productType": "Pretraži vrste proizvoda", "customerGroup": "Pretraži grupe kupaca"}, "tags": {"product": "Proizvod", "productCollection": "Kolekcija proizvoda", "productTag": "Oznaka proizvoda", "productType": "Vrsta proizvoda", "customerGroup": "Grupa kupaca"}, "modal": {"header": "<PERSON><PERSON><PERSON> c<PERSON>"}, "values_one": "{{count}} v<PERSON>jedn<PERSON>", "values_other": "{{count}} v<PERSON>jednosti", "numberOfTargets_one": "{{count}} cilj", "numberOfTargets_other": "{{count}} cil<PERSON><PERSON>", "additionalValues_one": "i {{count}} vi<PERSON><PERSON> v<PERSON>", "additionalValues_other": "i {{count}} vi<PERSON><PERSON> v<PERSON>i", "action": "<PERSON><PERSON><PERSON> cilj"}, "sublevels": {"labels": {"province": "<PERSON><PERSON><PERSON><PERSON>", "state": "Država", "region": "Regija", "stateOrTerritory": "Država/Teritorij", "department": "<PERSON><PERSON><PERSON><PERSON>", "county": "Okrug", "territory": "<PERSON><PERSON><PERSON><PERSON>", "prefecture": "Prefektura", "district": "Distrikt", "governorate": "Guvernerat", "emirate": "Emirat", "canton": "<PERSON><PERSON>", "sublevel": "Podnivo kod"}, "placeholders": {"province": "Odaberite pokrajinu", "state": "Odaberite državu", "region": "Odaberite regiju", "stateOrTerritory": "Odaberite državu/teritorij", "department": "Odaberite odjel", "county": "Odaberite okrug", "territory": "Odaberite teritorij", "prefecture": "Odaberite prefekturu", "district": "Odaberite distrikt", "governorate": "Odaberite guvernerat", "emirate": "Odaberite emirat", "canton": "Odaberite kanton"}, "tooltips": {"sublevel": "Unesite ISO 3166-2 kod za poreznu regiju na podnivoju.", "notPartOfCountry": "{{province}} očigledno nije dio {{country}}. Molimo provjerite da li je ovo ispravno."}, "alert": {"header": "Podnivo regije su onemogućeni za ovu poreznu regiju", "description": "Podnivo regije su onemogućeni za ovu regiju prema zadanim postavkama. Možete ih omogućiti kako biste kreirali podnivo regije poput pokrajina, dr<PERSON><PERSON> ili teritorija.", "action": "Omogući podnivo regije"}}, "noDefaultRate": {"label": "<PERSON><PERSON> zadate stope", "tooltip": "Ova porezna regija nema zadatu poreznu stopu. <PERSON><PERSON> post<PERSON> stopa, poput PDV-a u zeml<PERSON>, molimo dodajte je u ovu regiju."}}}, "promotions": {"domain": "Promocije", "sections": {"details": "Detalji promocije"}, "tabs": {"template": "Tip", "details": "<PERSON><PERSON><PERSON>", "campaign": "Kampanja"}, "fields": {"type": "Tip", "value_type": "Tip v<PERSON>dn<PERSON>i", "value": "Vrijednost", "campaign": "Kampanja", "method": "<PERSON><PERSON>", "allocation": "Alokacija", "addCondition": "Do<PERSON>j uvjet", "clearAll": "Očisti sve", "amount": {"tooltip": "Odaberite šifru valute kako biste omogućili postavljanje iznosa"}, "conditions": {"rules": {"title": "Ko može koristiti ovaj kod?", "description": "Koji kupac može koristiti promocijski kod? Promocijski kod mogu koristiti svi kupci ako se ne mijenja."}, "target-rules": {"title": "Na koje artikle će se promocija primijeniti?", "description": "Promocija će se primijeniti na artikle koji zadovoljavaju sljedeće uvjete."}, "buy-rules": {"title": "Šta mora biti u košarici da bi se otključala promocija?", "description": "<PERSON><PERSON> ovi uvjeti budu zadovoljeni, omogućit ćemo promociju na ciljnim artiklima."}}}, "tooltips": {"campaignType": "Šifra valute mora biti odabrana u promociji da biste postavili budžet za potrošnju."}, "errors": {"requiredField": "Obavezno polje", "promotionTabError": "Ispravite greške na kartici promocije prije nego što nastavite"}, "toasts": {"promotionCreateSuccess": "Promocija ({{code}}) je uspješno kreirana."}, "create": {}, "edit": {"title": "<PERSON>redi de<PERSON>je promocije", "rules": {"title": "Uredi uvjete upotrebe"}, "target-rules": {"title": "Uredi uvjete za artikle"}, "buy-rules": {"title": "Uredi uvjete za kupovinu"}}, "campaign": {"header": "Kampanja", "edit": {"header": "<PERSON><PERSON><PERSON>", "successToast": "Kampanja promocije je uspješno ažurirana."}, "actions": {"goToCampaign": "Idite na kampanju"}}, "campaign_currency": {"tooltip": "Ovo je valuta promocije. Promijenite je na kartici Detalji."}, "form": {"required": "Obavezno", "and": "I", "selectAttribute": "Odaberite atribut", "campaign": {"existing": {"title": "Postojeća kampanja", "description": "Dodajte promociju postojećoj kampanji.", "placeholder": {"title": "<PERSON><PERSON> kampanja", "desc": "Možete kreirati novu kako biste pratili više promocija i postavili budžetske limite."}}, "new": {"title": "Nova kampanja", "description": "Kreirajte novu kampanju za ovu promociju."}, "none": {"title": "Bez kampanje", "description": "Nastavite bez povezivanja promocije s kampanjom"}}, "status": {"label": "Status", "draft": {"title": "Nacrt", "description": "Ku<PERSON><PERSON> još uvijek neće moći koristiti kod"}, "active": {"title": "Aktivan", "description": "<PERSON><PERSON><PERSON> će moći koristiti kod"}, "inactive": {"title": "Neaktivan", "description": "Ku<PERSON><PERSON> više neće moći koristiti kod"}}, "method": {"label": "<PERSON><PERSON>", "code": {"title": "Promocijski kod", "description": "Kupci moraju unijeti ovaj kod prilikom plačanja"}, "automatic": {"title": "Automatski", "description": "Ku<PERSON><PERSON> će vidjeti ovu promociju prilikom plačanja"}}, "max_quantity": {"title": "Maksimalna količina", "description": "Maksimalna količina proizvoda na koju se ova promocija primjenjuje."}, "type": {"standard": {"title": "Standardno", "description": "Standardna promocija"}, "buyget": {"title": "Kupi i dobiješ", "description": "Ku<PERSON> X, dobiješ Y promocija"}}, "allocation": {"each": {"title": "Po svakom", "description": "Primjenjuje vrijednost na svaki proizvod"}, "across": {"title": "Na sve", "description": "Primjenjuje vrijednost na sve proizvode"}}, "code": {"title": "Kod", "description": "Kod koji će vaši kupci unijeti prilikom plačanja."}, "value": {"title": "Vrijednost promocije"}, "value_type": {"fixed": {"title": "Fiksni iznos", "description": "<PERSON>z<PERSON> koji će biti popust. npr. 100"}, "percentage": {"title": "Postotak", "description": "Postotak koji će biti popust od iznosa. npr. 8%"}}}, "deleteWarning": "Spremate se obrisati promociju {{code}}. Ova radnja se ne može poništiti.", "createPromotionTitle": "Kreiraj promociju", "type": "Tip promocije", "conditions": {"add": "<PERSON><PERSON><PERSON>", "list": {"noRecordsMessage": "Dodajte uslov kako biste ograničili na koje stavke se promocija primjenjuje."}}}, "campaigns": {"domain": "Kampanje", "details": "<PERSON><PERSON><PERSON>", "status": {"active": "Aktivno", "expired": "<PERSON><PERSON><PERSON><PERSON>", "scheduled": "<PERSON><PERSON><PERSON>"}, "delete": {"title": "Jeste li sigurni?", "description": "Spremate se obrisati kampanju '{{name}}'. <PERSON>va radnja se ne može poništiti.", "successToast": "Kampan<PERSON> '{{name}}' je us<PERSON><PERSON><PERSON><PERSON> k<PERSON>."}, "edit": {"header": "<PERSON><PERSON><PERSON>", "description": "Uredite de<PERSON><PERSON> kamp<PERSON>.", "successToast": "Kampan<PERSON> '{{name}}' je us<PERSON><PERSON><PERSON><PERSON>."}, "configuration": {"header": "Konfiguracija", "edit": {"header": "Uredi konfiguraciju kampanje", "description": "Uredite konfiguraciju kampanje.", "successToast": "Konfiguracija kampanje je uspješno až<PERSON>rana."}}, "create": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Kreirajte promotivnu kampanju.", "hint": "Kreirajte promotivnu kampanju.", "header": "<PERSON><PERSON><PERSON><PERSON>", "successToast": "Kampan<PERSON> '{{name}}' je us<PERSON><PERSON><PERSON><PERSON> k<PERSON>."}, "fields": {"name": "<PERSON><PERSON>", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start_date": "<PERSON>tum <PERSON>", "end_date": "<PERSON><PERSON>", "total_spend": "Potrošeni budžet", "total_used": "Iskorišteni budžet", "budget_limit": "<PERSON><PERSON> b<PERSON>", "campaign_id": {"hint": "Samo kampanje sa istim valutnim kodom kao promocija su prikazane na ovom popisu."}}, "budget": {"create": {"hint": "Kreirajte budžet za kampanju.", "header": "Budžet kampanje"}, "details": "Budžet kampanje", "fields": {"type": "Tip", "currency": "Valuta", "limit": "Limit", "used": "Iskorišteno"}, "type": {"spend": {"title": "Potrošnja", "description": "Postavite limit na ukupni iznos popusta za sve upotrebe promocije."}, "usage": {"title": "Upotreba", "description": "Postavite limit na broj puta koliko se promocija može iskoristiti."}}, "edit": {"header": "<PERSON>redi budž<PERSON> kampanje"}}, "promotions": {"remove": {"title": "Ukloni promociju iz kampanje", "description": "Uskoro ćete ukloniti {{count}} promociju/promocije iz kampanje. Ova akcija se ne može poništiti."}, "alreadyAdded": "Ova promocija je već dodana u kampanju.", "alreadyAddedDiffCampaign": "<PERSON>va promocija je već dodana u drugu kampanju ({{name}}).", "currencyMismatch": "Valuta promocije i kampanje se ne poklapaju.", "toast": {"success": "Us<PERSON><PERSON><PERSON><PERSON> ste dodali {{count}} promociju/promocije u kampanju"}, "add": {"list": {"noRecordsMessage": "Prvo kreirajte promociju."}}, "list": {"noRecordsMessage": "Nema promocija u kampanji."}}, "deleteCampaignWarning": "Uskoro ćete obrisati kampanju {{name}}. <PERSON>va ak<PERSON>ja se ne može poništiti.", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "Cjenovni popisi", "subtitle": "Kreirajte prodajne ili izmijenite cijene za specifične uvjete.", "delete": {"confirmation": "Uskoro ćete obrisati cjenovni popis \"{{title}}\". Ova akcija se ne može poništiti.", "successToast": "<PERSON><PERSON><PERSON><PERSON> popis \"{{title}}\" je us<PERSON><PERSON><PERSON><PERSON> o<PERSON>."}, "create": {"header": "Kreiraj cjenovni popis", "subheader": "Kreirajte novi cjenovni popis za upravljanje cijenama vaših proizvoda.", "tabs": {"details": "<PERSON><PERSON><PERSON>", "products": "Proizvodi", "prices": "Cijene"}, "successToast": "<PERSON><PERSON><PERSON><PERSON> popis {{title}} je usp<PERSON><PERSON><PERSON> k<PERSON>n.", "products": {"list": {"noRecordsMessage": "Prvo kreirajte proizvod."}}}, "edit": {"header": "Izmijeni cjenovni popis", "successToast": "<PERSON><PERSON><PERSON><PERSON> popis {{title}} je us<PERSON><PERSON><PERSON><PERSON>."}, "configuration": {"header": "Konfiguracija", "edit": {"header": "Izmijeni konfiguraciju cjenovnog popisa", "description": "Izmijeni konfiguraciju cjenovnog popisa.", "successToast": "Konfiguracija cjenovnog popisa je uspješno a<PERSON>."}}, "products": {"header": "Proizvodi", "actions": {"addProducts": "Dodaj proizvode", "editPrices": "Izmijeni cijene"}, "delete": {"confirmation_one": "Upravo ćete obrisati cijene za {{count}} proizvod u cjenovnom popisu. Ova radnja se ne može poništiti.", "confirmation_other": "Upravo ćete obrisati cijene za {{count}} proizvoda u cjenovnom popisu. Ova radnja se ne može poništiti.", "successToast_one": "Cijene za {{count}} proizvod su uspješno obrisane.", "successToast_other": "Cijene za {{count}} proizvoda su uspješno obrisane."}, "add": {"successToast": "Cijene su uspješno dodane u cjenovni popis."}, "edit": {"successToast": "Cijene su uspješno ažurirane."}}, "fields": {"priceOverrides": {"label": "Prekidi cijena", "header": "Prekidi cijena"}, "status": {"label": "Status", "options": {"active": "Aktivan", "draft": "Nacrt", "expired": "<PERSON><PERSON><PERSON><PERSON>", "scheduled": "<PERSON><PERSON><PERSON>"}}, "type": {"label": "Tip", "hint": "Odaberite tip cjenovnika koji želite kreirati.", "options": {"sale": {"label": "Akcija", "description": "Cijene na akciji su privremene promjene cijena za proizvode."}, "override": {"label": "Prekid", "description": "Prekidi se obično koriste za kreiranje cijena specifičnih za kupce."}}}, "startsAt": {"label": "Cjenovnik ima datum početka?", "hint": "Zakazivanje cjenovnika da se aktivira u budućnosti."}, "endsAt": {"label": "Cjenovnik ima datum isteka?", "hint": "Zakazivanje cjenovnika da se deaktivira u budućnosti."}, "customerAvailability": {"header": "Odaberite grupe kupaca", "label": "Dostupnost kupaca", "hint": "Odaberite koje grupe kupaca će imati pristup cjenovniku.", "placeholder": "Pretraži grupe kupaca", "attribute": "Grupe kupaca"}}}, "profile": {"domain": "Profil", "manageYourProfileDetails": "Upravljajte detaljima vašeg profila.", "fields": {"languageLabel": "<PERSON><PERSON><PERSON>", "usageInsightsLabel": "Uvid u korištenje"}, "edit": {"header": "<PERSON><PERSON><PERSON> profil", "languageHint": "Jezik koji želite koristiti u administratorskoj nadzornoj ploči. Ovo neće promijeniti jezik vašeg trgovine.", "languagePlaceholder": "Odaberite jezik", "usageInsightsHint": "Podijelite uvide u korištenje i pomozite nam poboljšati Medusu. Više o tome što prikupljamo i kako to koristimo možete pročitati u našoj <0>dokumentaciji</0>."}, "toast": {"edit": "Promjene na profilu su spremljene"}}, "users": {"domain": "<PERSON><PERSON><PERSON><PERSON>", "editUser": "<PERSON><PERSON><PERSON>", "inviteUser": "Pozovi korisnika", "inviteUserHint": "Pozovite novog korisnika u vašu trgovinu.", "sendInvite": "Pošaljite pozivnicu", "pendingInvites": "Pozivnice na čekanju", "deleteInviteWarning": "Spremate se izbrisati pozivnicu za {{email}}. Ova akcija se ne može poništiti.", "resendInvite": "Ponovno pošaljite pozivnicu", "copyInviteLink": "Kopirajte poveznicu za pozivnicu", "expiredOnDate": "<PERSON><PERSON><PERSON><PERSON> dana {{date}}", "validFromUntil": "<PERSON><PERSON> od <0>{{from}}</0> - <1>{{until}}</1>", "acceptedOnDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dana {{date}}", "inviteStatus": {"accepted": "P<PERSON>h<PERSON>ć<PERSON>", "pending": "Na čekanju", "expired": "<PERSON><PERSON><PERSON><PERSON>"}, "roles": {"admin": "Administrator", "developer": "Programer", "member": "Član"}, "list": {"empty": {"heading": "<PERSON><PERSON>", "description": "<PERSON>da korisnik bude pozvan, pojavit će se ovdje."}, "filtered": {"heading": "<PERSON><PERSON> rezultata", "description": "<PERSON>ema korisnika koji odgovaraju trenutnim kriterijima pretraživanja."}}, "deleteUserWarning": "Spremate se izbrisati korisnika {{name}}. <PERSON>va ak<PERSON>ja se ne može poništiti.", "deleteUserSuccess": "<PERSON><PERSON><PERSON> {{name}} uspješno izbrisan", "invite": "<PERSON><PERSON><PERSON>"}, "store": {"domain": "Trgovina", "manageYourStoresDetails": "Upravljajte podacima svoje trgovine", "editStore": "<PERSON><PERSON><PERSON>", "defaultCurrency": "Osnovna valuta", "defaultRegion": "Osnovna regija", "defaultSalesChannel": "Osnovni prodajni kanal", "defaultLocation": "Osnovna lokacija", "swapLinkTemplate": "Template za z<PERSON>a", "paymentLinkTemplate": "Template za link za plaćanje", "inviteLinkTemplate": "Template za pozivnicu", "currencies": "Valute", "addCurrencies": "<PERSON><PERSON><PERSON> valute", "enableTaxInclusivePricing": "Omogući cijene sa uključenim porezom", "disableTaxInclusivePricing": "Onemogući cijene sa uključenim porezom", "removeCurrencyWarning_one": "Spremate se izbrisati {{count}} valutu iz svoje trgovine. Pobrinite se da ste uklonili sve cijene koje koriste ovu valutu prije nego što nastavite.", "removeCurrencyWarning_other": "Spremate se izbrisati {{count}} valuta iz svoje trgovine. Pobrinite se da ste uklonili sve cijene koje koriste ove valute prije nego što nastavite.", "currencyAlreadyAdded": "Valuta je već dodana u vašu trgovinu.", "edit": {"header": "<PERSON><PERSON><PERSON>"}, "toast": {"update": "<PERSON><PERSON><PERSON> us<PERSON>", "currenciesUpdated": "Valute uspje<PERSON>", "currenciesRemoved": "Valute uspješno uklonjene iz trgovine", "updatedTaxInclusivitySuccessfully": "Porez sa uključenim cijenama uspješno ažuriran"}}, "regions": {"domain": "Regije", "subtitle": "Regija je područje u kojem prodajete proizvode. Može obuhvatiti više zemalja, i ima različite porezne stope, provajdere i valute.", "createRegion": "<PERSON><PERSON><PERSON><PERSON> regiju", "createRegionHint": "Upravljajte poreznim stopama i provajderima za skup zemalja.", "addCountries": "<PERSON><PERSON><PERSON>lje", "editRegion": "<PERSON><PERSON><PERSON> regiju", "countriesHint": "Dodajte zemlje koje su uključene u ovu regiju.", "deleteRegionWarning": "Spremate se izbrisati regiju {{name}}. <PERSON>va radnja se ne može poništiti.", "removeCountriesWarning_one": "Spremate se izbrisati {{count}} zemlju iz regije. Ova radnja se ne može poništiti.", "removeCountriesWarning_other": "Spremate se izbrisati {{count}} zemalja iz regije. Ova radnja se ne može poništiti.", "removeCountryWarning": "Spremate se izbrisati zemlju {{name}} iz regije. Ova radnja se ne može poništiti.", "automaticTaxesHint": "<PERSON><PERSON> je <PERSON><PERSON>, porezi će biti izračunati samo na temelju adrese za dostavu prilikom naplate.", "taxInclusiveHint": "<PERSON>da je <PERSON>, cijene u regiji će uključivati porez.", "providersHint": "Dodajte koji su provajderi plačanja dostupni u ovoj regiji.", "shippingOptions": "<PERSON><PERSON><PERSON>", "deleteShippingOptionWarning": "Spremate se izbrisati opciju dostave {{name}}. Ova radnja se ne može poništiti.", "return": "Povratak", "outbound": "Izlazni", "priceType": "Tip cijene", "flatRate": "Fiksna cijena", "calculated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "list": {"noRecordsMessage": "Kreirajte regiju za područja u kojima prodajete."}, "toast": {"delete": "Regija je uspješno izbrisana", "edit": "Izmjene regije su sačuvane", "create": "Regija je uspješno kreirana", "countries": "Zemlje u regiji su uspješno ažurirane"}, "shippingOption": {"createShippingOption": "K<PERSON>iraj opciju <PERSON>tave", "createShippingOptionHint": "Kreiraj novu opciju dostave za regiju.", "editShippingOption": "Uredi opciju dostave", "fulfillmentMethod": "<PERSON><PERSON><PERSON>", "type": {"outbound": "<PERSON><PERSON><PERSON>", "outboundHint": "Koristite ovu opciju ako kreirate opciju dostave za slanje proizvoda kupcu.", "return": "<PERSON><PERSON><PERSON>", "returnHint": "Koristite ovu opciju ako kreirate opciju dostave za povrat proizvoda od kupca."}, "priceType": {"label": "Tip cijene", "flatRate": "Fiksna cijena", "calculated": "Izračunata"}, "availability": {"adminOnly": "Samo za administraciju", "adminOnlyHint": "<PERSON><PERSON> je <PERSON><PERSON><PERSON><PERSON><PERSON>, opcija dostave će biti dostupna samo u administracijskom panelu, a ne na prodajnom mjestu."}, "taxInclusiveHint": "<PERSON><PERSON> je <PERSON>, cijena opcije dostave će uključivati porez.", "requirements": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "Odredite zahtjeve za opciju dostave."}}}, "taxes": {"domain": "<PERSON><PERSON><PERSON>", "domainDescription": "Upravljajte vašom poreznom regijom", "countries": {"taxCountriesHint": "Porezne postavke se primjenjuju na navedene zemlje."}, "settings": {"editTaxSettings": "<PERSON><PERSON>i porez<PERSON>", "taxProviderLabel": "Porezni pružatelj", "systemTaxProviderLabel": "Sistemski porezni pružatelj", "calculateTaxesAutomaticallyLabel": "Automatski izračunaj poreze", "calculateTaxesAutomaticallyHint": "Kada je o<PERSON><PERSON><PERSON><PERSON>, porezne stope će se automatski izračunavati i primjenjivati na korpe. Kada je onemo<PERSON><PERSON><PERSON>, porezi se moraju ručno izračunati prilikom naplate. Ručni porezi se preporučuju za korištenje s pružateljima poreznih usluga treće strane.", "applyTaxesOnGiftCardsLabel": "Primijeni poreze na poklon kartice", "applyTaxesOnGiftCardsHint": "<PERSON>da je <PERSON><PERSON><PERSON><PERSON><PERSON>, porezi će se primjenjivati na poklon kartice prilikom naplate. U nekim <PERSON>, porezni propisi zahtijevaju primjenu poreza na poklon kartice prilikom kupovine.", "defaultTaxRateLabel": "Podrazumijevana porezna stopa", "defaultTaxCodeLabel": "Podrazumijevani porezni kod"}, "defaultRate": {"sectionTitle": "Podrazumijevana porezna stopa"}, "taxRate": {"sectionTitle": "<PERSON><PERSON><PERSON>", "createTaxRate": "<PERSON><PERSON><PERSON><PERSON>", "createTaxRateHint": "Kreirajte novu poreznu stopu za regiju.", "deleteRateDescription": "Vi ćete obrisati poreznu stopu {{name}}. <PERSON>va ak<PERSON>ja se ne može poništiti.", "editRateAction": "<PERSON><PERSON><PERSON> stopu", "editOverridesAction": "<PERSON><PERSON><PERSON>", "editOverridesTitle": "Uredi Izuzetke Porezne Stope", "editOverridesHint": "Odredite izuzetke za poreznu stopu.", "deleteTaxRateWarning": "Vi ćete obrisati poreznu stopu {{name}}. <PERSON>va ak<PERSON>ja se ne može poništiti.", "productOverridesLabel": "Izuzetci za proizvode", "productOverridesHint": "Odredite izuzetke za poreznu stopu po proizvodima.", "addProductOverridesAction": "Dodaj izuzetke za proizvode", "productTypeOverridesLabel": "Izuzetci po tipu proizvoda", "productTypeOverridesHint": "Odredite izuzetke za poreznu stopu po tipu proizvoda.", "addProductTypeOverridesAction": "Dodaj izuzetke po tipu proizvoda", "shippingOptionOverridesLabel": "Izuzetci za opcije dostave", "shippingOptionOverridesHint": "Odredite izuzetke za poreznu stopu po opcijama dostave.", "addShippingOptionOverridesAction": "Dodaj izuzetke za opcije dostave", "productOverridesHeader": "Proizvodi", "productTypeOverridesHeader": "Tipovi Proizvoda", "shippingOptionOverridesHeader": "Opcije <PERSON>"}}, "locations": {"domain": "Lok<PERSON><PERSON><PERSON>", "editLocation": "Uredi lokaciju", "addSalesChannels": "Dodaj prodajne kanale", "noLocationsFound": "Nijedna lokacija nije pronađena", "selectLocations": "Odaberite lokacije koje imaju artikal na zalihi.", "deleteLocationWarning": "Vi ćete obrisati lokaciju {{name}}. Ova akcija se ne može poništiti.", "removeSalesChannelsWarning_one": "Vi ćete ukloniti {{count}} prodajni kanal sa lokacije.", "removeSalesChannelsWarning_other": "Vi ćete ukloniti {{count}} prodajna kanala sa lokacije.", "toast": {"create": "Lokacija uspješno kreirana", "update": "Lokacija <PERSON>", "removeChannel": "Prodajni kanal uspješno uklonjen"}}, "reservations": {"domain": "Rezervacije", "subtitle": "Upravljajte rezervisanom količinom inventarskih artikala.", "deleteWarning": "Vi ćete obrisati rezervaciju. Ova akcija se ne može poništiti."}, "salesChannels": {"domain": "<PERSON><PERSON><PERSON><PERSON> kanali", "subtitle": "Upravljajte online i offline kanalima na kojima prodajete proizvode.", "list": {"empty": {"heading": "Nisu pronađeni prodajni kanali", "description": "Kada se kreira prodajni kanal, pojavit će se ovdje."}, "filtered": {"heading": "<PERSON><PERSON> rezultata", "description": "<PERSON><PERSON><PERSON> prodajni kanal ne odgovara trenutnim kriterijima filtera."}}, "createSalesChannel": "<PERSON><PERSON><PERSON><PERSON> prodajni kanal", "createSalesChannelHint": "Kreirajte novi prodajni kanal za prodaju svojih proizvoda.", "enabledHint": "Odre<PERSON><PERSON> da li je prodajni kanal omogućen.", "removeProductsWarning_one": "Uklonit ćete {{count}} proizvod iz {{sales_channel}}.", "removeProductsWarning_other": "Uklonit ćete {{count}} proizvoda iz {{sales_channel}}.", "addProducts": "Dodaj proizvode", "editSalesChannel": "<PERSON><PERSON><PERSON> prodajni kanal", "productAlreadyAdded": "Proizvod je već dodan u prodajni kanal.", "deleteSalesChannelWarning": "Uklonit ćete prodajni kanal {{name}}. Ova akcija se ne može poništiti.", "toast": {"create": "Prodajni kanal uspješno kreiran", "update": "Prodajni kanal uspješno ažuriran", "delete": "Prodajni kanal uspješno obrisan"}, "tooltip": {"cannotDeleteDefault": "<PERSON><PERSON> mogu<PERSON>e obrisati zadani prodajni kanal"}, "products": {"list": {"noRecordsMessage": "Nema proizvoda u prodajnom kanalu."}, "add": {"list": {"noRecordsMessage": "Prvo kreirajte proizvod."}}}}, "apiKeyManagement": {"domain": {"publishable": "Javni API ključevi", "secret": "Tajni API ključevi"}, "subtitle": {"publishable": "Upravljajte API ključevima koji se koriste u prodajnom interfejsu kako biste ograničili opseg zahtjeva na određene prodajne kanale.", "secret": "Upravljajte API ključevima koji se koriste za autentifikaciju administrativnih korisnika u administrativnim aplikacijama."}, "status": {"active": "Aktivan", "revoked": "<PERSON><PERSON><PERSON><PERSON>"}, "type": {"publishable": "<PERSON><PERSON><PERSON>", "secret": "<PERSON><PERSON><PERSON>"}, "create": {"createPublishableHeader": "Kreiraj javni API ključ", "createPublishableHint": "Kreirajte novi javni API ključ kako biste ograničili opseg zahtjeva na određene prodajne kanale.", "createSecretHeader": "Kreiraj tajni API ključ", "createSecretHint": "Kreirajte novi tajni API ključ za pristup Medusa API-ju kao autentifikovani administrativni korisnik.", "secretKeyCreatedHeader": "<PERSON><PERSON><PERSON> kl<PERSON> kreiran", "secretKeyCreatedHint": "<PERSON>aš novi tajni ključ je generisan. Kopirajte ga i sigurno sačuvajte sada. <PERSON><PERSON> je jed<PERSON> put kada će biti prikazan.", "copySecretTokenSuccess": "Tajni ključ je uspješno kopiran u međuspremnik.", "copySecretTokenFailure": "Neuspješno kopiranje tajnog ključa u međuspremnik.", "successToast": "API ključ je uspješno kreiran."}, "edit": {"header": "Uredi API ključ", "description": "Uredite naziv API ključa.", "successToast": "API ključ {{title}} je usp<PERSON><PERSON><PERSON>."}, "salesChannels": {"title": "Dodaj prodajne kanale", "description": "Dodajte prodajne kanale kojima će API ključ biti ograničen.", "successToast_one": "{{count}} prodajni kanal je uspješno dodat API ključu.", "successToast_other": "{{count}} prodajna kanala su uspješno dodana API ključu.", "alreadyAddedTooltip": "Prodajni kanal je već dodat API ključu.", "list": {"noRecordsMessage": "Nema prodajnih kanala u opsegu javnog API ključa."}}, "delete": {"warning": "Uskoro ćete obrisati API ključ {{title}}. Ova radnja se ne može poništiti.", "successToast": "API ključ {{title}} je us<PERSON><PERSON><PERSON><PERSON> o<PERSON>."}, "revoke": {"warning": "Uskoro ćete opozvati API ključ {{title}}. Ova radnja se ne može poništiti.", "successToast": "API ključ {{title}} je usp<PERSON><PERSON><PERSON> opozvan."}, "addSalesChannels": {"list": {"noRecordsMessage": "Prvo kreirajte prodajni kanal."}}, "removeSalesChannel": {"warning": "Uskoro ćete ukloniti prodajni kanal {{name}} iz API ključa. Ova radnja se ne može poništiti.", "warningBatch_one": "Uskoro ćete ukloniti {{count}} prodajni kanal iz API ključa. Ova radnja se ne može poništiti.", "warningBatch_other": "Uskoro ćete ukloniti {{count}} prodajnih kanala iz API ključa. Ova radnja se ne može poništiti.", "successToast": "Prodajni kanal je uspješno uklonjen iz API ključa.", "successToastBatch_one": "{{count}} prodajni kanal je uspješno uklonjen iz API ključa.", "successToastBatch_other": "{{count}} prodajnih kanala je uspješno uklonjeno iz API ključa."}, "actions": {"revoke": "Opozovi API ključ", "copy": "Kopiraj API ključ", "copySuccessToast": "API ključ je kopiran u međuspremnik."}, "table": {"lastUsedAtHeader": "Posljednja upotreba", "createdAtHeader": "Datum opoziva"}, "fields": {"lastUsedAtLabel": "Posljednja upotreba", "revokedByLabel": "Opozvao", "revokedAtLabel": "Datum opoziva", "createdByLabel": "<PERSON><PERSON><PERSON><PERSON>"}}, "returnReasons": {"domain": "Razlozi za povrat", "subtitle": "Upravljajte razlozima za vraćene artikle.", "calloutHint": "Upravljajte razlozima za kategorizaciju povrata.", "editReason": "Uredi razlog povrata", "create": {"header": "Dodaj razlog povrata", "subtitle": "Odredite najčešće razloge za povrate.", "hint": "Kreirajte novi razlog povrata za kategorizaciju povrata.", "successToast": "Razlog povrata {{label}} je usp<PERSON><PERSON><PERSON> k<PERSON>."}, "edit": {"header": "Uredi razlog povrata", "subtitle": "Uredi vrijednost razloga povrata.", "successToast": "Razlog povrata {{label}} je usp<PERSON><PERSON><PERSON>."}, "delete": {"confirmation": "Upravo ćete obrisati razlog povrata \"{{label}}\". Ova akcija se ne može poništiti.", "successToast": "Razlog povrata \"{{label}}\" je us<PERSON><PERSON><PERSON><PERSON> o<PERSON>."}, "fields": {"value": {"label": "Vrijednost", "placeholder": "pogrešna_veličina", "tooltip": "Vrijednost treba biti jedinstveni identifikator za razlog povrata."}, "label": {"label": "Oznaka", "placeholder": "Pogrešna veli<PERSON>"}, "description": {"label": "Opis", "placeholder": "<PERSON><PERSON><PERSON> je primio pogreš<PERSON> ve<PERSON>u"}}}, "login": {"forgotPassword": "<PERSON><PERSON><PERSON><PERSON> ste lozinku? - <0><PERSON><PERSON><PERSON><PERSON></0>", "title": "Dobrodošli u Medusa", "hint": "Prijavite se da biste pristupili korisničkom području"}, "invite": {"title": "Dobrodošli u Medusa", "hint": "Kreirajte svoj račun ispod", "backToLogin": "Povratak na prijavu", "createAccount": "<PERSON><PERSON>iraj rač<PERSON>", "alreadyHaveAccount": "<PERSON><PERSON><PERSON> imate račun? - <0>Prijavite se</0>", "emailTooltip": "Vaš email se ne može mijenjati. Ako želite koristiti drugi email, mora se poslati nova pozivnica.", "invalidInvite": "Pozivnica je nevažeća ili je istekao rok.", "successTitle": "<PERSON><PERSON><PERSON> ra<PERSON>un je registriran", "successHint": "Počnite odmah s <PERSON><PERSON>.", "successAction": "Započnite Medusa Admin", "invalidTokenTitle": "<PERSON><PERSON>š poziv<PERSON> je nevaž<PERSON>ći", "invalidTokenHint": "Pokušajte zatražiti novi pozivni link.", "passwordMismatch": "Lozinke se ne podudaraju", "toast": {"accepted": "Pozivnica uspješno prihvaćena"}}, "resetPassword": {"title": "Ponovno postavljan<PERSON> lo<PERSON>", "hint": "Unesite svoj email ispod i poslat ćemo vam upute kako da resetirate svoju lozinku.", "email": "Email", "sendResetInstructions": "Pošaljite upute za resetiranje", "backToLogin": "<0>Povratak na prijavu</0>", "newPasswordHint": "Odaberite novu lozinku ispod.", "invalidTokenTitle": "Vaš <PERSON> za resetiranje je nevažeći", "invalidTokenHint": "Pokušajte zatražiti novi link za resetiranje.", "expiredTokenTitle": "Vaš <PERSON> za resetiranje je istekao", "goToResetPassword": "Idite na Resetiranje lozinke", "resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "newPassword": "Nova lozinka", "repeatNewPassword": "Ponovite novu lozinku", "tokenExpiresIn": "Token ističe za <0>{{time}}</0> minuta", "successfulRequestTitle": "Uspješno smo vam poslali email", "successfulRequest": "Poslali smo vam email koji možete koristiti za resetiranje lozinke. Provjerite svoju spam folderu ako ga niste primili nakon nekoliko minuta.", "successfulResetTitle": "Resetiranje lozinke uspješno", "successfulReset": "Molimo vas da se prijavite na stranici za prijavu.", "passwordMismatch": "Lozinke se ne podudaraju", "invalidLinkTitle": "V<PERSON>š link za resetiranje je nevažeći", "invalidLinkHint": "Pokušajte ponovo resetirati lozinku."}, "workflowExecutions": {"domain": "<PERSON><PERSON><PERSON>", "subtitle": "Pregledajte i pratite izvršenja radnih tokova u vašoj Medusa aplikaciji.", "transactionIdLabel": "ID transakcije", "workflowIdLabel": "ID radnog toka", "progressLabel": "Napredak", "stepsCompletedLabel_one": "{{completed}} od {{count}} koraka", "stepsCompletedLabel_other": "{{completed}} od {{count}} koraka", "list": {"noRecordsMessage": "<PERSON>š uvi<PERSON>k nisu izvršeni radni tokovi."}, "history": {"sectionTitle": "Historija", "runningState": "Izvršava se...", "awaitingState": "Čeka se", "failedState": "Neuspješno", "skippedState": "Preskočeno", "skippedFailureState": "Preskočeno (Greška)", "definitionLabel": "Definicija", "outputLabel": "<PERSON><PERSON><PERSON>z", "compensateInputLabel": "Komp<PERSON><PERSON><PERSON>", "revertedLabel": "Po<PERSON>čeno", "errorLabel": "Greška"}, "state": {"done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "failed": "Neuspješno", "reverted": "Po<PERSON>čeno", "invoking": "Pozivanje", "compensating": "Kompenzacija", "notStarted": "<PERSON><PERSON>"}, "transaction": {"state": {"waitingToCompensate": "Čeka se na kompenzaciju"}}, "step": {"state": {"skipped": "Preskočeno", "skippedFailure": "Preskočeno (Greška)", "dormant": "Neaktivan", "timeout": "Isteklo vrijeme"}}}, "productTypes": {"domain": "Tipovi proizvoda", "subtitle": "Organizujte svoje proizvode u tipove.", "create": {"header": "Kreirajte tip proizvoda", "hint": "Kreirajte novi tip proizvoda za kategorizaciju svojih proizvoda.", "successToast": "Tip proizvoda {{value}} je usp<PERSON><PERSON><PERSON> k<PERSON>."}, "edit": {"header": "Uredite tip proizvoda", "successToast": "Tip proizvoda {{value}} je us<PERSON><PERSON><PERSON><PERSON>."}, "delete": {"confirmation": "Uskoro ćete obrisati tip proizvoda \"{{value}}\". Ova radnja se ne može poništiti.", "successToast": "Tip proizvoda \"{{value}}\" je us<PERSON><PERSON><PERSON><PERSON> o<PERSON>."}, "fields": {"value": "Vrijednost"}}, "productTags": {"domain": "Oznake proizvoda", "create": {"header": "Kreirajte oznaku proizvoda", "subtitle": "Kreirajte novu oznaku proizvoda za kategorizaciju svojih proizvoda.", "successToast": "Oznaka proizvoda {{value}} je uspješno kre<PERSON>na."}, "edit": {"header": "Uredite oznaku proizvoda", "subtitle": "Uredite vrijednost oznake proizvoda.", "successToast": "Oznaka proizvoda {{value}} je usp<PERSON><PERSON><PERSON>."}, "delete": {"confirmation": "Uskoro ćete obrisati oznaku proizvoda {{value}}. Ova radnja se ne može poništiti.", "successToast": "Oznaka proizvoda {{value}} je uspje<PERSON> obri<PERSON>a."}, "fields": {"value": "Vrijednost"}}, "notifications": {"domain": "Obavijesti", "emptyState": {"title": "<PERSON><PERSON>", "description": "Nemate obavijesti u ovom trenutku, ali kada ih budete imali, bit će ovdje."}, "accessibility": {"description": "Obavijesti o Medusa aktivnostima bit će ovdje prikazane."}}, "errors": {"serverError": "Greška na serveru - Pokušajte ponovo kasnije.", "invalidCredentials": "Pogrešan email ili lo<PERSON>ka"}, "statuses": {"scheduled": "<PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON><PERSON>", "active": "Aktivno", "inactive": "Neaktivno", "draft": "Skica", "enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "labels": {"productVariant": "Varijanta proizvoda", "prices": "Cijene", "available": "Do<PERSON><PERSON><PERSON>", "inStock": "Na skladištu", "added": "Dodano", "removed": "Uklonjeno", "from": "Od", "to": "Do", "beaware": "Pazite", "loading": "Učitavanje"}, "fields": {"amount": "Iznos", "refundAmount": "<PERSON>znos p<PERSON>", "name": "Ime", "default": "Zadano", "lastName": "Prezime", "firstName": "Ime", "title": "Titula", "customTitle": "Prilagođena titula", "manageInventory": "Upravljaj inventarom", "inventoryKit": "<PERSON><PERSON> inventarni komplet", "inventoryItems": "Inventarski artikli", "inventoryItem": "Inventarski artikal", "requiredQuantity": "Potrebna količina", "description": "Opis", "email": "Email", "password": "Lozinka", "repeatPassword": "Ponovite lozinku", "confirmPassword": "Potvrdite lozinku", "newPassword": "Nova lozinka", "repeatNewPassword": "Ponovite novu lozinku", "categories": "Kategorije", "shippingMethod": "Metoda dostave", "configurations": "Konfiguracije", "conditions": "<PERSON><PERSON><PERSON>", "category": "Kategorija", "collection": "Kolekcija", "discountable": "Sniživo", "handle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON>", "by": "Autor", "item": "Artik<PERSON>", "qty": "količina", "limit": "Limit", "tags": "Oznake", "type": "Tip", "reason": "Razlog", "none": "nema", "all": "svi", "search": "Pretraži", "percentage": "Postotak", "sales_channels": "<PERSON><PERSON><PERSON><PERSON> kanali", "customer_groups": "Grupe kupaca", "product_tags": "Oznake proizvoda", "product_types": "Vrste proizvoda", "product_collections": "Kolekcije proizvoda", "status": "Status", "code": "Kod", "value": "Value", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dynamic": "<PERSON><PERSON><PERSON><PERSON>", "normal": "Normalno", "years": "<PERSON><PERSON>", "months": "<PERSON><PERSON><PERSON><PERSON>", "days": "<PERSON>", "hours": "<PERSON><PERSON>", "minutes": "Minute", "totalRedemptions": "Ukupne otkupnine", "countries": "Zemlje", "paymentProviders": "Pružatelji plaćanja", "refundReason": "Razlog za povrat", "fulfillmentProviders": "Pružatelji ispunjenja", "fulfillmentProvider": "Pružatelj ispunjenja", "providers": "Pružatelji", "availability": "Dostupnost", "inventory": "Inventar", "optional": "Opcionalno", "note": "Na<PERSON>men<PERSON>", "automaticTaxes": "Automatski porezi", "taxInclusivePricing": "Cijena uključuje p<PERSON>z", "currency": "Valuta", "address": "<PERSON><PERSON><PERSON>", "address2": "<PERSON>, apartman, itd.", "city": "Grad", "postalCode": "Poštanski broj", "country": "Zemlja", "state": "Država", "province": "<PERSON><PERSON><PERSON><PERSON>", "company": "Firma", "phone": "Telefon", "metadata": "<PERSON><PERSON><PERSON><PERSON>", "selectCountry": "Odaberite zemlju", "products": "Proizvodi", "variants": "<PERSON><PERSON><PERSON><PERSON>", "orders": "<PERSON><PERSON>ž<PERSON>", "account": "<PERSON><PERSON><PERSON>", "total": "Ukupno narudžbe", "paidTotal": "Ukupno zabilježeno", "totalExclTax": "Ukupno bez poreza", "subtotal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shipping": "<PERSON><PERSON><PERSON>", "outboundShipping": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnShipping": "Dostava povrata", "tax": "<PERSON><PERSON>", "created": "<PERSON><PERSON><PERSON><PERSON>", "key": "K<PERSON><PERSON>č", "customer": "Ku<PERSON><PERSON>", "date": "Datum", "order": "<PERSON><PERSON><PERSON><PERSON>", "fulfillment": "Ispuna", "provider": "Pružatelj", "payment": "P<PERSON>ć<PERSON><PERSON>", "items": "Stavke", "salesChannel": "<PERSON><PERSON><PERSON><PERSON> kanal", "region": "Regija", "discount": "Popust", "role": "<PERSON><PERSON><PERSON>", "sent": "Poslano", "salesChannels": "<PERSON><PERSON><PERSON><PERSON> kanali", "product": "Proizvod", "createdAt": "<PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON><PERSON>", "revokedAt": "Poništeno u", "true": "Tačno", "false": "Netačno", "giftCard": "Poklon kartica", "tag": "Oznaka", "dateIssued": "<PERSON><PERSON>", "issuedDate": "<PERSON><PERSON>", "expiryDate": "<PERSON><PERSON> isteka", "price": "Cijena", "priceTemplate": "Cijena {{regionOrCurrency}}", "height": "<PERSON><PERSON><PERSON>", "width": "<PERSON><PERSON><PERSON>", "length": "Dužina", "weight": "Težina", "midCode": "MID kod", "hsCode": "HS kod", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "Količina u skladištu", "barcode": "Bar kod", "countryOfOrigin": "Zemlja porijekla", "material": "<PERSON><PERSON><PERSON><PERSON>", "thumbnail": "Mini slika", "sku": "SKU", "managedInventory": "Upravljano skladi<PERSON>", "allowBackorder": "Dozvoli narudžbe na čekanju", "inStock": "Na skladištu", "location": "Lokacija", "quantity": "<PERSON><PERSON><PERSON><PERSON>", "variant": "<PERSON><PERSON><PERSON><PERSON>", "id": "ID", "parent": "<PERSON><PERSON><PERSON><PERSON>", "minSubtotal": "<PERSON><PERSON>", "maxSubtotal": "<PERSON><PERSON>", "shippingProfile": "<PERSON><PERSON> dos<PERSON>", "summary": "Sažetak", "details": "<PERSON><PERSON><PERSON>", "label": "Oznaka", "rate": "Stopa", "requiresShipping": "Potreban je prijevoz", "unitPrice": "Jedinična cijena", "startDate": "<PERSON>tum <PERSON>", "endDate": "<PERSON><PERSON>", "draft": "Skica", "values": "Vrijednosti"}, "quotes": {"domain": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "subtitle": "Upravljajte ponudama i prijedlozima kupaca", "noQuotes": "<PERSON><PERSON> pro<PERSON> pon<PERSON>", "noQuotesDescription": "Trenutno nema ponuda. Kreirajte jednu iz trgovine.", "table": {"id": "ID ponude", "customer": "Ku<PERSON><PERSON>", "status": "Status", "company": "Firma", "amount": "Iznos", "createdAt": "<PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON><PERSON>"}, "status": {"pending_merchant": "Čeka trgovca", "pending_customer": "Čeka kupca", "merchant_rejected": "Odbačeno od trgovca", "customer_rejected": "Odbačeno od kupca", "accepted": "P<PERSON>h<PERSON>ć<PERSON>", "unknown": "Nepoznato"}, "actions": {"sendQuote": "Poša<PERSON><PERSON> p<PERSON>", "rejectQuote": "Odbaci ponudu", "viewOrder": "Pogledaj <PERSON>"}, "details": {"header": "<PERSON><PERSON><PERSON> p<PERSON>", "quoteSummary": "Sažetak ponude", "customer": "Ku<PERSON><PERSON>", "company": "Firma", "items": "Stavke", "total": "Ukupno", "subtotal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shipping": "<PERSON><PERSON><PERSON>", "tax": "<PERSON><PERSON>", "discounts": "<PERSON><PERSON><PERSON>", "originalTotal": "<PERSON><PERSON>up<PERSON>", "quoteTotal": "Ukupno ponude", "messages": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON><PERSON>", "sendMessage": "Pošalji poruku", "send": "Pošalji", "pickQuoteItem": "Odaberi stavku ponude", "selectQuoteItem": "Odaberite stavku ponude za komentar", "selectItem": "Odaberi stavku", "manage": "Upravljaj", "phone": "Telefon", "spendingLimit": "<PERSON>it tro<PERSON>", "name": "Ime", "manageQuote": "Upravl<PERSON><PERSON> pon<PERSON>m", "noItems": "Nema stavki u ovoj ponudi", "noMessages": "<PERSON><PERSON> poruka za ovu ponudu"}, "items": {"title": "Proizvod", "quantity": "<PERSON><PERSON><PERSON><PERSON>", "unitPrice": "Jedinična cijena", "total": "Ukupno"}, "messages": {"admin": "Administrator", "customer": "Ku<PERSON><PERSON>", "placeholder": "Unesite svoju poruku ovdje..."}, "filters": {"status": "Filtriraj po statusu"}, "confirmations": {"sendTitle": "Poša<PERSON><PERSON> p<PERSON>", "sendDescription": "Jeste li sigurni da želi<PERSON> poslati ovu ponudu kupcu?", "rejectTitle": "Odbaci ponudu", "rejectDescription": "Jeste li sigurni da želite odbaciti ovu ponudu?"}, "acceptance": {"message": "Ponuda je prihvaćena"}, "toasts": {"sendSuccess": "Ponuda je uspješ<PERSON> pos<PERSON> k<PERSON>cu", "sendError": "Neuspješno slanje ponude", "rejectSuccess": "Ponuda kupca je uspješno odbačena", "rejectError": "Neuspješno odbacivanje ponude", "messageSuccess": "Poruka je uspješ<PERSON> pos<PERSON> k<PERSON>cu", "messageError": "Neuspješno slanje poruke", "updateSuccess": "Ponuda je uspješ<PERSON>"}, "manage": {"overridePriceHint": "Zamijeni originalnu cijenu za ovu stavku", "updatePrice": "<PERSON><PERSON><PERSON><PERSON>"}}, "companies": {"domain": "Firme", "title": "Firme", "subtitle": "Upravljajte poslovnim odnosima", "noCompanies": "Nema pronađ<PERSON> firmi", "noCompaniesDescription": "Kreirajte svoju prvu firmu za početak.", "notFound": "Firma nije pronađena", "table": {"name": "Ime", "phone": "Telefon", "email": "Email", "address": "<PERSON><PERSON><PERSON>", "employees": "Zap<PERSON><PERSON><PERSON>", "customerGroup": "Grupa kupaca", "actions": "<PERSON><PERSON><PERSON><PERSON>"}, "fields": {"name": "Ime firme", "email": "Email", "phone": "Telefon", "website": "Web stranica", "address": "<PERSON><PERSON><PERSON>", "city": "Grad", "state": "Država", "zip": "Poštanski broj", "zipCode": "Poštanski broj", "country": "Zemlja", "currency": "Valuta", "logoUrl": "URL logotipa", "description": "Opis", "employees": "Zap<PERSON><PERSON><PERSON>", "customerGroup": "Grupa kupaca", "approvalSettings": "Postavke odobrenja"}, "placeholders": {"name": "Unesite ime firme", "email": "Unesite email adresu", "phone": "Unesite broj telefona", "website": "Unesite URL web stranice", "address": "Unesite adresu", "city": "Unesite grad", "state": "Unesite državu", "zip": "Unesite poštanski broj", "logoUrl": "Unesite URL logotipa", "description": "Unesite opis firme", "selectCountry": "Odaberite zemlju", "selectCurrency": "Odaberite valutu"}, "validation": {"nameRequired": "Ime firme je oba<PERSON>no", "emailRequired": "<PERSON><PERSON> je o<PERSON>", "emailInvalid": "Neispravna email adresa", "addressRequired": "<PERSON><PERSON><PERSON> je obavezna", "cityRequired": "<PERSON><PERSON> je oba<PERSON>an", "stateRequired": "Država je obavezna", "zipRequired": "Poštanski broj je oba<PERSON>an"}, "create": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Kreirajte novu firmu za upravljanje poslovnim odnosima.", "submit": "<PERSON><PERSON><PERSON><PERSON>"}, "edit": {"title": "<PERSON><PERSON><PERSON>u", "submit": "<PERSON><PERSON><PERSON><PERSON>"}, "details": {"actions": "<PERSON><PERSON><PERSON><PERSON>"}, "approvals": {"requiresAdminApproval": "Zahtijeva odobrenje administratora", "requiresSalesManagerApproval": "Zahtijeva odobrenje menadžera prodaje", "noApprovalRequired": "Ne zahtijeva odobrenje"}, "deleteWarning": "Ovo će trajno obrisati kompaniju i sve povezane podatke.", "approvalSettings": {"title": "Postavke odobrenja", "requiresAdminApproval": "Zahtijeva odobrenje administratora", "requiresSalesManagerApproval": "Zahtijeva odobrenje menadžera prodaje", "requiresAdminApprovalDesc": "Narudžbe od ove firme zahtijevaju odobrenje administratora prije obrade", "requiresSalesManagerApprovalDesc": "Narudžbe od ove firme zahtijevaju odobrenje menadžera prodaje prije obrade", "updateSuccess": "Postavke odobrenja su uspješno ažurirane", "updateError": "Neuspješno ažuriranje postavki odobrenja"}, "customerGroup": {"title": "Upravljanje grupom kupaca", "hint": "Dodijelite ovu firmu grupi kupaca za primjenu grupnih cijena i dozvola.", "name": "<PERSON><PERSON> grupe kupaca", "groupName": "Grupa kupaca", "actions": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON>", "remove": "Ukloni", "description": "Upravljajte grupama kupaca za ovu firmu", "noGroups": "<PERSON><PERSON> grupa kupaca", "addSuccess": "Firma je uspješno dodana u grupu kupaca", "addError": "Neuspješno dodavanje firme u grupu kupaca", "removeSuccess": "Firma je uspješno uklonjena iz grupe kupaca", "removeError": "Neuspješno uklanjanje firme iz grupe kupaca"}, "actions": {"edit": "<PERSON><PERSON><PERSON>u", "editDetails": "<PERSON><PERSON><PERSON>", "manageCustomerGroup": "Upravljaj grupom kupaca", "approvalSettings": "Postavke odobrenja", "delete": "<PERSON><PERSON><PERSON><PERSON> firmu", "confirmDelete": "Potvrdi brisanje"}, "delete": {"title": "<PERSON><PERSON><PERSON><PERSON> firmu", "description": "Jeste li sigurni da želite obrisati ovu firmu? Ova akcija se ne može poništiti."}, "employees": {"title": "Zap<PERSON><PERSON><PERSON>", "noEmployees": "Nema pronađenih zaposlenih za ovu firmu", "name": "Ime", "email": "Email", "phone": "Telefon", "role": "<PERSON><PERSON><PERSON>", "spendingLimit": "<PERSON>it tro<PERSON>", "admin": "Administrator", "employee": "Zap<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON>", "create": {"title": "K<PERSON>iraj zaposlenog", "success": "Zaposleni je uspješno kreiran", "error": "Neuspješno kreiranje zaposlenog"}, "form": {"details": "Detaljne informacije", "permissions": "Dozvole", "firstName": "Ime", "lastName": "Prezime", "email": "Email", "phone": "Telefon", "spendingLimit": "<PERSON>it tro<PERSON>", "adminAccess": "<PERSON><PERSON> pristup", "isAdmin": "Je administrator", "isAdminDesc": "Dodjeli administratorske privilegije ovom zaposlenom", "isAdminTooltip": "Administratori mogu upravljati postavkama firme i drugim zaposlenima", "firstNamePlaceholder": "Unesite ime", "lastNamePlaceholder": "Unesite prezime", "emailPlaceholder": "Unesite email adresu", "phonePlaceholder": "Unesite broj telefona", "spendingLimitPlaceholder": "Unesite limit trošenja", "save": "Sačuvaj", "saving": "Čuvanje..."}, "delete": {"confirmation": "Jeste li sigurni da želite obrisati ovog zaposlenog?", "success": "Zaposleni je uspješno obrisan"}, "edit": {"title": "<PERSON>redi zaposlenog"}, "toasts": {"updateSuccess": "Zaposleni je uspješno ažuriran", "updateError": "Neuspješno ažuriranje zaposlenog"}}, "toasts": {"createSuccess": "Firma je uspješ<PERSON> kreirana", "createError": "Neuspješno kreiranje firme", "updateSuccess": "<PERSON>rma je usp<PERSON>", "updateError": "Neuspješno ažuriranje firme", "deleteSuccess": "Firma je uspješ<PERSON> obrisana", "deleteError": "Neuspješno brisanje firme"}}, "approvals": {"domain": "Odobrenja", "title": "Odobrenja", "subtitle": "Upravljajte tokovima odobrenja", "noApprovals": "<PERSON><PERSON>", "noApprovalsDescription": "Trenutno nema odobrenja za pregled.", "table": {"id": "ID", "type": "Tip", "company": "Firma", "customer": "Ku<PERSON><PERSON>", "amount": "Iznos", "status": "Status", "createdAt": "<PERSON><PERSON><PERSON><PERSON>"}, "status": {"pending": "Na čekanju", "approved": "Odobreno", "rejected": "Odbačeno", "expired": "<PERSON><PERSON><PERSON><PERSON>", "unknown": "Nepoznato"}, "details": {"header": "<PERSON><PERSON><PERSON>", "summary": "Sažetak odobrenja", "company": "Firma", "customer": "Ku<PERSON><PERSON>", "order": "<PERSON><PERSON><PERSON><PERSON>", "amount": "Iznos", "updatedAt": "<PERSON><PERSON><PERSON><PERSON>", "reason": "Razlog", "actions": "<PERSON><PERSON><PERSON><PERSON>"}, "actions": {"approve": "<PERSON><PERSON><PERSON>", "reject": "<PERSON>d<PERSON><PERSON>", "confirmApprove": "Potvrdi odobrenje", "confirmReject": "Potvrdi odbacivanje", "reasonPlaceholder": "Unesite razlog (opcionalno)..."}, "filters": {"status": "Filtriraj po statusu"}, "toasts": {"approveSuccess": "Uspješno odobreno", "approveError": "Neuspješno odobravanje", "rejectSuccess": "Uspješno odbačeno", "rejectError": "Neuspješno odbacivanje"}}, "dateTime": {"years_one": "<PERSON><PERSON>", "years_other": "<PERSON><PERSON>", "months_one": "<PERSON><PERSON><PERSON><PERSON>", "months_other": "<PERSON><PERSON><PERSON><PERSON>", "weeks_one": "Sedmica", "weeks_other": "Sedmice", "days_one": "<PERSON>", "days_other": "<PERSON>", "hours_one": "Sat", "hours_other": "<PERSON><PERSON>", "minutes_one": "<PERSON>uta", "minutes_other": "Minute", "seconds_one": "<PERSON><PERSON><PERSON>", "seconds_other": "Sekunde"}}